package entity

import (
	"database/sql"
	"time"

	"github.com/google/uuid"
	"repo.nusatek.id/nusatek/payment/domain"
)

type TrxRequestLog struct {
	Id             uuid.UUID      `gorm:"column:id"`
	ResourceType   sql.NullString `gorm:"column:resource_type"`
	ResourceId     sql.NullString `gorm:"column:resource_id"`
	CorrelationId  sql.NullString `gorm:"column:correlation_id"`
	RequestHeaders sql.NullString `gorm:"column:request_headers"`
	RequestMethod  sql.NullString `gorm:"column:request_method"`
	RequestPath    sql.NullString `gorm:"column:request_path"`
	RequestQuery   sql.NullString `gorm:"column:request_query"`
	RequestBody    sql.NullString `gorm:"column:request_body"`
	ResponseStatus sql.NullString `gorm:"column:response_status"`
	ResponseBody   sql.NullString `gorm:"column:response_body"`
	CreatedAt      time.Time      `gorm:"column:created_at"`
}

func (TrxRequestLog) TableName() string {
	return "trx_request_logs"
}

func (t *TrxRequestLog) FromDomain(domain *domain.TrxRequestLog) {
	t.Id, _ = uuid.Parse(domain.Id)
	t.ResourceType = sql.NullString{String: domain.ResourceType, Valid: len(domain.ResourceType) > 0}
	t.ResourceId = sql.NullString{String: domain.ResourceId, Valid: len(domain.ResourceId) > 0}
	t.CorrelationId = sql.NullString{String: domain.CorrelationId, Valid: len(domain.CorrelationId) > 0}
	t.RequestHeaders = sql.NullString{String: domain.RequestHeaders, Valid: len(domain.RequestHeaders) > 0}
	t.RequestMethod = sql.NullString{String: domain.RequestMethod, Valid: len(domain.RequestMethod) > 0}
	t.RequestPath = sql.NullString{String: domain.RequestPath, Valid: len(domain.RequestPath) > 0}
	t.RequestQuery = sql.NullString{String: domain.RequestQuery, Valid: len(domain.RequestQuery) > 0}
	t.RequestBody = sql.NullString{String: domain.RequestBody, Valid: len(domain.RequestBody) > 0}
	t.ResponseStatus = sql.NullString{String: domain.ResponseStatus, Valid: len(domain.ResponseStatus) > 0}
	t.ResponseBody = sql.NullString{String: domain.ResponseBody, Valid: len(domain.ResponseBody) > 0}
	t.CreatedAt = domain.CreatedAt
}
