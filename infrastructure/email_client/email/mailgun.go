package email

import (
	"context"
	"fmt"
	"log"
	"time"

	emailverifier "github.com/AfterShip/email-verifier"
	"github.com/mailgun/mailgun-go/v4"
	"repo.nusatek.id/moaja/backend/libraries/logr"
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email/webhook"
)

type mailgunEmailProvider struct {
	client  mailgun.Mailgun
	webhook mailgun.Mailgun
}

type MailgunAttachment struct {
	Filename string
	Buffer   []byte
}

type MailgunParameters func(msg *mailgun.Message) error

func WithAttachments(attachments []MailgunAttachment) MailgunParameters {
	return func(msg *mailgun.Message) error {
		for _, attachment := range attachments {
			msg.AddBufferAttachment(attachment.Filename, attachment.Buffer)
		}
		return nil
	}
}

func WithCC(cc []string) MailgunParameters {
	return func(msg *mailgun.Message) error {
		for _, c := range cc {
			msg.AddCC(c)
		}
		return nil
	}
}

func WithHtml(html string) MailgunParameters {
	return func(msg *mailgun.Message) error {
		msg.SetHtml(html)
		return nil
	}
}

func WithParameters(parameters interface{}) MailgunParameters {
	return func(msg *mailgun.Message) error {
		err := msg.AddVariable("parameters", parameters)
		if err != nil {
			log.Printf("Error generating parameters")
		}
		return nil
	}
}

func NewMailgunEmailProvider(domain string, authKey, webhookKey string) *mailgunEmailProvider {
	mailgun.Debug = true
	mg := mailgun.NewMailgun(domain, authKey)
	wb := mailgun.NewMailgun(domain, webhookKey)

	return &mailgunEmailProvider{
		client:  mg,
		webhook: wb,
	}
}

func (s *mailgunEmailProvider) Send(ctx context.Context, from, subject, text string, to []string, params ...MailgunParameters) (*Response, error) {
	msg := s.client.NewMessage(from, subject, "", to...)

	msg.SetTrackingClicks(true)
	msg.SetTrackingOpens(true)

	for _, param := range params {
		err := param(msg)
		if err != nil {
			return nil, err
		}
	}

	msg.SetHtml(text)

	ctxCancel, cancel := context.WithTimeout(ctx, time.Second*30)
	defer cancel()

	var err error
	var response Response

	response.Message, response.ID, err = s.client.Send(ctxCancel, msg)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func (s *mailgunEmailProvider) Webhook(ctx context.Context, payload mailgun.WebhookPayload) (*webhook.WebhookResponse, error) {
	verify, err := s.webhook.VerifyWebhookSignature(payload.Signature)
	if err != nil {
		return nil, fmt.Errorf("failed to parse event signature: %w", err)
	}

	if !verify {
		return nil, fmt.Errorf("signature is not valid: %+v", payload.Signature)
	}

	e, err := mailgun.ParseEvent(payload.EventData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse event data: %w", err)
	}

	wr, err := webhook.NewWebhook(e)
	if err != nil {
		return nil, err
	}

	return wr, nil
}

func (s *mailgunEmailProvider) IsValid(email string) bool {
	verifier := emailverifier.NewVerifier()

	ret, err := verifier.Verify(email)
	if err != nil {
		logr.Error("[EMAIL VERIFICATION FAILED]", err, logr.LogField{"email": email})
		return false
	}

	if !ret.HasMxRecords {
		logr.Error("[EMAIL HAS NO MX RECORD]", err, logr.LogField{"email": email})
		return false
	}

	if !ret.Syntax.Valid {
		logr.Error("[EMAIL SYNTAX INVALID]", err, logr.LogField{"email": email})
		return false
	}

	return true
}
