package logutil

import (
	"context"

	"github.com/streadway/amqp"
	"repo.nusatek.id/moaja/backend/libraries/logger"
)

const (
	CorrelationIDKey = "correlationId"
)

func SetAmqpTableFromCtx(ctx context.Context) amqp.Table {
	t := amqp.Table{
		CorrelationIDKey: logger.MustCorrIdVal(ctx),
	}
	trxLogId := GetTrxLogId(ctx)
	if len(trxLogId) != 0 {
		t[trxLogIdKey] = trxLogId
	}

	return t
}

func GetCtxDataFromAmqpTable(h amqp.Table) map[string]interface{} {
	if h == nil {
		h = amqp.Table{}
	}
	ctxData := map[string]interface{}{}
	if h[CorrelationIDKey] != nil {
		ctxData[CorrelationIDKey] = h[CorrelationIDKey]
	}
	if h[trxLogIdKey] != nil {
		ctxData[trxLogIdKey] = h[trxLogIdKey]
	}

	return ctxData
}

func NewCtxFromAmqpTable(ctx context.Context, h amqp.Table) context.Context {
	ctxData := GetCtxDataFromAmqpTable(h)
	corrId, _ := ctxData["correlationId"].(string)
	ctx = logger.MustCorrelationId(ctx, corrId)
	return logger.SetCtxData(ctx, ctxData)
}

const (
	trxLogIdKey         = "trxLogId"
	resourceIdKey       = "resourceId"
	resourceTypeKey     = "resourceType"
	ResourceTypeCashIn  = "cash_in"
	ResourceTypeCashOut = "cash_out"
)

func SetTrxLogId(ctx context.Context, val string) context.Context {
	// ctxData := logger.GetCtxData(ctx)
	// ctxData[trxLogIdKey] = val
	return logger.AppendCtxData(ctx, trxLogIdKey, val)
}

func GetTrxLogId(ctx context.Context) string {
	ctxData := logger.GetCtxData(ctx)
	val, _ := ctxData[trxLogIdKey].(string)
	return val
}

func SetResourceId(ctx context.Context, val string) {
	ctxData := logger.GetCtxData(ctx)
	ctxData[resourceIdKey] = val
}

func GetResourceId(ctx context.Context) string {
	ctxData := logger.GetCtxData(ctx)
	val, _ := ctxData[resourceIdKey].(string)
	return val
}

func SetResourceType(ctx context.Context, val string) {
	ctxData := logger.GetCtxData(ctx)
	ctxData[resourceTypeKey] = val
}

func GetResourceType(ctx context.Context) string {
	ctxData := logger.GetCtxData(ctx)
	val, _ := ctxData[resourceTypeKey].(string)
	return val
}
