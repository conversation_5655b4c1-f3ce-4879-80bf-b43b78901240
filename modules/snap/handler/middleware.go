package handler

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

func (h *Handler) ParseHeader() fiber.Handler {

	return func(c *fiber.Ctx) error {
		ctx := c.UserContext()
		br := snaputil.NewBaseRespCtx(ctx)

		var header = domain.SnapHeaderReq{}
		err := c.ReqHeaderParser(&header)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("err parsing header %v", err))
			err = br.Set(http.StatusInternalServerError, "00", "error parsing header")
			return h.sendErr(c, err)
		}

		ctx = snaputil.SetHeaderReq(ctx, header)
		c.SetUserContext(ctx)

		return c.Next()
	}
}

func (h *Handler) VerifyAsymetricSignature() func(c *fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		var err error
		ctx := c.UserContext()

		br := snaputil.NewBaseRespCtx(ctx)

		// get and set client
		clientId := string(c.Request().Header.Peek("X-CLIENT-KEY"))
		client, err := h.uc.GetOneClient(ctx, clientId)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("get client err %v", err))
			return h.sendErr(c, err)
		}
		ctx = snaputil.SetClient(ctx, client)
		c.SetUserContext(ctx)

		header := snaputil.GetHeaderReq(ctx)
		// validate header
		if len(header.XClientKey) == 0 {
			err = br.Set(http.StatusBadRequest, "02", "Invalid mandatory field [X-CLIENT-KEY]")
			return h.sendErr(c, err)
		}

		if !header.XTimestamp.Valid() {
			err = br.Set(http.StatusBadRequest, "01", "Invalid field format [X-TIMESTAMP]")
			return h.sendErr(c, err)
		}

		if !h.ignoreSignature {
			//Signature Type PKCS
			err = snaputil.VerifyAsymetricSignature(client.PublicKey, client.Id, header.XTimestamp.String(), header.XSignature)
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("error generate token %v", err))
				err = br.Set(http.StatusUnauthorized, "00", "Unauthorized. [Signature]")
				return h.sendErr(c, err)
			}
		}

		return c.Next()
	}
}

func (h *Handler) VerifySymetricSignature() func(c *fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		var err error
		ctx := c.UserContext()

		br := snaputil.NewBaseRespCtx(ctx)

		header := snaputil.GetHeaderReq(ctx)

		// get and set client
		// TODO: TBC where we can get client id
		// clientId := string(c.Request().Header.Peek("X-PARTNER-ID"))
		// client, exist := h.uc.GetSnapClientsConfig(clientId)
		// if !exist {
		// 	logger.Error(ctx, "client not exist")
		// 	err = br.Set(http.StatusUnauthorized, "01", "Unauthorized. [Unknown client]")
		// 	return h.sendErr(c, err)
		// }
		// ctx = snaputil.SetClient(ctx, client)
		// c.SetUserContext(ctx)
		//validate jwt
		accessToken := strings.ReplaceAll(header.Authorization, "Bearer ", "")
		claims, err := h.uc.ValidateToken(ctx, accessToken)
		if err != nil {
			return h.sendErr(c, err)
		}

		client, err := h.uc.GetOneClient(ctx, claims.ID)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("get client err %v", err))
			return h.sendErr(c, err)
		}
		ctx = snaputil.SetClient(ctx, client)
		c.SetUserContext(ctx)

		if !h.ignoreSignature {
			//validate signature
			expSignature, err := snaputil.GenerateSymetricSignature(accessToken, c.Method(), c.Path(), string(c.Request().Body()), header.XTimestamp.String(), client.Secret)
			if err != nil {
				logger.Error(ctx, fmt.Sprintf("generate signature err %v", err))
				err = br.Set(http.StatusInternalServerError, "00", "Generate Signature")
				return h.sendErr(c, err)
			}

			if expSignature != header.XSignature {
				logger.Error(ctx, "signature not valid")
				err = br.Set(http.StatusUnauthorized, "00", "Unauthorized. [Signature]")
				return h.sendErr(c, err)
			}
		}

		return c.Next()
	}
}

// starting middleware
func (h *Handler) InitMd(serviceCode string, timeout time.Duration) func(c *fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		ctx := c.UserContext()
		ctx = snaputil.SetServiceCode(ctx, serviceCode) // set service code
		// ctx, cancel := snaputil.SetTimeout(ctx, timeout)
		// defer cancel() // TODO: check timeout ctx for every operation
		c.SetUserContext(ctx)

		return c.Next()
	}
}
