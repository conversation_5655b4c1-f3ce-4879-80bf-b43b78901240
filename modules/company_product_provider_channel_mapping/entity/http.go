package entity

import (
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
)

type HttpCreateReq struct {
	CompanyPaymentProviderChannelMappingID int     `json:"company_payment_provider_channel_mapping_id" validate:"required"`
	FeeFixValue                            float64 `json:"fee_fix_value"`
	FeePercentage                          float64 `json:"fee_percentage"`
	Status                                 bool    `json:"status"`
}

type HttpCreateBulkReq struct {
	Data []HttpCreateReq `json:"data"`
}

func (r HttpCreateBulkReq) ToDomains() (res []domain.CompanyProductProviderChannelMapping) {
	for _, data := range r.Data {
		res = append(res, domain.CompanyProductProviderChannelMapping{
			CompanyPaymentProviderChannelMappingID: data.CompanyPaymentProviderChannelMappingID,
			FeeFixValue:                            data.FeeFixValue,
			FeePercentage:                          data.FeePercentage,
			Status:                                 data.Status,
		})
	}

	return
}

func FromDomainsGetListWithDetail(data []domain.CompanyProductProviderChannelMapping) (res []HttpGetListWithDetailResp) {
	mapProviders := make(map[string]HttpGetListWithDetailResp)
	mapProviderChannels := make(map[string][]HttpGetListWithDetailChannelResp)

	for _, d := range data {
		mapProviders[d.PaymentProviderCode] = HttpGetListWithDetailResp{
			ID:   d.PaymentProviderID,
			Name: d.PaymentProviderName,
			Code: d.PaymentProviderCode,
		}
		mapProviderChannels[d.PaymentProviderCode] = append(mapProviderChannels[d.PaymentProviderCode], HttpGetListWithDetailChannelResp{
			ID:                                     d.ID,
			ProviderChannelCode:                    d.CompanyProviderChannelMappingProviderCode,
			CompanyPaymentProviderChannelMappingID: d.CompanyPaymentProviderChannelMappingID,
			CompanyPaymentProviderChannelMappingFeeFixValue:   d.CompanyProviderChannelMappingFeeFixValue,
			CompanyPaymentProviderChannelMappingFeePercentage: d.CompanyProviderChannelMappingFeePercentage,
			Code:          d.CompanyProviderChannelMappingCode,
			Name:          d.PaymentChannelName,
			FeeFixValue:   d.FeeFixValue,
			FeePercentage: d.FeePercentage,
			Status:        d.Status,
			CreatedAt:     d.CreatedAt,
			UpdatedAt:     d.UpdatedAt,
		})
	}

	for _, v := range mapProviders {
		v.Channels = mapProviderChannels[v.Code]
		res = append(res, v)
	}

	return
}

type HttpGetListWithDetailResp struct { // provider payment object
	ID       int                                `json:"id"`
	Name     string                             `json:"name"`
	Code     string                             `json:"code"`
	Channels []HttpGetListWithDetailChannelResp `json:"channels"`
}

type HttpGetListWithDetailChannelResp struct { // provider payment channel object
	ID                                                int       `json:"id"`
	ProviderChannelCode                               string    `json:"provider_channel_code"`
	CompanyPaymentProviderChannelMappingID            int       `json:"company_payment_provider_channel_mapping_id"`
	Code                                              string    `json:"code"`
	Name                                              string    `json:"name"`
	CompanyPaymentProviderChannelMappingFeeFixValue   float64   `json:"company_payment_provider_channel_mapping_fee_fix_value"`
	CompanyPaymentProviderChannelMappingFeePercentage float64   `json:"company_payment_provider_channel_mapping_fee_percentage"`
	FeeFixValue                                       float64   `json:"fee_fix_value"`
	FeePercentage                                     float64   `json:"fee_percentage"`
	Status                                            bool      `json:"status"`
	CreatedAt                                         time.Time `json:"created_at"`
	UpdatedAt                                         time.Time `json:"updated_at"`
}

type HttpUpdateReq struct {
	ID            int     `json:"id" validate:"required"`
	FeeFixValue   float64 `json:"fee_fix_value"`
	FeePercentage float64 `json:"fee_percentage"`
	Status        bool    `json:"status"`
}

type HttpUpdateBulkReq struct {
	Data []HttpUpdateReq `json:"data"`
}

func (r HttpUpdateBulkReq) ToDomains() (res []domain.CompanyProductProviderChannelMapping) {
	for _, data := range r.Data {
		res = append(res, domain.CompanyProductProviderChannelMapping{
			ID:            data.ID,
			FeeFixValue:   data.FeeFixValue,
			FeePercentage: data.FeePercentage,
			Status:        data.Status,
		})
	}

	return
}

type HttpPatchDataReq struct {
	CompanyPaymentProviderChannelMappingID int     `json:"company_payment_provider_channel_mapping_id" validate:"required"`
	FeeFixValue                            float64 `json:"fee_fix_value"`
	FeePercentage                          float64 `json:"fee_percentage"`
	Status                                 bool    `json:"status"`
}

type HttpPatchDataBulkReq struct {
	Data []HttpPatchDataReq `json:"data"`
}

func (r HttpPatchDataBulkReq) ToDomains() (res []domain.CompanyProductProviderChannelMapping) {
	for _, data := range r.Data {
		res = append(res, domain.CompanyProductProviderChannelMapping{
			CompanyPaymentProviderChannelMappingID: data.CompanyPaymentProviderChannelMappingID,
			FeeFixValue:                            data.FeeFixValue,
			FeePercentage:                          data.FeePercentage,
			Status:                                 data.Status,
		})
	}

	return
}

type HttpDeleteReq struct {
	ID int `json:"id"`
}

type HttpDeleteBulkReq struct {
	Data []HttpDeleteReq `json:"data"`
}

func (r HttpDeleteBulkReq) ToDomains() (res []domain.CompanyProductProviderChannelMapping) {
	for _, data := range r.Data {
		res = append(res, domain.CompanyProductProviderChannelMapping{
			ID: data.ID,
		})
	}

	return
}

// OPEN API

type HttpOpenAPIGetOneWithDetailResp struct {
	Status        bool    `json:"status"`
	FeeFixValue   float64 `json:"fee_fix_value"`
	FeePercentage float64 `json:"fee_percentage"`
}

type HttpOpenAPICalculateReq struct {
	Amount float64 `json:"amount" validate:"required"`
}

type HttpOpenAPICalculateResp struct {
	Status           bool    `json:"status"`
	FeeFixValue      float64 `json:"fee_fix_value"`
	FeePercentage    float64 `json:"fee_percentage"`
	Amount           float64 `json:"amount"`
	CalculatedAmount float64 `json:"calculated_amount"`
	AdminFee         float64 `json:"admin_fee"`
}
