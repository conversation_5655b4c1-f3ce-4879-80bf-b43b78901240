package usecase

import (
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func generateResponseXfersCashin(res xfers.PaymentResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.Data.ID,
		InvoiceNumber:         res.Data.PaymentDataResp.ReferenceId,
		PaymentProvider:       req.ProviderName,
		Type:                  constants.TrxVirtualAccount,
		Status:                req.CashinTransaction.Status,
		Amount:                req.CashinTransaction.Total,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		ExpiredTime:           res.Data.PaymentDataResp.ExpiredAt.Format(time.RFC3339),
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		AccountNo:             res.Data.PaymentDataResp.PaymentMethodResp.InstructionsResp.AccountNo,
		DisplayName:           res.Data.PaymentDataResp.PaymentMethodResp.InstructionsResp.DisplayName,
	}
}

func generateResponseXenditVACashin(res xendit.VaResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))
	tm, _ := time.Parse(time.RFC3339, res.ExpirationDate)

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.ID,
		InvoiceNumber:         res.ExternalID,
		Amount:                req.CashinTransaction.Total,
		Status:                res.Status,
		PaymentProvider:       req.ProviderName,
		ExpiredTime:           tm.In(loc).Format(time.RFC3339),
		Type:                  constants.TrxVirtualAccount,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		AccountNo:             res.AccountNumber,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		DisplayName:           res.Name,
	}
}

func generateResponseXenditEwallet(req xendit.CreateEwalletResponse, cashIn *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))

	action := entity.EwalletActionResponse{
		DesktopWebCheckoutURL:     req.Actions.DesktopWebCheckoutURL,
		MobileDeeplinkCheckoutURL: req.Actions.MobileDeeplinkCheckoutURL,
		MobileWebCheckoutURL:      req.Actions.MobileWebCheckoutURL,
		QrCheckoutString:          req.Actions.QrCheckoutString,
	}

	fees := cashIn.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    cashIn.CashinTransaction.ID,
		ProviderTransactionID: req.ID,
		InvoiceNumber:         req.ReferenceId,
		Amount:                cashIn.CashinTransaction.Total,
		Status:                cashIn.CashinTransaction.Status,
		PaymentProvider:       cashIn.ProviderName,
		ExpiredTime:           cashIn.ExpiredTime.In(loc).Format(time.RFC3339),
		Type:                  constants.TrxEwallet,
		Bank:                  cashIn.ChannelName,
		ProviderChannelCode:   cashIn.ProviderChannelCode,
		Fees:                  fees,
		CallbackUrl:           req.CallbackUrl,
		Total:                 cashIn.CashinTransaction.Total + fees,
		SuccessRediretUrl:     cashIn.SuccessRedirectUrl,
		Actions:               &action,
	}
}

func generateResponseXenditInvoiceCashin(res xendit.CreateInvoiceResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.ID,
		InvoiceNumber:         res.ExternalID,
		Amount:                req.CashinTransaction.Total,
		Status:                res.Status,
		PaymentProvider:       req.ProviderName,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		ExpiredTime:           res.ExpiryDate.In(loc).Format(time.RFC3339),
		Type:                  constants.TrxCc,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		PaymentURL:            res.InvoiceURL,
	}
}

func generateResponseOttocashEwalletCashin(res ottocash.InquiryResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.ReferenceNumber,
		InvoiceNumber:         req.CashinTransaction.InvoiceNumber,
		Amount:                req.CashinTransaction.Total,
		Status:                constants.StatusPending,
		PaymentProvider:       req.ProviderName,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		Type:                  constants.TrxEwallet,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		PaymentURL:            res.SecurePage,
	}
}

func generateResponseXenditFixedPaymentCodeCashin(res xendit.FixedPaymentCodeResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.ID,
		InvoiceNumber:         res.ExternalID,
		Amount:                req.CashinTransaction.Total,
		Status:                req.CashinTransaction.Status,
		PaymentProvider:       req.ProviderName,
		ExpiredTime:           res.ExpirationDate.In(loc).Format(time.RFC3339),
		Type:                  constants.TrxRetail,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		PaymentCode:           res.PaymentCode,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		DisplayName:           res.Name,
	}
}

func generateResponseNicepayCashin(res nicepay.RegistrationResponse, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	payMethod := ""
	switch res.PayMethod {
	case nicepay.PayMethodVirtualAccount:
		payMethod = constants.TrxVirtualAccount
	}
	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: res.TXid,
		InvoiceNumber:         res.ReferenceNo,
		Amount:                req.CashinTransaction.Total,
		Status:                constants.StatusPending,
		PaymentProvider:       req.ProviderName,
		ExpiredTime:           req.CashinTransaction.ExpiredAt.Format(time.RFC3339),
		Type:                  payMethod,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		AccountNo:             res.VacctNo,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		DisplayName:           res.GoodsNm,
	}
}

func generateResponseDokuVACashin(res doku.VAPaymentCodeRes, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))
	// tm, _ := time.Parse(time.RFC3339, res.VirtualAccountInfo.ExpiredDate)
	tm := res.VirtualAccountInfo.ExpiredDateUtc

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID: req.CashinTransaction.ID,
		// ProviderTransactionID: res.Order.InvoiceNumber,
		InvoiceNumber:       res.Order.InvoiceNumber,
		Amount:              req.CashinTransaction.Total,
		Status:              constants.StatusPending,
		PaymentProvider:     req.ProviderName,
		ExpiredTime:         tm.In(loc).Format(time.RFC3339),
		Type:                constants.TrxVirtualAccount,
		Bank:                req.ChannelName,
		ProviderChannelCode: req.ProviderChannelCode,
		AccountNo:           res.VirtualAccountInfo.VirtualAccountNumber,
		Fees:                fees,
		Total:               req.CashinTransaction.Total + fees,
		DisplayName:         req.CashinTransaction.CustomerName,
		AdditionalInfo: &entity.AdditionalInfo{
			HowToPayPage: res.VirtualAccountInfo.HowToPayPage,
		},
	}
}

func generateResponseCashlezCashin(cashlez domain.CashInAdditionalInfoCashlezz, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                  req.CashinTransaction.ID,
		InvoiceNumber:       req.CashinTransaction.InvoiceNumber,
		Amount:              req.CashinTransaction.Total,
		Status:              req.CashinTransaction.Status,
		PaymentProvider:     req.ProviderName,
		ExpiredTime:         req.ExpiredTime.In(loc).Format(time.RFC3339),
		Type:                constants.TrxOfflinePG,
		Bank:                req.ChannelName,
		ProviderChannelCode: req.ProviderChannelCode,
		Fees:                fees,
		Total:               req.CashinTransaction.Total + fees,
		DisplayName:         req.CashinTransaction.CustomerName,
		AdditionalInfo: &entity.AdditionalInfo{
			CashInAdditionalInfoCashlezz: &cashlez,
		},
	}
}

func generateResponseBankBcaCashin(vaNumber string, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	loc, _ := time.LoadLocation(config.GetString("tz"))

	fees := req.CashinTransaction.ProductFee
	return entity.CashinTranscationResponse{
		ID:                  req.CashinTransaction.ID,
		InvoiceNumber:       req.CashinTransaction.InvoiceNumber,
		Amount:              req.CashinTransaction.Total,
		Status:              req.CashinTransaction.Status,
		PaymentProvider:     req.ProviderName,
		ExpiredTime:         req.ExpiredTime.In(loc).Format(time.RFC3339),
		Type:                constants.TrxSNAP,
		Bank:                req.ChannelName,
		ProviderChannelCode: req.ProviderChannelCode,
		AccountNo:           vaNumber,
		Fees:                fees,
		Total:               req.CashinTransaction.Total + fees,
		DisplayName:         req.CashinTransaction.CustomerName,
	}
}

func generateResponseSnapNicepayVACashin(res snapclient.TransferVaCreateVaResp, req *entity.CashinTransactionProcess) entity.CashinTranscationResponse {
	fees := req.CashinTransaction.ProductFee
	txid, _ := res.VirtualAccountData.AdditionalInfo.Get("tXidVA").(string)
	goodsNm, _ := res.VirtualAccountData.AdditionalInfo.Get("goodsNm").(string)
	return entity.CashinTranscationResponse{
		ID:                    req.CashinTransaction.ID,
		ProviderTransactionID: txid,
		InvoiceNumber:         res.VirtualAccountData.TrxID,
		Amount:                req.CashinTransaction.Total,
		Status:                constants.StatusPending,
		PaymentProvider:       req.ProviderName,
		ExpiredTime:           req.CashinTransaction.ExpiredAt.Format(time.RFC3339),
		Type:                  constants.TrxVirtualAccount,
		Bank:                  req.ChannelName,
		ProviderChannelCode:   req.ProviderChannelCode,
		AccountNo:             res.VirtualAccountData.VirtualAccountNo,
		Fees:                  fees,
		Total:                 req.CashinTransaction.Total + fees,
		DisplayName:           goodsNm,
	}
}
