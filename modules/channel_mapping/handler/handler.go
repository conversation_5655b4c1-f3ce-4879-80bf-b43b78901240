package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

type ChannelMappingHandler struct {
	channelMappingService usecase.ChannelMappingUsecase
}

func NewHandler(channelMappingService usecase.ChannelMappingUsecase) *ChannelMappingHandler {
	return &ChannelMappingHandler{channelMappingService}
}

func (h *ChannelMappingHandler) CreateChannelMapping(c *fiber.Ctx) (err error) {
	var req domain.PaymentProviderChannelMappings
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.channelMappingService.CreateChannelMapping(c.UserContext(), &req)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *ChannelMappingHandler) GetChannelMappingByID(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.channelMappingService.GetChannelMappingById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *ChannelMappingHandler) UpdateChannelMapping(c *fiber.Ctx) (err error) {
	var req domain.PaymentProviderChannelMappings
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.channelMappingService.UpdateChannelMapping(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *ChannelMappingHandler) DeleteChannelMapping(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.channelMappingService.DeleteChannelMapping(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *ChannelMappingHandler) GetListChannelMappingByProviderId(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	providerId := c.Params("providerId")
	isAvailable, _ := strconv.ParseBool(c.Query("is_available"))
	companyId, _ := strconv.Atoi(c.Query("company_id"))
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}
	params := domain.GetPaymentChannelsParams{
		Pagination:  paginate,
		CompanyID:   companyId,
		IsAvailable: isAvailable,
	}
	resp, totalData, err := h.channelMappingService.GetListChannelMappingByProviderId(c.UserContext(), params, providerId)
	if err != nil {
		return
	}
	if totalData == -1 {
		return response.HandleSuccess(c, resp)
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}
