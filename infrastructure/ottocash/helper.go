package ottocash

import (
	"encoding/json"
	"fmt"

	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

func GetOttocashCredential(secret string) (resp Credential, err error) {
	dec, err := utils.DecryptAES(secret, config.GetString("secret_key_company"))
	if err != nil {
		return
	}
	var cred Credential
	err = json.Unmarshal([]byte(dec), &cred)
	if err != nil {
		err = fmt.Errorf("unmarshan credential error: %v", err.<PERSON>rror())
		return
	}

	resp = cred
	return
}
