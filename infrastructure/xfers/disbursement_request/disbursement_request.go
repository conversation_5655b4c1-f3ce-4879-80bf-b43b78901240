package disbursement_request

type AttributeValue struct {
	Amount             float64     `json:"amount"`
	ReferenceID        string      `json:"referenceId"`
	Description        string      `json:"description"`
	DisbursementMethod interface{} `json:"disbursementMethod"`
}

type DisbursementMethod struct {
	Type                  string `json:"type"`
	BankShortCode         string `json:"bankShortCode"`
	BankAccountNo         string `json:"bankAccountNo"`
	BankAccountHolderName string `json:"bankAccountHolderName,omitempty"`
}
