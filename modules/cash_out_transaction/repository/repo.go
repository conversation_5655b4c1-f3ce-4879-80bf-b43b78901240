package repository

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/entity"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

type cashOutRepository struct {
	DB *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.CashOutRepository {
	return &cashOutRepository{db}
}

func (co *cashOutRepository) GetByID(ctx context.Context, ID int) (resp domain.CashOutTransactions, err error) {
	err = co.DB.WithContext(ctx).First(&resp, ID).Error
	return
}

func (co *cashOutRepository) GetByIDs(ctx context.Context, IDs []int) (resp []domain.CashOutTransactions, err error) {
	err = co.DB.WithContext(ctx).Where("id IN ?", IDs).Find(&resp).Error
	return
}

func (co *cashOutRepository) GetRequestedCashOutByIds(ctx context.Context, Ids []int) (resp []domain.CashOutTransactions, err error) {
	err = co.DB.WithContext(ctx).Where("id IN ? AND status = ?", Ids, "requested").Find(&resp).Error
	return
}

func (co *cashOutRepository) ListCashOut(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []domain.CashOutTransactionDetail, totalItem int64, err error) {
	dataType := map[string]interface{}{
		"created_date":    "o.created_at",
		"settlement_date": "TO_TIMESTAMP(o.payment_at)",
	}

	queryParam := func(db *gorm.DB) *gorm.DB {
		condition := db.Where("1=1 AND o.deleted_at IS NULL")

		if param["from"] != nil && param["to"] != nil {
			if _, ok := dataType[param["date_type"].(string)]; !ok {
				param["date_type"] = "created_date"
			}

			dateFilter := fmt.Sprintf("%s BETWEEN ? AND ?", dataType[param["date_type"].(string)])

			condition.Where(dateFilter, param["from"], param["to"])
		}

		if param["status"] != nil {
			condition.Where("o.status = ?", param["status"])
		}

		if param["partner_id"] != nil {
			condition.Where("o.partner_id = ?", param["partner_id"])
		}

		if param["fee_payment_status"] != nil {
			condition.Where("o.fee_payment_status = ?", param["fee_payment_status"])
		}

		if param["value"] != nil {
			searchVal := "%" + fmt.Sprint(param["value"]) + "%"

			if param["key"] != nil {
				switch true {
				case param["key"] == "payment_status":
					condition.Where("o.status = ?", param["value"])

				case param["key"] == "partner_name":
					condition.Where("o.partner_name ILIKE ?", searchVal)

				case param["key"] == "bacth_number":
					condition.Where("o.batch_number ILIKE ?", searchVal)

				case param["key"] == "admin_fee":
					condition.Where("o.admin_fee = ?", param["value"])

				case param["key"] == "platform_fee":
					condition.Where("o.platform_fee = ?", param["value"])

				case param["key"] == "company_name":
					condition.Where("c.name ILIKE ?", searchVal)

				case param["key"] == "partner_bank_name":
					condition.Where("o.partner_bank_name ILIKE ?", searchVal)

				case param["key"] == "payment_provider":
					condition.Where("p.name ILIKE ?", searchVal)

				case param["key"] == "payment_channel":
					condition.Where("n.name ILIKE ?", searchVal)

				case param["key"] == "created_date":
					condition.Where("o.created_at = ?", param["value"])

				case param["key"] == "settlemet_date":
					condition.Where("o.payment_at = ?", param["value"])

				case param["key"] == "company_id":
					condition.Where("o.company_id = ?", param["value"])

				case param["key"] == "fee_payment_status":
					condition.Where("o.fee_payment_status = ?", param["value"])

				case param["key"] == "cashOut_id":
					condition.Where("o.id = ?", param["value"])

				default:
					condition.Where("CONCAT(o.batch_number, ' ', c.name, ' ', o.partner_name) ILIKE ?", searchVal)
				}
			} else {
				condition.Where("CONCAT(o.batch_number, ' ', c.name, ' ', o.partner_name) ILIKE ?", searchVal)
			}
		}

		return condition
	}
	joinQuery := "JOIN companies c ON o.company_id = c.id " +
		"JOIN partners partner ON partner.id = o.partner_id " +
		"JOIN payment_providers p ON o.payment_provider_id = p.id " +
		"JOIN payment_channels n ON o.payment_channel_id = n.id " +
		"LEFT JOIN company_products cp ON cp.id = o.company_product_id " +
		"LEFT JOIN users u_requested ON u_requested.id = o.requested_by " +
		"LEFT JOIN users u_approved ON u_approved.id = o.approved_by "

	selectItem := "o.id, o.batch_number, o.partner_name, partner.fee_behind partner_fee_behind, o.partner_account_number, o.partner_account_name, " +
		"o.partner_bank_name, o.fee_payment_status, o.partner_id, o.total, o.admin_fee, o.platform_fee, o.status, o.created_at, " +
		"o.payment_at, o.disbursement_schedule_at, o.requested_by, u_requested.name requested_by_name, " +
		"o.requested_at, o.approved_by, o.approved_at, u_approved.name approved_by_name," +
		"p.name provider_name, p.id provider_id, " +
		"c.name company_name, c.id as company_id, " +
		"n.name channel_name, n.id channel_id, " +
		"o.company_product_id, cp.product_name company_product_name, cp.code company_product_code "

	page, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["start"]))
	limit, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["limit"]))

	err = co.DB.Debug().WithContext(ctx).Scopes(utils.Paginate(page, limit)).Table("cash_out_transactions o").
		Select(selectItem).Joins(joinQuery).Scopes(queryParam).Order("o.created_at DESC").Find(&res).Error
	if err != nil {
		return
	}

	err = co.DB.Debug().WithContext(ctx).Distinct("o.id").Table("cash_out_transactions o").Joins(joinQuery).Scopes(queryParam).Count(&totalItem).Error
	if err != nil {
		return
	}

	return
}

func (co *cashOutRepository) GetDetailByID(ctx context.Context, id int) (res domain.CashOutTransactionDetail, err error) {

	joinQuery := "JOIN companies c ON o.company_id = c.id " +
		"JOIN partners partner ON partner.id = o.partner_id " +
		"JOIN payment_providers p ON o.payment_provider_id = p.id " +
		"JOIN payment_channels n ON o.payment_channel_id = n.id " +
		"LEFT JOIN company_products cp ON cp.id = o.company_product_id " +
		"LEFT JOIN users u_requested ON u_requested.id = o.requested_by " +
		"LEFT JOIN users u_approved ON u_approved.id = o.approved_by "
	selectItem := "o.id, o.batch_number, o.partner_name, partner.fee_behind partner_fee_behind, o.partner_account_number, o.partner_account_name, " +
		"o.partner_bank_name, o.fee_payment_status, o.partner_id, o.total, o.admin_fee, o.platform_fee, o.status, o.created_at, " +
		"o.payment_at, o.disbursement_schedule_at, o.requested_by, u_requested.name requested_by_name, " +
		"o.requested_at, o.approved_by, o.approved_at, u_approved.name approved_by_name," +
		"p.name provider_name, p.id provider_id, " +
		"c.name company_name, c.id as company_id, " +
		"n.name channel_name, n.id channel_id, " +
		"o.company_product_id, cp.product_name company_product_name, cp.code company_product_code "

	err = co.DB.Debug().WithContext(ctx).Table("cash_out_transactions o").
		Select(selectItem).Joins(joinQuery).Where("o.deleted_at IS NULL AND o.id = ?", id).Order("o.created_at DESC").Take(&res).Error
	if err != nil {
		return
	}

	return
}

func (co *cashOutRepository) GetCashInInfo(ctx context.Context, cashOutIDs []int64) (res []domain.CashOutCashInInfo, err error) {

	joinQuery := `
	LEFT JOIN cash_in_transaction_items citi ON citi.id = co_detail.cash_in_transaction_item_id AND citi.deleted_at IS NULL
	LEFT JOIN cash_in_transactions cit ON cit.id = citi.cash_in_transaction_id
	`

	selectItem := `DISTINCT co_detail.cash_out_transaction_id "cash_out_id", cit.id "cash_in_id", cit.product_fee, cit.discount, cit.voucher, cit.company_product_fee "company_product_fee"`

	err = co.DB.Debug().WithContext(ctx).Table("cash_out_transaction_details co_detail").
		Select(selectItem).Joins(joinQuery).Where("co_detail.deleted_at IS NULL AND co_detail.cash_out_transaction_id IN ?", cashOutIDs).Find(&res).Error
	if err != nil {
		return
	}

	return
}

func (r *cashOutRepository) GetCashInByCashoutID(ctx context.Context, cashoutID int) (resp []domain.CashInTransactionItems, err error) {
	err = r.DB.Select(`ci_item.*`).Debug().
		Table(`cash_out_transaction_details co_detail`).
		Joins(`INNER JOIN cash_in_transaction_items ci_item ON ci_item.id = co_detail.cash_in_transaction_item_id AND ci_item.deleted_at IS NULL`).
		Where("co_detail.deleted_at IS null AND ci_item.status = ? AND co_detail.cash_out_transaction_id = ?", constants.CashInItemStatusReconciled, cashoutID).
		Order("co_detail.created_at ASC").Scan(&resp).Error

	return resp, err
}

func (co *cashOutRepository) Update(ctx context.Context, cashOut *domain.CashOutTransactions, tx *gorm.DB) (res *domain.CashOutTransactions, err error) {
	rs := tx.Debug().WithContext(ctx).Save(&cashOut)
	if rs.Error != nil {
		logger.Error(ctx, "db error update cashout ", logger.Err(rs.Error))
		return res, rs.Error
	}

	return cashOut, err
}

func (co *cashOutRepository) UpdateAmount(ctx context.Context, cashOut *domain.CashOutTransactions, tx *gorm.DB) (res *domain.CashOutTransactions, err error) {
	if tx == nil {
		tx = co.DB
	}
	rs := tx.Debug().WithContext(ctx).Table("cash_out_transactions").Where("id = ?", cashOut.ID).Updates(map[string]interface{}{
		"total":     cashOut.Total,
		"admin_fee": cashOut.AdminFee,
	})
	if rs.Error != nil {
		logger.Error(ctx, "db error update cashout amount", logger.Err(rs.Error))
		return res, rs.Error
	}

	return cashOut, err
}

func (co *cashOutRepository) CreateHistory(ctx context.Context, cashOut *domain.CashOutTransactionHistories, tx *gorm.DB) (res *domain.CashOutTransactionHistories, err error) {
	rs := tx.WithContext(ctx).Create(&cashOut)
	if rs.Error != nil {
		logger.Error(ctx, "db error create history cashout", logger.Err(rs.Error))
		return res, rs.Error
	}

	return cashOut, err
}

func (co *cashOutRepository) RequestDisbursement(ctx context.Context) ([]domain.CashOutTransactions, error) {
	var cashOut []domain.CashOutTransactions

	err := co.DB.WithContext(ctx).Find(&cashOut, "status = ?", "approved").Error
	if err != nil {
		logger.Error(ctx, "db error find", logger.Err(err))
		return cashOut, err
	}
	return cashOut, err
}

func (c *cashOutRepository) UpdateCashOutTransactionHistory(ctx context.Context, req *domain.CashOutTransactionHistories) (err error) {
	err = c.DB.WithContext(ctx).Where("cash_out_transaction_id = ?", req.CashOutTransactionID).Updates(req).Error
	return
}

func (c *cashOutRepository) GetCashOutTransactionByBatchNumber(ctx context.Context, batchNumber string) (resp *domain.CashOutTransactions, err error) {
	err = c.DB.WithContext(ctx).Where("batch_number = ?", batchNumber).First(&resp).Error
	return
}

func (c *cashOutRepository) GetCashOutTransactionDetailsByCashOutId(ctx context.Context, id int) (resp []domain.CashOutTransactionDetails, err error) {
	err = c.DB.WithContext(ctx).Model(domain.CashOutTransactionDetails{}).Where("cash_out_transaction_id = ?", id).Find(&resp).Error
	return
}

func (co *cashOutRepository) ListHistory(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	var result []interface{}

	condition := "AND h.deleted_at IS NULL "
	type cashOut struct {
		ID                   int32  `json:"id"`
		PartnerAccountNumber string `json:"partner_account_number"`
		ProviderName         string `json:"provider_name"`
		ChannelName          string `json:"channel_name"`
		Description          string `json:"description"`
		Status               string `json:"status"`
		CreatedAt            string `json:"created_at"`
	}
	var ct []cashOut

	type total struct {
		TotalItem int64 `json:"total_item"`
	}
	tot := new(total)

	query := "SELECT COUNT(h.id) AS total_item " +
		"FROM cash_out_transaction_histories h " +
		"WHERE 1=1 AND h.cash_out_transaction_id = @cash_out_transaction_id " + condition

	err = co.DB.WithContext(ctx).Raw(query, param, orderParam).Scan(&tot).Error

	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
		return nil, 0, err
	}

	query = "SELECT h.id, c.partner_account_number, h.status, h.created_at, h.description, " +
		"p.name provider_name, " +
		"n.name channel_name " +
		"FROM cash_out_transaction_histories h " +
		"LEFT JOIN cash_out_transactions c ON h.cash_out_transaction_id = c.id " +
		"LEFT JOIN payment_providers p ON c.payment_provider_id = p.id " +
		"LEFT JOIN payment_channels n ON c.payment_channel_id = n.id " +
		"WHERE 1=1 AND h.cash_out_transaction_id = @cash_out_transaction_id " + condition +
		"ORDER BY h.created_at DESC " +
		"OFFSET @start LIMIT @limit "
	err = co.DB.WithContext(ctx).Raw(query, param, orderParam).Scan(&ct).Error
	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
	}

	for _, v := range ct {
		m := map[string]interface{}{
			"id":                     v.ID,
			"partner_account_number": v.PartnerAccountNumber,
			"status":                 v.Status,
			"provider_name":          v.ProviderName,
			"description":            v.Description,
			"channel_name":           v.ChannelName,
			"created_at":             v.CreatedAt,
		}
		result = append(result, m)
	}
	return result, tot.TotalItem, err
}

var getListItemCashInBatchNumberOrderByMap = map[string]string{
	"created_at":     "d.created_at",
	"invoice_number": "c.invoice_number",
}

func (s *cashOutRepository) GetListItemCashInBatchNumber(ctx context.Context, req domain.GetCashoutItemParam) (resp *[]entity.ListItemCashInBatchNumber, total int64, err error) {
	q := s.DB.Debug().WithContext(ctx).Table("cash_out_transaction_details d").
		Select(`d.id, d.created_at, s.item_name, s.note, s.amount as item_amount, s.amount as item_grand_total, c.invoice_number, c.product_fee, c.company_product_fee,
		s.ref_invoice_number, ch."name" as payment_channel_name, p."name" as payment_provider_name, c.payment_at as settlement_date`).
		Joins("JOIN cash_in_transaction_items s ON s.id = d.cash_in_transaction_item_id AND s.deleted_at IS NULL").
		Joins(`JOIN cash_in_transactions c ON s.cash_in_transaction_id = c.id`).
		Joins(`JOIN payment_channels ch ON ch.id = c.payment_channel_id`).
		Joins(`JOIN payment_providers p ON c.payment_provider_id = p.id`).
		Where("d.cash_out_transaction_id = ? AND s.is_cashout = TRUE AND d.deleted_at IS NULL", req.CashoutId)

	if len(req.Paginate.Search) > 0 {
		req.Paginate.Search = "%" + strings.ToLower(req.Paginate.Search) + "%"
		q = q.Where("LOWER(c.invoice_number) LIKE ? OR LOWER(s.ref_invoice_number) LIKE ?", req.Paginate.Search, req.Paginate.Search)
	}

	// Mapping tipe tanggal ke kolom database
	dateColumnMap := map[string]string{
		"created_date":    "d.created_at",
		"settlement_date": "TO_TIMESTAMP(c.payment_at)",
	}
	// Default ke settlement_date jika req.DateType tidak valid
	dateColumn, ok := dateColumnMap[req.DateType]
	if !ok {
		dateColumn = dateColumnMap["settlement_date"]
	}

	// Penerapan filter tanggal jika salah satu dari FromDate atau ToDate disediakan
	if !req.FromDate.IsZero() || !req.ToDate.IsZero() {
		var start, end time.Time
		if !req.FromDate.IsZero() {
			start = req.FromDate
		}
		if !req.ToDate.IsZero() {
			end = req.ToDate.Add(24 * time.Hour)
		} else if !req.FromDate.IsZero() {
			end = start.Add(24 * time.Hour)
		}

		if !req.FromDate.IsZero() && !req.ToDate.IsZero() {
			q = q.Where(fmt.Sprintf("%s >= ? AND %s < ?", dateColumn, dateColumn), start, end)
		} else if !req.FromDate.IsZero() {
			q = q.Where(fmt.Sprintf("%s >= ?", dateColumn), start)
		} else {
			q = q.Where(fmt.Sprintf("%s < ?", dateColumn), end)
		}
	}

	if req.WithTotal {
		qCount := q
		err = qCount.Count(&total).Error
		if err != nil {
			return
		}
	}

	if req.Paginate.IsValidParam() {
		q = q.Scopes(req.Paginate.GormScope())
	}

	err = q.Order(req.Paginate.GetOrderByMaps("d", getListItemCashInBatchNumberOrderByMap)).Find(&resp).Error
	return
}

func (s *cashOutRepository) DeleteCashOutTransaction(ctx context.Context, req *domain.CashOutTransactions, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Delete(req).Error
	return
}

func (s *cashOutRepository) UpdateCashInItemStatusBulk(ctx context.Context, tx *gorm.DB, itemIds []int, status string) (err error) {
	err = tx.WithContext(ctx).Exec(`UPDATE cash_in_transaction_items SET status = ? WHERE id IN ?`, status, itemIds).Error
	return
}

func (s *cashOutRepository) RollbackCashInItemByCashout(ctx context.Context, tx *gorm.DB, cashoutIds []int) (err error) {
	err = tx.WithContext(ctx).Exec(`UPDATE cash_in_transaction_items AS def SET status = ?, is_cashout = ?
		FROM cash_out_transaction_details AS cotd
		WHERE def.id = cotd.cash_in_transaction_item_id AND cotd.cash_out_transaction_id IN ?`,
		constants.StatusPending, false, cashoutIds).Error
	return
}

func (co *cashOutRepository) CountDetail(ctx context.Context, tx *gorm.DB, cashOutID int) (res int, err error) {
	if tx == nil {
		tx = co.DB
	}
	err = tx.Debug().WithContext(ctx).Table("cash_out_transaction_details").
		Select("COUNT(id)").Where("deleted_at IS NULL AND cash_out_transaction_id = ?", cashOutID).Find(&res).Error
	if err != nil {
		return
	}

	return
}

func (r *cashOutRepository) GetAllCompleteDetailByCashOutItemIDs(ctx context.Context, cashOutId int, cashOutItemIds []int) (resp []domain.CashOutTransactionDetailComplete, err error) {
	q := r.DB.Debug().WithContext(ctx).Table("cash_out_transaction_details cod").
		Select(`cod.id, cod.cash_out_transaction_id, cod.cash_in_transaction_item_id, cti.cash_in_transaction_id, cod.created_at, cod.updated_at, cti.amount`).
		Joins(`LEFT JOIN cash_in_transaction_items cti ON cod.cash_in_transaction_item_id = cti.id AND cti.deleted_at IS NULL`).
		Where(`cti.status = ? AND cod.deleted_at IS NULL AND cod.cash_out_transaction_id = ?`, constants.CashInItemStatusReconciled, cashOutId)
	if len(cashOutItemIds) > 0 {
		q = q.Where("cod.id IN ?", cashOutItemIds)
	}
	err = q.Order("cod.updated_at DESC").Find(&resp).Error
	return
}

func (s *cashOutRepository) DeleteCashOutItemsByIDs(ctx context.Context, tx *gorm.DB, cashOutId int, itemIds []int) (err error) {
	if tx == nil {
		tx = s.DB
	}
	err = tx.Debug().WithContext(ctx).Exec(`UPDATE cash_out_transaction_details SET deleted_at = NOW() WHERE id IN ? AND cash_out_transaction_id = ?`, itemIds, cashOutId).Error
	return
}

var getAllOutstandingItemCashInOrderByMap = map[string]string{
	"created_at":     "cit.created_at",
	"invoice_number": "cit.invoice_number",
	"payment_at":     "cit.payment_at",
}

// get cash in that not have cash out transactions
func (r *cashOutRepository) GetAllOutstandingItemCashIn(ctx context.Context, params domain.CashInTransactionItemsOutstandingParam) (resp []domain.CashInTransactionItemsOutstanding, total int64, err error) {
	q := r.DB.Debug().WithContext(ctx).Table("cash_in_transactions cit").
		Select(`cit.invoice_number, pc."name" "payment_channel_name", pp."name" "payment_provider_name", cit.payment_at, 
		citi.id, citi.cash_in_transaction_id, cit.company_product_id "cash_in_transaction_company_product_id",
		cit.customer_name, cit.total, 
		citi.partner_id, p."name" "partner_name",
		citi.item_name, citi.amount, citi.status, citi.is_cashout, 
		citi.sla_date, citi.created_at, citi.updated_at`).
		Joins(`JOIN payment_channels pc ON pc.id = cit.payment_channel_id`).
		Joins(`JOIN payment_providers pp ON pp.id = cit.payment_provider_id `).
		Joins(`JOIN cash_in_transaction_items citi ON citi.cash_in_transaction_id = cit.id`).
		Joins(`JOIN partners p ON citi.partner_id = p.id`).
		Joins(`LEFT JOIN cash_out_transaction_details co ON co.cash_in_transaction_item_id = citi.id AND co.deleted_at IS NULL`).
		Where("co.id IS NULL AND citi.is_cashout = true") // outstanding condition
	if params.PartnerID > 0 {
		q = q.Where(`p.id = ?`, params.PartnerID)
	}
	if params.CompanyProductId > 0 {
		q = q.Where(`cit.company_product_id = ?`, params.CompanyProductId)
	}
	if len(params.CashInItemIDs) > 0 {
		q = q.Where(`citi.id IN ?`, params.CashInItemIDs)
	}

	if len(params.Search) > 0 {
		search := "%" + strings.ToLower(params.Search) + "%"
		q = q.Where("LOWER(cit.customer_name) LIKE ? OR LOWER(cit.invoice_number) LIKE ?", search, search)
	}

	if !params.SettlementDateStart.IsZero() {
		q.Where(`cit.payment_at >= ?`, params.SettlementDateStart.Unix())
	}
	if !params.SettlementDateEnd.IsZero() {
		q.Where(`cit.payment_at < ?`, params.SettlementDateEnd.Add(24*time.Hour).Unix())
	}

	if params.WithTotal {
		qCount := q
		err = qCount.Count(&total).Error
		if err != nil {
			return
		}
	}

	if params.Paginate.IsValidParam() {
		q = q.Scopes(params.Paginate.GormScope())
	}

	err = q.Order(params.Paginate.GetOrderByMaps("cit", getAllOutstandingItemCashInOrderByMap)).Find(&resp).Error
	return
}

func (r *cashOutRepository) Export(ctx context.Context, req *domain.CashoutExportReq) (res []domain.CashoutExportRes, err error) {

	q := r.DB.Debug().Table("cash_out_transactions co").
		Select(`co.id, co.batch_number, co.admin_fee, co.fee_payment_status, co.total, 
		cit.ref_invoice_number, p."name" partner_name, cp.cashout_fee_percentage as fee_percentage,
    	cp.cashout_fee_fix_value as fee_fix_value, cit.note cash_in_item_note, ci.customer_name, ci.customer_email, 
		co.payment_at, ci.payment_at settlement_date, cit.amount grand_total, ci.product_fee, ci.company_product_fee,
		ci.id "cash_in_id",
		ci.invoice_number "cash_in_invoice_number"`).
		Joins(`INNER JOIN cash_out_transaction_details cod ON co.id = cod.cash_out_transaction_id AND cod.deleted_at IS NULL`).
		Joins(`INNER JOIN cash_in_transaction_items cit ON cit.id = cod.cash_in_transaction_item_id`).
		Joins(`INNER JOIN cash_in_transactions ci ON ci.id = cit.cash_in_transaction_id`).
		Joins(`INNER JOIN company_products cp ON cp.id = co.company_product_id`).
		Joins(`INNER JOIN partners p ON p.id = co.partner_id`).
		Where("co.deleted_at IS NULL").
		Order("ci.invoice_number ASC, cod.id ASC")
	if req.CashoutID != 0 {
		q = q.Where("co.id = ?", req.CashoutID)
	}
	if !req.StartDatetime.IsZero() && !req.EndDatetime.IsZero() {
		q = q.Where(`DATE_TRUNC('day',to_timestamp(ci.payment_at) AT TIME ZONE 'Asia/Jakarta') >= ? AND DATE_TRUNC('day',to_timestamp(ci.payment_at) AT TIME ZONE 'Asia/Jakarta') <= ?`, req.StartDatetime.Format(timepkg.TimeDateFormat), req.EndDatetime.Format(timepkg.TimeDateFormat))
	}

	err = q.Find(&res).Error

	return
}

func (r *cashOutRepository) UpdateCashOutDetails(ctx context.Context, cashOutId int, tx *gorm.DB) (err error) {
	// payment_at is unix timestamp now
	updatedAt := time.Now()

	if tx == nil {
		tx = tx.WithContext(ctx).Begin()
		if tx.Error != nil {
			return tx.Error
		}
	}

	// get all cash in transaction item id by cash out id
	var cashInItemIds []int
	err = tx.Table("cash_in_transaction_items i").
		Select("i.cash_in_transaction_id").
		Joins("LEFT JOIN cash_out_transaction_details o ON o.cash_in_transaction_item_id = i.id").
		Where("o.cash_out_transaction_id = ?", cashOutId).Find(&cashInItemIds).Error
	if err != nil {
		tx.Rollback()
		return
	}

	// update detail cashin
	query := `UPDATE cash_in_transaction_items SET updated_at = ? , status = ? WHERE id IN (?)`
	err = tx.Exec(query, updatedAt, "done", cashInItemIds).Error
	if err != nil {
		tx.Rollback()
		return
	}

	// update cash in transaction status to done
	query = `UPDATE cash_in_transactions SET updated_at = ? , status = ? WHERE id IN (?)`
	err = tx.Exec(query, updatedAt, "done", cashInItemIds).Error
	if err != nil {
		tx.Rollback()
		return
	}

	return
}

func (r *cashOutRepository) SaveEmailLog(ctx context.Context, emailLog entity.EmailLog) (err error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// check if email log already exists
	var existingEmailLog int64
	err = tx.Table("cashout_email_logs").Select("id").
		Where("cashout_id = ? AND company_id = ? AND partner_id = ? AND batch_number = ? AND email = ?",
			emailLog.CashoutID, emailLog.CompanyID, emailLog.PartnerID, emailLog.BatchNumber, emailLog.Email).
		Count(&existingEmailLog).Error
	if err != nil {
		tx.Rollback()
		return
	}

	// if not exists, create new email log
	if existingEmailLog == 0 {
		err = tx.Table("cashout_email_logs").Create(&emailLog).Error
		if err != nil {
			tx.Rollback()
			return
		}
	} else {
		// update email log
		err = tx.Table("cashout_email_logs").Where("id = ?", existingEmailLog).Updates(&emailLog).Error
		if err != nil {
			tx.Rollback()
			return
		}
	}

	err = tx.Commit().Error
	if err != nil {
		tx.Rollback()
		return
	}

	return
}
