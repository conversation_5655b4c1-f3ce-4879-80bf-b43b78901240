package domain

import (
	"fmt"
	"math"
	"time"

	"gorm.io/gorm"
)

type CashOutTransactions struct {
	ID                          int                          `gorm:"primaryKey"`
	CompanyID                   int                          `json:"company_id"`
	PartnerID                   int                          `json:"partner_id"`
	PaymentProviderID           int                          `json:"payment_provider_id"`
	PaymentChannelID            int                          `json:"payment_channel_id"`
	CompanyProductID            *int                         `json:"company_product_id"`
	BatchNumber                 string                       `json:"batch_number"`
	PartnerName                 string                       `json:"partner_name"`
	PartnerBankName             string                       `json:"partner_bank_name"`
	PartnerAccountNumber        string                       `json:"partner_account_number"`
	PartnerAccountName          string                       `json:"partner_account_name"`
	Total                       float64                      `json:"total"`
	PlatformFee                 float64                      `json:"platform_fee"`
	AdminFee                    float64                      `json:"admin_fee"`
	PaymentAt                   int                          `json:"payment_at"`
	PgReferenceInformation      string                       `json:"pg_reference_information"`
	DisbursementScheduleAt      int64                        `json:"disbursement_schedule_at"`
	Status                      string                       `json:"status"`
	RequestedBy                 *int                         `json:"requested_by"`
	RequestedAt                 *time.Time                   `json:"requested_at"`
	ApprovedBy                  *int                         `json:"approved_by"`
	ApprovedAt                  *time.Time                   `json:"approved_at"`
	FeePaymentStatus            bool                         `json:"fee_payment_status"`
	FeePaymentAt                *time.Time                   `json:"fee_payment_at"`
	CreatedAt                   time.Time                    `json:"created_at"`
	UpdatedAt                   time.Time                    `json:"updated_at"`
	DeletedAt                   gorm.DeletedAt               `gorm:"index" json:"deleted_at"`
	CashOutTransactionHistories *CashOutTransactionHistories `gorm:"foreignKey:CashOutTransactionID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"history,omitempty"`
}

func (c *CashOutTransactions) SetAdminFee(partner Partners, totalCashIn int) { // will be remove after all product used fee per product
	// formula for each item = partner fee fix + ( partner fee percentage * sub total cash in)
	//formula for group sum(sub total item) * partner fee percentage + (partner fee fix * total cash in)
	fmt.Println("[DEBUG ADMIN FEE]", partner.FeePercentage, partner.FeeFixValue, c.Total, totalCashIn)
	feePercentage := (partner.FeePercentage / 100) * c.Total
	fixFee := (partner.FeeFixValue * float64(totalCashIn))
	c.AdminFee = feePercentage + fixFee
	c.AdminFee = math.Round(c.AdminFee)
}

func (c *CashOutTransactions) SetAdminFeeFromProduct(product CompanyProducts, totalCashIn int) {
	// formula for each item = partner fee fix + ( partner fee percentage * sub total cash in)
	//formula for group sum(sub total item) * partner fee percentage + (partner fee fix * total cash in)
	fmt.Println("[DEBUG ADMIN FEE]", product.CashoutFeePercentage, product.CashoutFeeFixValue, c.Total, totalCashIn)
	feePercentage := (product.CashoutFeePercentage / 100) * c.Total
	fixFee := (product.CashoutFeeFixValue * float64(totalCashIn))
	c.AdminFee = feePercentage + fixFee
	c.AdminFee = math.Round(c.AdminFee)
}

type CashOutTransactionDetail struct {
	ID                     int64                    `json:"id" gorm:"column:id;<-false"`
	BatchNumber            string                   `json:"batch_number" gorm:"column:batch_number;<-false"`
	PartnerName            string                   `json:"partner_name" gorm:"column:partner_name;<-false"`
	PartnerFeeBehind       bool                     `json:"partner_fee_behind" gorm:"column:partner_fee_behind;<-false"`
	PartnerAccountNumber   string                   `json:"partner_account_number" gorm:"column:partner_account_number;<-false"`
	PartnerAccountName     string                   `json:"partner_account_name" gorm:"column:partner_account_name;<-false"`
	PartnerBankName        string                   `json:"partner_bank_name" gorm:"column:partner_bank_name;<-false"`
	Total                  float64                  `json:"total" gorm:"column:total;<-false"`
	AdminFee               float64                  `json:"admin_fee" gorm:"column:admin_fee;<-false"`
	PlatformFee            float64                  `json:"platform_fee" gorm:"column:platform_fee;<-false"`
	Status                 string                   `json:"status" gorm:"column:status;<-false"`
	CreatedAt              string                   `json:"created_at" gorm:"column:created_at;<-false"`
	PaymentAt              UnixTime                 `json:"payment_at" gorm:"column:payment_at;<-false"`
	ProviderName           string                   `json:"provider_name" gorm:"column:provider_name;<-false"`
	ProviderID             int64                    `json:"provider_id" gorm:"column:provider_id;<-false"`
	CompanyName            string                   `json:"company_name" gorm:"column:company_name;<-false"`
	ChannelName            string                   `json:"channel_name" gorm:"column:channel_name;<-false"`
	ChannelID              int64                    `json:"channel_id" gorm:"column:channel_id;<-false"`
	CompanyID              int64                    `json:"company_id" gorm:"column:company_id;<-false"`
	DisbursementScheduleAt UnixTime                 `json:"disbursement_schedule_at" gorm:"column:disbursement_schedule_at;<-false"`
	RequestedBy            int                      `json:"requested_by" gorm:"column:requested_by;<-false"`
	RequestedByName        string                   `json:"requested_by_name" gorm:"column:requested_by_name;<-false"`
	RequestedAt            *time.Time               `json:"requested_at" gorm:"column:json;<-false"`
	ApprovedBy             int                      `json:"approved_by" gorm:"column:approved_by;<-false"`
	ApprovedByName         string                   `json:"approved_by_name" gorm:"column:approved_by_name;<-false"`
	ApprovedAt             *time.Time               `json:"approved_at" gorm:"column:json;<-false"`
	FeePaymentStatus       bool                     `json:"fee_payment_status" gorm:"column:fee_payment_status;<-false"`
	PartnerId              int                      `json:"partner_id" gorm:"column:partner_id;<-false"`
	CashInTotalAmount      CashOutCashInTotalAmount `json:"cash_out_cash_in_total_amount" gorm:"embedded"`
	CompanyProductId       int                      `json:"company_product_id" gorm:"column:company_product_id;<-false"`
	CompanyProductName     string                   `json:"company_product_name" gorm:"column:company_product_name;<-false"`
	CompanyProductCode     string                   `json:"company_product_code" gorm:"column:company_product_code;<-false"`
}

type CashOutCashInInfo struct {
	CashOutID         int64   `json:"cash_out_id" gorm:"column:cash_out_id;<-false"`
	CashInID          int64   `json:"cash_in_id" gorm:"column:cash_in_id;<-false"`
	Discount          float64 `json:"discount" gorm:"column:discount;<-false"`
	Voucher           float64 `json:"voucher" gorm:"column:voucher;<-false"`
	ProductFee        float64 `json:"transaction_fee" gorm:"column:product_fee;<-false"`
	CompanyProductFee float64 `json:"company_product_fee" gorm:"column:company_product_fee;<-false"`
}
type CashOutCashInTotalAmount struct {
	TotalDiscount  float64 `json:"total_discount" gorm:"column:total_discount;<-false"`
	TotalVoucher   float64 `json:"total_voucher" gorm:"column:total_voucher;<-false"`
	TransactionFee float64 `json:"transaction_fee" gorm:"column:transaction_fee;<-false"` // total product fee
}

type CashoutExportReq struct {
	CashoutID     int
	CashoutInfo   *CashOutTransactions
	StartDatetime time.Time
	EndDatetime   time.Time
	ShowFee       bool
}

type CashoutExportRes struct {
	Id               int     `gorm:"column:id;<-false"`
	BatchNumber      string  `gorm:"column:batch_number;<-false"`
	AdminFee         float64 `gorm:"column:admin_fee;<-false"`
	FeePaymentStatus bool    `gorm:"column:fee_payment_status;<-false"`
	Total            float64 `gorm:"column:total;<-false"`
	FeePercentage    float64 `gorm:"column:fee_percentage" json:"fee_percentage"`
	FeeFixValue      float64 `gorm:"column:fee_fix_value" json:"fee_fix_value"`

	RefInvoiceNumber    string   `gorm:"column:ref_invoice_number;<-false"`
	PartnerName         string   `gorm:"column:partner_name;<-false"`
	CashInItemNote      string   `gorm:"column:cash_in_item_note;<-false"`
	CustomerName        string   `gorm:"column:customer_name;<-false"`
	CustomerEmail       string   `gorm:"column:customer_email;<-false"`
	PaymentAt           UnixTime `gorm:"column:payment_at;<-false"`
	SettlementDate      UnixTime `gorm:"column:settlement_date;<-false"`
	GrandTotal          float64  `gorm:"column:grand_total;<-false"` // grand total cashout item
	CashInId            int      `gorm:"column:cash_in_id;<-false"`
	CashInInvoiceNumber string   `gorm:"column:cash_in_invoice_number;<-false"`
	CompanyProductFee   float64  `gorm:"column:company_product_fee;<-false"`
}

func (c *CashoutExportRes) TotalAdminFeePercentage() float64 { // will be remove after all product used fee per product
	feePercentage := (c.FeePercentage / 100) * c.Total

	return feePercentage
}
