package usecase

import (
	"context"
	"log"
	"time"

	"repo.nusatek.id/nusatek/payment/modules/healty/entity"
)

func (s *defaultHealtyService) CheckHealty(ctx context.Context) (res entity.HealthCheckResponse) {
	err := s.healtyRepo.Ping(ctx)
	if err != nil {
		log.Fatalf("failed ping database, err: %s", err.Error())
		return
	}

	res = entity.HealthCheckResponse{
		Message:    "Server up and running",
		ServerTime: time.Now().Format(time.RFC1123),
	}

	return
}
