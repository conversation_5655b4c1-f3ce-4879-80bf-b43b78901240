package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_channel/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (s *defaultPaymentChannel) CreatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error) {
	_, err = s.paymentChannelRepo.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannelTypeId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel type not found")
		logger.Error(ctx, err.Error())
		return
	}
	err = s.paymentChannelRepo.CreatePaymentChannel(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in name field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultPaymentChannel) UpdatePaymentChannel(ctx context.Context, req *domain.PaymentChannels, id string) (err error) {
	_, err = s.paymentChannelRepo.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannelTypeId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel type not found")
		logger.Error(ctx, err.Error())
		return
	}
	paymentChannel, err := s.paymentChannelRepo.GetPaymentChannelById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	req.ID = paymentChannel.ID
	req.CreatedAt = paymentChannel.CreatedAt
	err = s.paymentChannelRepo.UpdatePaymentChannel(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in name field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCachePaymentChannel(ctx, id)

	return
}

func (s *defaultPaymentChannel) DeletePaymentChannel(ctx context.Context, id string) (err error) {
	paymentChannel, err := s.paymentChannelRepo.GetPaymentChannelById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	exist, err := s.paymentChannelRepo.CheckExistingDataRelation(ctx, paymentChannel.ID)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		return err
	}
	if exist {
		err = errors.SetErrorMessage(http.StatusForbidden, "there is existing relation data")
		return err
	}

	var req domain.PaymentChannels
	req.ID = paymentChannel.ID
	err = s.paymentChannelRepo.DeletePaymentChannel(ctx, &req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCachePaymentChannel(ctx, id)
	return
}

func (s *defaultPaymentChannel) SearchPaymentChannel(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	resp, totalData, err = s.paymentChannelRepo.SearchPaymentChannel(ctx, paginate)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := range *resp {
		if (*resp)[i].LogoURL != "" {
			(*resp)[i].LogoURL, err = s.s3.GetURL((*resp)[i].LogoURL)
			if err != nil {
				logger.Error(ctx, err.Error())
			}
		}
	}

	return
}

func (s *defaultPaymentChannel) GetPaymentChannelListActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	resp, totalData, err = s.paymentChannelRepo.GetPaymentChannelListAllActive(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := range *resp {
		if (*resp)[i].LogoURL != "" {
			(*resp)[i].LogoURL, err = s.s3.GetURL((*resp)[i].LogoURL)
			if err != nil {
				logger.Error(ctx, err.Error())
			}
		}
	}

	return
}

func (s *defaultPaymentChannel) GetPaymentChannelListAll(ctx context.Context, status string) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	switch status {
	case "true":
		resp, totalData, err = s.paymentChannelRepo.GetPaymentChannelListAllActive(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	case "false":
		resp, totalData, err = s.paymentChannelRepo.GetPaymentChannelListAllNotActive(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	default:
		resp, totalData, err = s.paymentChannelRepo.GetPaymentChannelListAll(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	for i := range *resp {
		if (*resp)[i].LogoURL != "" {
			(*resp)[i].LogoURL, err = s.s3.GetURL((*resp)[i].LogoURL)
			if err != nil {
				logger.Error(ctx, err.Error())
			}
		}
	}

	return
}

func (s *defaultPaymentChannel) GetPaymentChannelById(ctx context.Context, id string) (resp *entity.PaymentChannelShow, err error) {
	resp, err = s.paymentChannelRepo.GetPaymentChannelById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if resp.LogoURL != "" {
		resp.LogoURL, err = s.s3.GetURL(resp.LogoURL)
		if err != nil {
			logger.Error(ctx, err.Error())
		}
	}

	return
}

func (s *defaultPaymentChannel) GetPaymentChannelTypeById(ctx context.Context, id string) (resp *domain.PaymentChannelTypes, err error) {
	resp, err = s.getCachePaymentChannelType(ctx, id)
	return
}

func (s *defaultPaymentChannel) GetListPaymentChannelType(ctx context.Context) (resp *[]domain.PaymentChannelTypes, err error) {
	resp, err = s.paymentChannelRepo.GetListPaymentChannelType(ctx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultPaymentChannel) GetCachePaymentChannel(ctx context.Context, channelId string) (resp *entity.PaymentChannelShow, err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePaymentChannel, channelId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.GetPaymentChannelById(ctx, channelId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "paymentChannel not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCachePaymentChannel(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod entity.PaymentChannelShow
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultPaymentChannel) setCachePaymentChannel(ctx context.Context, req *entity.PaymentChannelShow) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CachePaymentChannel, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultPaymentChannel) deleteCachePaymentChannel(ctx context.Context, channelId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePaymentChannel, channelId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultPaymentChannel) getCachePaymentChannelType(ctx context.Context, channelTypeId string) (resp *domain.PaymentChannelTypes, err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePaymentChannelType, channelTypeId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.paymentChannelRepo.GetPaymentChannelTypeById(ctx, channelTypeId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "payment channel type not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCachePaymentChannelType(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.PaymentChannelTypes
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultPaymentChannel) setCachePaymentChannelType(ctx context.Context, req *domain.PaymentChannelTypes) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CachePaymentChannelType, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultPaymentChannel) SelectByIds(ctx context.Context, ids []int) (resp []domain.PaymentChannels, err error) {
	resp, err = s.paymentChannelRepo.SelectByIds(ctx, ids)
	return
}

func (s *defaultPaymentChannel) UpdatePaymentChannelStatus(ctx context.Context, id string, status bool) (err error) {
	paymentChannel, err := s.paymentChannelRepo.GetPaymentChannelById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}

	err = s.paymentChannelRepo.UpdatePaymentChannelStatus(ctx, paymentChannel.ID, status)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCachePaymentChannel(ctx, id)
	return
}
