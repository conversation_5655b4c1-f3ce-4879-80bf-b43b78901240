package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

func (h *PartnerHandler) CreateTag(c *fiber.Ctx) (err error) {
	var req domain.Tags
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.PartnerUseCase.CreateTag(c.UserContext(), &req)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *PartnerHandler) GetTagByID(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.PartnerUseCase.GetTagById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PartnerHandler) UpdateTag(c *fiber.Ctx) (err error) {
	var req domain.Tags
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.PartnerUseCase.UpdateTag(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *PartnerHandler) DeleteTag(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.PartnerUseCase.DeleteTag(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *PartnerHandler) GetListAllTag(c *fiber.Ctx) (err error) {
	resp, err := h.PartnerUseCase.GetListAllTag(c.UserContext())
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}
