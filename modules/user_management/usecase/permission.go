package usecase

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

func (s *defaultUserManagement) CreateRolePermission(ctx context.Context, req *domain.RolePermissions) (err error) {
	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	err = s.userRepo.CreateRolePermission(ctx, req, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	tx.Commit()
	return
}

func (s *defaultUserManagement) createRolePermissionMenu(ctx context.Context, userPermission *[]domain.RolePermissions, menus *[]domain.Menus, roleId string, tx *gorm.DB) (isCreate bool, err error) {
	var userPermissionIdArr []int
	for j := 0; j < len(*userPermission); j++ {
		userPermissionIdArr = append(userPermissionIdArr, (*userPermission)[j].MenuId)
	}
	var menuIdArr []int
	for k := 0; k < len(*menus); k++ {
		menuIdArr = append(menuIdArr, (*menus)[k].ID)
	}
	resArr := sortDifferenceMenus(menuIdArr, userPermissionIdArr)
	if resArr != nil {
		isCreate = true
		for r := 0; r < len(resArr); r++ {
			menu, errRes := s.userRepo.GetUserMenuById(ctx, strconv.Itoa(resArr[r]))
			if errRes != nil {
				err = errors.SetErrorMessage(http.StatusNotFound, "menu not found")
				logger.Error(ctx, err.Error())
				return
			}
			var permissionUser = model.RoleUserPermission{}
			perUser, _ := json.Marshal(permissionUser)
			roleIdInt, _ := strconv.Atoi(roleId)
			permissionUserMap := make(map[string]bool)
			if menu.Code == constants.MenuCashIn {
				permissionUserMap["view"] = false
				permissionUserMap["paid_snap"] = false
				perUser, _ = json.Marshal(permissionUserMap)
			}
			if menu.Code == constants.MenuCashOut {
				permissionUserMap["view"] = false
				permissionUserMap["transfer"] = false
				permissionUserMap["approve"] = false
				permissionUserMap["reject"] = false
				permissionUserMap["revised"] = false
				permissionUserMap["done_manual"] = false
				permissionUserMap["email_resent"] = false
				permissionUserMap["has_invoice_batch"] = false
				perUser, _ = json.Marshal(permissionUserMap)
			}

			req := domain.RolePermissions{
				RoleId:      roleIdInt,
				MenuId:      resArr[r],
				Permissions: string(perUser),
			}
			err = s.userRepo.CreateRolePermission(ctx, &req, tx)
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}
		}
	}
	return
}
