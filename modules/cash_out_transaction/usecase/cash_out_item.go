package usecase

import (
	"context"
	"fmt"
	"net/http"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
)

func (s *defaultCashOutUsecase) AddCashOutItemsByIDs(ctx context.Context, cashOutId int, cashInItemIDs []int) error {
	existingCO, err := s.cashOutRepository.GetByID(ctx, cashOutId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid cashout id")
		logger.Error(ctx, err.Error())
		return err
	}

	// partner, err := s.partnerRepo.GetPartnerByID(ctx, fmt.Sprintf("%v", existingCO.PartnerID))
	// if err != nil {
	// 	err = errors.SetErrorMessage(http.StatusNotFound, "invalid partner")
	// 	logger.Error(ctx, err.Error())
	// 	return err
	// }
	var compProduct domain.CompanyProducts
	if existingCO.CompanyProductID != nil {
		product, err := s.comProductRepo.GetCompanyProductById(ctx, *existingCO.CompanyProductID)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "invalid partner")
			logger.Error(ctx, err.Error())
			return err
		}
		compProduct = *product
	}

	if existingCO.CompanyProductID == nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "old cashout doesnt have company product")
		logger.Error(ctx, err.Error())
		return err
	}

	cashInItems, _, err := s.cashOutRepository.GetAllOutstandingItemCashIn(ctx, domain.CashInTransactionItemsOutstandingParam{
		CashInItemIDs: cashInItemIDs,
	})
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}
	mapExistCashIn := make(map[int]domain.CashInTransactionItemsOutstanding)
	for _, v := range cashInItems {
		if v.CashInTransactionCompanyProductID != *existingCO.CompanyProductID {
			logger.Warn(ctx, fmt.Sprintf("comp product id cashout not valid %v != %v", v.CashInTransactionCompanyProductID, *existingCO.CompanyProductID))
			continue
		}
		mapExistCashIn[v.ID] = v
	}

	addedTotal := 0.

	items, err := s.cashOutRepository.GetCashInByCashoutID(ctx, cashOutId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}
	cashInMap := make(map[int]bool)
	var totalItemAmount float64
	var totalCashIn int
	var origCashInIds []int64

	for _, v := range items {
		totalItemAmount += v.Amount
		_, exist := cashInMap[v.CashInTransactionID]
		if exist {
			continue
		}

		origCashInIds = append(origCashInIds, int64(v.CashInTransactionID))
		cashInMap[v.CashInTransactionID] = true
		totalCashIn++
	}

	cashOuts := []domain.CashOutTransactionDetails{}
	for _, v := range cashInItemIDs {
		ciItem, exist := mapExistCashIn[v]
		if !exist {
			err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("cash in item %v not outstanding", v))
			logger.Error(ctx, err.Error())
			return err
		}
		addedTotal += ciItem.Amount
		cashOuts = append(cashOuts, domain.CashOutTransactionDetails{
			CashOutTransactionID:    cashOutId,
			CashInTransactionItemID: v,
		})
		// check existing cash in count
		_, exist = cashInMap[ciItem.CashInTransactionID]
		if exist {
			continue
		}

		origCashInIds = append(origCashInIds, int64(ciItem.CashInTransactionID))
		cashInMap[ciItem.CashInTransactionID] = true
		totalCashIn++
	}

	tx := s.cashInRepo.BeginTrans()
	defer tx.Rollback()

	err = s.cashInRepo.CreateBulkCashOutTransactionDetail(ctx, &cashOuts, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	updatedCO := domain.CashOutTransactions{
		ID:    existingCO.ID,
		Total: totalItemAmount + addedTotal,
	}

	// updatedCO.SetAdminFee(*partner, totalCashIn)
	// updatedCO.SetAdminFeeFromProduct(compProduct, totalCashIn)

	fees, err := s.cashInRepo.GetTotalCompanyProductFees(ctx, origCashInIds)
	if err != nil {
		logger.Error(ctx, err.Error())
	}

	updatedCO.AdminFee = fees
	_ = compProduct

	// update cashout
	_, err = s.cashOutRepository.UpdateAmount(ctx, &updatedCO, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	return nil
}

func (s *defaultCashOutUsecase) DeleteCashOutItemsByIDs(ctx context.Context, cashOutId int, cashoutItemIDs []int) (err error) {
	existingCO, err := s.cashOutRepository.GetByID(ctx, cashOutId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid cashout id")
		logger.Error(ctx, err.Error())
		return err
	}

	cashOutItems, err := s.cashOutRepository.GetAllCompleteDetailByCashOutItemIDs(ctx, cashOutId, nil)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	cashInMap := make(map[int]int)
	var totalCashIn int
	var totalItemAmount float64
	var origCashInIds []int64
	mapCashoutItem := make(map[int]domain.CashOutTransactionDetailComplete)

	for _, v := range cashOutItems {
		totalItemAmount += v.Amount
		mapCashoutItem[v.ID] = v
		_, exist := cashInMap[v.CashInTransactionID]
		if exist {
			cashInMap[v.CashInTransactionID] += 1
			continue
		}

		origCashInIds = append(origCashInIds, int64(v.CashInTransactionID))
		cashInMap[v.CashInTransactionID] = 1
		totalCashIn++
	}
	logger.Info(ctx, fmt.Sprintf("total cash in %v", totalCashIn))

	reducedTotal := 0.

	// validate
	for _, v := range cashoutItemIDs {
		item, exist := mapCashoutItem[v]
		if !exist {
			err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("cashout item id %v not found", v))
			return
		}
		reducedTotal += item.Amount
		// check existing cash in

		cashInMap[item.CashInTransactionID] -= 1

		if cashInMap[item.CashInTransactionID] == 0 {
			totalCashIn--

			// remove slice with item.CashInTransactionID from origCashInIds
			for i, v := range origCashInIds {
				if v == int64(item.CashInTransactionID) {
					origCashInIds = append(origCashInIds[:i], origCashInIds[i+1:]...)
					break
				}
			}
		}
	}
	logger.Info(ctx, fmt.Sprintf("total cash in %v", totalCashIn))

	// partner, err := s.partnerRepo.GetPartnerByID(ctx, fmt.Sprintf("%v", existingCO.PartnerID))
	// if err != nil {
	// 	err = errors.SetErrorMessage(http.StatusNotFound, "invalid partner")
	// 	logger.Error(ctx, err.Error())
	// 	return err
	// }
	var compProduct domain.CompanyProducts

	if existingCO.CompanyProductID != nil {
		product, err := s.comProductRepo.GetCompanyProductById(ctx, *existingCO.CompanyProductID)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "invalid partner")
			logger.Error(ctx, err.Error())
			return err
		}
		compProduct = *product
	}

	tx := s.cashInRepo.BeginTrans()
	defer tx.Rollback()

	updatedCO := domain.CashOutTransactions{
		ID:    existingCO.ID,
		Total: totalItemAmount - reducedTotal,
	}

	//updatedCO.SetAdminFeeFromProduct(compProduct, totalCashIn)
	fees, err := s.cashInRepo.GetTotalCompanyProductFees(ctx, origCashInIds)
	if err != nil {
		logger.Error(ctx, err.Error())
	}

	updatedCO.AdminFee = fees
	_ = compProduct

	// updatedCO.SetAdminFee(*partner, totalCashIn)

	// update cashout
	_, err = s.cashOutRepository.UpdateAmount(ctx, &updatedCO, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	err = s.cashOutRepository.DeleteCashOutItemsByIDs(ctx, tx, cashOutId, cashoutItemIDs)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetError(http.StatusNotFound, "cashout or item not found")
			return
		}
		return
	}

	count, err := s.cashOutRepository.CountDetail(ctx, tx, cashOutId)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("error cound detail %v", err.Error()))
		logger.Error(ctx, err.Error())
		return
	}

	if count <= 0 {
		err = s.cashOutRepository.DeleteCashOutTransaction(ctx, &existingCO, tx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("error delete cash out %v", err.Error()))
			logger.Error(ctx, err.Error())
			return
		}
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	return
}

func (s *defaultCashOutUsecase) GetAllOutstandingItemCashIn(ctx context.Context, param domain.CashInTransactionItemsOutstandingParam) (resp []domain.CashInTransactionItemsOutstanding, total int64, err error) {
	resp, total, err = s.cashOutRepository.GetAllOutstandingItemCashIn(ctx, param)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}
