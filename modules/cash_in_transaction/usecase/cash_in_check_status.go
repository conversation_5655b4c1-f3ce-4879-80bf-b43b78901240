package usecase

import (
	"context"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

func (s *defaultCashinTransaction) CheckStatusCashinAPI(ctx context.Context, id, auth string) (resp entity.CashinList, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	var paginate utils.Pagination
	paginate.Key = "cash_in_id"
	paginate.Value = id
	paginate.Limit = 1
	paginate.Page = 1

	cashInList, totalData, err := s.cashinRepo.GetCashinTranscationList(ctx, entity.CashinListParam{Pagination: paginate})
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error get cash in transaction list", logger.Err(err))
		return
	}

	if totalData == 0 {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
		logger.Error(ctx, "total data is 0", logger.Err(err))
		return
	}

	resp = (*cashInList)[0]

	if resp.CompanyId != company.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found in company")
		logger.Error(ctx, "resp.CompanyId != company.ID", logger.Err(err))
		return
	}

	return
}
