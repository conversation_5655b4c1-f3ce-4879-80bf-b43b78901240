version: '3'
services:
  redis:
    image: redis:alpine3.14
    restart: always
    ports:
      - 6379:6379
    volumes: 
      - redis:/data

  rabbitmq:
    image: rabbitmq:3.10.5-management
    restart: always
    container_name: rabbitmq
    ports:
    - 5672:5672
    - 15672:15672
    volumes:
        - rabbitmq:/var/lib/rabbitmq/
        - rabbitmqlog:/var/log/rabbitmq

# create the directory first if you want to use this
volumes:
  redis:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: volumes/redis
      
  rabbitmq:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: volumes/rabbitmq
  rabbitmqlog:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: volumes/rabbitmq/log