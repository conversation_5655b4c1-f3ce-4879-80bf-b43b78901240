package doku

import (
	"context"
	"fmt"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (w *wrapper) OrderStatus(ctx context.Context, credential Credential, invoiceNumber string) (rawResp []byte, resp OrderStatusRes, err error) {
	path := fmt.Sprintf("/orders/v1/status/%s", invoiceNumber)
	hrw := HttpReqWrapper{
		Credential: credential,
		Method:     http.MethodGet,
		Path:       path,
		ReqBody:    nil,
		Header:     make(map[string]string),
	}
	hrsp, err := w.httpReqWrapper(ctx, hrw, &resp)
	rawResp = hrsp.RespBody
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		return
	}

	return
}
