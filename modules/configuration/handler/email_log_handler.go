package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/mailgun/mailgun-go/v4"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/moaja/backend/services/entities/common"
	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/modules/configuration/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type EmailLogHandler interface {
	GetEmailLogs(c *fiber.Ctx) error
	GetEmailLogDetail(c *fiber.Ctx) error
	EmailWebhook(c *fiber.Ctx) error
	ResendEmail(c *fiber.Ctx) error
}

type emailLogHandler struct {
	uc usecase.EmailLogUsecase
}

func NewEmailLogHandler(uc usecase.EmailLogUsecase) EmailLogHandler {
	return &emailLogHandler{uc: uc}
}

func (h *emailLogHandler) EmailWebhook(c *fiber.Ctx) error {
	var payload mailgun.WebhookPayload

	if err := c.BodyParser(&payload); err != nil {
		return c.Status(http.StatusNotAcceptable).JSON(common.NewResponse(http.StatusNotAcceptable, err.Error(), nil))
	}

	_, err := h.uc.Webhook(c.UserContext(), payload)
	if err != nil {
		return c.Status(http.StatusNotFound).JSON(common.NewResponseNotFound(err.Error()))
	}

	return c.Status(http.StatusOK).JSON(common.NewResponseSuccess(nil, "success"))
}

func (h *emailLogHandler) GetEmailLogs(c *fiber.Ctx) error {
	var payload entity.EmailLogPaginate

	if err := c.QueryParser(&payload); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	q := c.Query("q")
	if q != "" {
		payload.Search = q
	}

	// Menggunakan struct untuk mengatur pagination
	if payload.Pagination == nil {
		payload.Pagination = &utils.Pagination{
			Page:  1,
			Limit: 10,
		}
	} else {
		if payload.Pagination.Page == 0 {
			payload.Pagination.Page = 1
		}
		if payload.Pagination.Limit == 0 {
			payload.Pagination.Limit = 10
		}
	}

	emailLogs, totalData, err := h.uc.GetEmailLogs(c.Context(), &payload)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccessWithPagination(c, float64(totalData), *payload.Pagination, emailLogs)
}

func (h *emailLogHandler) GetEmailLogDetail(c *fiber.Ctx) error {
	type request struct {
		Page        int    `query:"page" validate:"required,min=1"`
		ItemPerPage int    `query:"limit" validate:"required,min=1"`
		Key         string `query:"key"`
		Value       string `query:"value"`
	}

	id, err := strconv.ParseInt(c.Params("id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid log Id")
	}

	r := new(request)
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	param := map[string]interface{}{}
	if r.Value != "" {
		param["value"] = r.Value
	}

	if r.Key != "" {
		param["key"] = r.Key
	}

	orderParam := map[string]interface{}{
		"start": r.Page,
		"limit": r.ItemPerPage,
	}

	emailLogDetail, err := h.uc.GetEmailLogDetail(c.Context(), id, param, orderParam)
	if err != nil {
		return errors.SetErrorMessage(http.StatusNotFound, err.Error())
	}

	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}

	return response.HandleSuccessWithPagination(c, float64(len(emailLogDetail)), paginate, emailLogDetail)
}

func (h *emailLogHandler) CreateEmailLog(c *fiber.Ctx) error {
	var emailLog entity.EmailLog
	if err := c.BodyParser(&emailLog); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	createdLog, err := h.uc.CreateEmailLog(c.Context(), &emailLog)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, createdLog)
}

func (h *emailLogHandler) UpdateEmailLog(c *fiber.Ctx) error {
	id, err := strconv.ParseInt(c.Params("id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid ID")
	}

	var emailLog entity.EmailLog
	if err := c.BodyParser(&emailLog); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	emailLog.Id = id

	updatedLog, err := h.uc.UpdateEmailLog(c.Context(), &emailLog)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, updatedLog)
}

func (h *emailLogHandler) DeleteEmailLog(c *fiber.Ctx) error {
	id, err := strconv.ParseInt(c.Params("id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid ID")
	}

	err = h.uc.DeleteEmailLog(c.Context(), id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, nil)
}

func (h *emailLogHandler) ResendEmail(c *fiber.Ctx) error {
	id, err := strconv.ParseInt(c.Params("id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid ID")
	}

	err = h.uc.ResendEmail(c.Context(), id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, nil)
}
