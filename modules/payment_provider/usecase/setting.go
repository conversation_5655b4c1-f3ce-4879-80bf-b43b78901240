package usecase

import "github.com/go-redis/redis/v8"

type defaultPaymentProvider struct {
	paymentProviderRepo PaymentProviderRepository
	cache               *redis.Client
}

func Setup() *defaultPaymentProvider {
	return &defaultPaymentProvider{}
}

func (s *defaultPaymentProvider) SetPaymentProviderRepo(t PaymentProviderRepository) *defaultPaymentProvider {
	s.paymentProviderRepo = t
	return s
}

func (s *defaultPaymentProvider) SetRedisClient(t *redis.Client) *defaultPaymentProvider {
	s.cache = t
	return s
}

func (s *defaultPaymentProvider) Validate() PaymentProviderUsecase {
	if s.paymentProviderRepo == nil {
		panic("payment provider repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	return s
}