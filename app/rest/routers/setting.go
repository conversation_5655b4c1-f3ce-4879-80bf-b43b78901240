package routers

import (
	cashinHandler "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/handler"
	cashOutHandler "repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/handler"
	channelMappingHandler "repo.nusatek.id/nusatek/payment/modules/channel_mapping/handler"
	companyHandler "repo.nusatek.id/nusatek/payment/modules/company_management/handler"
	companyMappingHandler "repo.nusatek.id/nusatek/payment/modules/company_mapping/handler"
	companyProductHandler "repo.nusatek.id/nusatek/payment/modules/company_product/handler"
	companyProductProviderChannelMappingHandler "repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/handler"
	configurationEmailHandler "repo.nusatek.id/nusatek/payment/modules/configuration/handler"
	fileHandler "repo.nusatek.id/nusatek/payment/modules/file/handler"
	healtyHandler "repo.nusatek.id/nusatek/payment/modules/healty/handler"
	partnerHandler "repo.nusatek.id/nusatek/payment/modules/partner/handler"
	paymentChannelHandler "repo.nusatek.id/nusatek/payment/modules/payment_channel/handler"
	paymentProviderHandler "repo.nusatek.id/nusatek/payment/modules/payment_provider/handler"
	snapHandler "repo.nusatek.id/nusatek/payment/modules/snap/handler"
	userHandler "repo.nusatek.id/nusatek/payment/modules/user_management/handler"
	webhookHandler "repo.nusatek.id/nusatek/payment/modules/webhook/handler"
)

type rooters struct {
	healtyHandler                               *healtyHandler.HealtyHandler
	companyHandler                              *companyHandler.CompanyManagementHandler
	companyProductHandler                       *companyProductHandler.CompanyProductHandler
	paymentChannelHandler                       *paymentChannelHandler.PaymentChannelHandler
	paymentProviderHandler                      *paymentProviderHandler.PaymentProviderHandler
	channelMappingHandler                       *channelMappingHandler.ChannelMappingHandler
	partnerHandler                              *partnerHandler.PartnerHandler
	companyMappingHandler                       *companyMappingHandler.CompanyMappingHandler
	companyProductProviderChannelMappingHandler *companyProductProviderChannelMappingHandler.HttpHandler
	cashinHandler                               *cashinHandler.CashinHandler
	cashOutHandler                              *cashOutHandler.CashOutHandler
	userHandler                                 *userHandler.UserManagementHandler
	fileHandler                                 *fileHandler.FileHandler
	webhookHandler                              *webhookHandler.WebhookHandler
	// snap
	snapHandler                  *snapHandler.Handler
	configurationEmailHandler    configurationEmailHandler.EmailTemplateHandler
	configurationEmailLogHandler configurationEmailHandler.EmailLogHandler
}

func SetupRest() *rooters {
	return &rooters{}
}

func (h *rooters) SetupCompanyHandler(t *companyHandler.CompanyManagementHandler) *rooters {
	h.companyHandler = t
	return h
}

func (h *rooters) SetupHealtyHandler(t *healtyHandler.HealtyHandler) *rooters {
	h.healtyHandler = t
	return h
}

func (h *rooters) SetupCompanyProductHandler(t *companyProductHandler.CompanyProductHandler) *rooters {
	h.companyProductHandler = t
	return h
}

func (h *rooters) SetupPaymentChannelHandler(t *paymentChannelHandler.PaymentChannelHandler) *rooters {
	h.paymentChannelHandler = t
	return h
}

func (h *rooters) SetupPaymentProviderHandler(t *paymentProviderHandler.PaymentProviderHandler) *rooters {
	h.paymentProviderHandler = t
	return h
}

func (h *rooters) SetupChannelMappingHandler(t *channelMappingHandler.ChannelMappingHandler) *rooters {
	h.channelMappingHandler = t
	return h
}

func (h *rooters) SetupPartnerHandler(t *partnerHandler.PartnerHandler) *rooters {
	h.partnerHandler = t
	return h
}

func (h *rooters) SetupCompanyMappingHandler(t *companyMappingHandler.CompanyMappingHandler) *rooters {
	h.companyMappingHandler = t
	return h
}

func (h *rooters) SetupCompanyProductProviderChannelMappingHandler(t *companyProductProviderChannelMappingHandler.HttpHandler) *rooters {
	h.companyProductProviderChannelMappingHandler = t
	return h
}

func (h *rooters) SetupCashInHandler(t *cashinHandler.CashinHandler) *rooters {
	h.cashinHandler = t
	return h
}

func (h *rooters) SetupCashOutHandler(t *cashOutHandler.CashOutHandler) *rooters {
	h.cashOutHandler = t
	return h
}

func (h *rooters) SetupUserManagementHandler(t *userHandler.UserManagementHandler) *rooters {
	h.userHandler = t
	return h
}

func (h *rooters) SetupFileHandler(t *fileHandler.FileHandler) *rooters {
	h.fileHandler = t
	return h
}

func (h *rooters) SetupSnapHandler(t *snapHandler.Handler) *rooters {
	h.snapHandler = t
	return h
}

func (h *rooters) SetupConfigurationEmailHandler(t configurationEmailHandler.EmailTemplateHandler) *rooters {
	h.configurationEmailHandler = t
	return h
}

func (h *rooters) SetupConfigurationEmailLogHandler(t configurationEmailHandler.EmailLogHandler) *rooters {
	h.configurationEmailLogHandler = t
	return h
}

func (h *rooters) SetupWebhook(t *webhookHandler.WebhookHandler) *rooters {
	h.webhookHandler = t
	return h
}

func (h *rooters) Validate() *rooters {
	if h.healtyHandler == nil {
		panic("healty handler is nil")
	}

	if h.companyHandler == nil {
		panic("company Handler is nil")
	}

	if h.companyProductHandler == nil {
		panic("company product handler is nil")
	}

	if h.paymentChannelHandler == nil {
		panic("payment channel handler is nil")
	}

	if h.paymentProviderHandler == nil {
		panic("payment provider handler is nil")
	}

	if h.channelMappingHandler == nil {
		panic("channel mapping handler is nil")
	}

	if h.partnerHandler == nil {
		panic("partner handler is nil")
	}

	if h.companyMappingHandler == nil {
		panic("company mapping handler is nil")
	}

	if h.cashinHandler == nil {
		panic("cash in handler is nil")
	}

	if h.cashOutHandler == nil {
		panic("cash out handler is nil")
	}

	if h.userHandler == nil {
		panic("user management is nil")
	}

	if h.snapHandler == nil {
		panic("snap handler is nil")
	}

	if h.webhookHandler == nil {
		panic("webhook hander is nill")
	}

	if h.configurationEmailHandler == nil {
		panic("configuration email handler is nil")
	}

	if h.configurationEmailLogHandler == nil {
		panic("configuration email log handler is nil")
	}

	return h
}
