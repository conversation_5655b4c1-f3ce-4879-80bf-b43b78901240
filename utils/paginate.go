package utils

import (
	"fmt"
	"regexp"
	"strings"

	"gorm.io/gorm"
)

type Pagination struct {
	Page     int    `query:"page"`
	Limit    int    `query:"limit"`
	Search   string `query:"search"`
	Status   string `query:"status"`
	Key      string `query:"key"`
	Value    string `query:"value"`
	In       string `query:"in"`
	FromDate string `query:"from" validate:"number"`
	ToDate   string `query:"to" validate:"number"`
	Order    string `query:"order"`
	Sort     string `query:"sort"`
	maxLimit int
}

func (p *Pagination) IsValidParam() bool {
	return p.Page != 0 && p.Limit != 0
}

type ItemPages struct {
	Page      int64 `json:"page"`
	Limit     int64 `json:"limit"`
	TotalData int64 `json:"total_data"`
	TotalPage int64 `json:"total_page"`
}

func (p *Pagination) GetOrderBy(alias string) string {
	var order string
	if alias != "" {
		alias = alias + "."
	}

	// Regular expression untuk mendeteksi kolom timestamp
	timestampPattern := regexp.MustCompile(`(_at|_date|Date)$`)

	// Regular expression untuk mendeteksi kolom BOOLEAN
	booleanPattern := regexp.MustCompile(`(^is_|_flag$|status$)`)

	// Regular expression untuk mendeteksi kolom NUMERIC
	numericPattern := regexp.MustCompile(`(_id|_count|_number|_value|_price|_total|_quantity|_amount|_rate|_fee|_percentage)`)

	// Logika utama untuk menentukan tipe data
	switch true {
	case p.Order != "" && p.Sort != "":
		switch {
		case timestampPattern.MatchString(p.Order):
			// Jika kolom cocok dengan pola timestamp
			order = fmt.Sprintf("%s%s %s", alias, p.Order, strings.ToUpper(p.Sort))
		case booleanPattern.MatchString(p.Order):
			// Jika kolom cocok dengan pola boolean
			order = fmt.Sprintf("%s%s %s", alias, p.Order, strings.ToUpper(p.Sort))
		case numericPattern.MatchString(p.Order):
			// Jika kolom cocok dengan pola numeric
			order = fmt.Sprintf("%s%s %s", alias, p.Order, strings.ToUpper(p.Sort))
		default:
			// Jika kolom dianggap teks (default)
			order = fmt.Sprintf("lower(%s%s) %s", alias, strings.ToLower(p.Order), strings.ToUpper(p.Sort))
		}
	case p.Order != "" && p.Sort == "":
		switch {
		case timestampPattern.MatchString(p.Order):
			order = fmt.Sprintf("%s%s %s", alias, p.Order, "ASC")
		case booleanPattern.MatchString(p.Order):
			order = fmt.Sprintf("%s%s %s", alias, p.Order, "ASC")
		case numericPattern.MatchString(p.Order):
			order = fmt.Sprintf("%s%s %s", alias, p.Order, "ASC")
		default:
			order = fmt.Sprintf("lower(%s%s) %s", alias, strings.ToLower(p.Order), "ASC")
		}
	case p.Order == "" && p.Sort != "":
		// Default ke kolom teks
		order = fmt.Sprintf("%s%s %s", alias, "updated_at", strings.ToUpper(p.Sort))
	default:
		// Default ke kolom teks
		order = fmt.Sprintf("%s%s %s", alias, "updated_at", "DESC")
	}

	return order
}

func (p *Pagination) GetOrderByMaps(alias string, fieldMaps map[string]string) string {
	if alias != "" {
		alias = alias + "."
	}

	if len(p.Sort) == 0 {
		p.Sort = "DESC"
	}

	// Regular expression untuk mendeteksi kolom timestamp
	timestampPattern := regexp.MustCompile(`(_at|_date|Date)$`)

	// Regular expression untuk mendeteksi kolom BOOLEAN
	booleanPattern := regexp.MustCompile(`(^is_|_flag$|status$)`)

	// Regular expression untuk mendeteksi kolom NUMERIC
	numericPattern := regexp.MustCompile(`(_id|_count|_number|_value|_price|_total|_quantity|_amount|_rate|_fee|_percentage)`)

	if fieldMaps != nil {
		if value, exist := fieldMaps[p.Order]; exist {
			// Deteksi kolom BOOLEAN
			if booleanPattern.MatchString(value) {
				// Kolom BOOLEAN digunakan langsung tanpa LOWER
				p.Order = value
			} else if timestampPattern.MatchString(value) {
				// Kolom timestamp digunakan langsung tanpa LOWER
				p.Order = value
			} else if numericPattern.MatchString(value) {
				// Kolom NUMERIC digunakan langsung tanpa LOWER
				p.Order = value
			} else if !strings.HasPrefix(value, "LOWER(") && !strings.Contains(value, "(") {
				// Bungkus dengan LOWER jika kolom adalah string
				p.Order = fmt.Sprintf("LOWER(%s)", value)
			} else {
				// Gunakan nilai apa adanya
				p.Order = value
			}
		} else {
			// Jika kolom tidak ditemukan, gunakan kolom timestamp default
			p.Order = fmt.Sprintf("%supdated_at", alias)
		}
	}

	return fmt.Sprintf("%s %s", p.Order, p.Sort)
}

func (p *Pagination) SetMaxLimit(limit int) {
	p.maxLimit = limit
}

func (p Pagination) GormScope() func(db *gorm.DB) *gorm.DB {
	// validate max limit
	if p.maxLimit <= 0 {
		p.maxLimit = 50
	}

	return func(db *gorm.DB) *gorm.DB {
		if p.Page == 0 {
			p.Page = 1
		}

		switch {
		case p.Limit > p.maxLimit:
			p.Limit = p.maxLimit
		case p.Limit <= 0:
			p.Limit = 10
		}

		offset := (p.Page - 1) * p.Limit
		return db.Offset(offset).Limit(p.Limit)
	}

}

const (
	maxLength = 100
)

// TODO: remove this function and used paginate.GormScope
func Paginate(page, length int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page == 0 {
			page = 1
		}

		switch {
		case length > maxLength:
			length = maxLength
		case length <= 0:
			length = 10
		}

		offset := (page - 1) * length
		return db.Offset(offset).Limit(length)
	}
}
