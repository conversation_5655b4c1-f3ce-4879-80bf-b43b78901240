package repository

import (
	"context"
	"database/sql"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/usecase"
)

type gormRepository struct {
	db        *gorm.DB
	tableName string
}

func NewGORMRepository(db *gorm.DB) usecase.Repository {
	return &gormRepository{
		db:        db,
		tableName: "company_product_provider_channel_mappings",
	}
}

func (r gormRepository) BeginTrans(opts ...*sql.TxOptions) *gorm.DB {
	return r.db.Begin(opts...)
}

func (r gormRepository) Create(ctx context.Context, data domain.CompanyProductProviderChannelMapping) {
	now := time.Now()
	gormData := entity.GORM{
		CompanyPaymentProviderChannelMappingID: data.CompanyPaymentProviderChannelMappingID,
		CompanyProductID:                       data.CompanyProductID,
		FeeFixValue:                            data.FeeFixValue,
		FeePercentage:                          data.FeePercentage,
		Status:                                 data.Status,
		CreatedAt:                              now,
		UpdatedAt:                              now,
	}

	r.db.Create(gormData)
}

func (r gormRepository) GetCompanyProduct(ctx context.Context, productID int) (res domain.CompanyProducts, err error) {
	gormData := entity.GORMCompanyProduct{}
	err = r.db.Table("company_products").Select(`id, company_id, code, product_name, fee_fix_value, fee_percentage, status`).
		Where("deleted_at IS NULL").
		Where(`id = ?`, productID).Find(&gormData).Error
	res = gormData.ToDomain()
	return
}

func (r gormRepository) GetCompanyProductByCode(ctx context.Context, productCode string) (res domain.CompanyProducts, err error) {
	gormData := entity.GORMCompanyProduct{}
	err = r.db.Table("company_products").Select(`id, company_id, code, product_name, fee_fix_value, fee_percentage, status`).
		Where("deleted_at IS NULL").
		Where(`code = ?`, productCode).Find(&res).Error
	res = gormData.ToDomain()
	return
}

func (r gormRepository) GetCompanyProviderChannelMappings(ctx context.Context, channelMappingIDs []int) (res []domain.CompanyPaymentProviderChannelMappings, err error) {
	gormData := []entity.GORMCompanyPaymentProviderChannelMapping{}
	err = r.db.Table("company_payment_provider_channel_mappings").Select(`id, company_id, payment_provider_id, payment_channel_id, fee_fix_value, fee_percentage, capability, expired_time, status`).
		Where("deleted_at IS NULL").
		Where(`id IN ?`, channelMappingIDs).Find(&gormData).Error

	for _, v := range gormData {
		res = append(res, v.ToDomain())
	}

	return
}

func (r gormRepository) GetExistingMaps(ctx context.Context, productIDs []int, channelIDs []int) (exists map[int]map[int]bool, err error) {
	exists = make(map[int]map[int]bool) // comapny_product_id : channel_id : isExist (true/false)
	res := []entity.GormExistingData{}
	err = r.db.Table(r.tableName).Select(`company_product_id, company_payment_provider_channel_mapping_id`).
		Where("deleted_at IS NULL").
		Where(`company_product_id IN ?`, productIDs).
		Where(`company_payment_provider_channel_mapping_id IN ?`, channelIDs).
		Find(&res).Error
	if err != nil {
		return
	}

	for _, data := range res {
		exists[data.CompanyProductID] = make(map[int]bool)
		exists[data.CompanyProductID][data.CompanyPaymentProviderChannelMappingID] = true
	}

	return
}

func (r gormRepository) CreateBulkWithProductID(ctx context.Context, tx *gorm.DB, productID int, datas []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	if tx == nil {
		tx = r.db
	}
	now := time.Now()
	gormDatas := []entity.GORM{}
	for _, data := range datas {
		gormData := entity.GORM{
			CompanyPaymentProviderChannelMappingID: data.CompanyPaymentProviderChannelMappingID,
			CompanyProductID:                       productID,
			FeeFixValue:                            data.FeeFixValue,
			FeePercentage:                          data.FeePercentage,
			Status:                                 data.Status,
			CreatedAt:                              now,
			UpdatedAt:                              now,
		}
		gormDatas = append(gormDatas, gormData)
	}

	err = tx.Debug().Create(&gormDatas).Error
	if err != nil {
		return
	}

	for _, gormData := range gormDatas {
		res = append(res, domain.CompanyProductProviderChannelMapping{
			ID:                                     gormData.ID,
			CompanyProductID:                       gormData.CompanyProductID,
			CompanyPaymentProviderChannelMappingID: gormData.CompanyPaymentProviderChannelMappingID,
			FeeFixValue:                            gormData.FeeFixValue,
			FeePercentage:                          gormData.FeePercentage,
			CreatedAt:                              gormData.CreatedAt,
			UpdatedAt:                              gormData.UpdatedAt,
		})
	}

	return
}

func (r gormRepository) GetOneByProductIDAndChannelID(ctx context.Context, productID, channelID int) (res domain.CompanyProductProviderChannelMapping, err error) {
	gormData := entity.GORM{}
	err = r.db.Debug().Where("deleted_at IS NULL").
		Where(`company_product_id = ?`, productID).
		Where(`company_payment_provider_channel_mapping_id = ?`, channelID).Limit(1).Take(&gormData).Error
	if err != nil {
		return
	}
	res = gormData.ToDomain()

	return
}

func (r gormRepository) GetAllByIDs(ctx context.Context, ids []int) (res []domain.CompanyProductProviderChannelMapping, err error) {
	gormDatas := []entity.GORM{}
	err = r.db.Debug().Find(&gormDatas, ids).Error
	if err != nil {
		return
	}

	for _, gormData := range gormDatas {
		res = append(res, gormData.ToDomain())
	}

	return
}

func (r gormRepository) GetAllByProductID(ctx context.Context, productID int) (res []domain.CompanyProductProviderChannelMapping, err error) {
	gormDatas := []entity.GORM{}
	err = r.db.Debug().Where("company_product_id = ?", productID).Find(&gormDatas).Error
	if err != nil {
		return
	}

	for _, gormData := range gormDatas {
		res = append(res, gormData.ToDomain())
	}

	return
}

func (r gormRepository) GetAllWithDetailByProductID(ctx context.Context, productID int) (res []domain.CompanyProductProviderChannelMapping, err error) {
	gormDatas := []entity.GORMDetail{}
	err = r.db.Debug().Table(r.tableName+" comp_product_cm").
		Select(`
		comp_product_cm.id, comp_product_cm.company_product_id, comp_product_cm.company_payment_provider_channel_mapping_id,
			comp_product_cm.fee_fix_value, comp_product_cm.fee_percentage, comp_product_cm.status, comp_product_cm.created_at, comp_product_cm.updated_at,  
		comp_product.id "company_product_id", 
			comp_product.code "company_product_code", comp_product.product_name "company_product_name",
		prov_cm.id "company_provider_channel_mapping_id", 
			prov_cm.code "company_provider_channel_mapping_code", 
			prov_cm.provider_channel_code "company_provider_channel_mapping_provider_code",   
			comp_prov_cm.fee_fix_value "company_provider_channel_mapping_fee_fix_value",   
			comp_prov_cm.fee_percentage "company_provider_channel_mapping_fee_percentage",   
		payment_prov.id "payment_provider_id", 
			payment_prov.code "payment_provider_code", payment_prov."name" "payment_provider_name",
		payment_chan.id "payment_channel_id", payment_chan."name" "payment_channel_name"`).
		Joins(`INNER JOIN company_products comp_product 
			ON comp_product.id = comp_product_cm.company_product_id`).
		Joins(`INNER JOIN company_payment_provider_channel_mappings comp_prov_cm 
			ON comp_prov_cm.id = comp_product_cm.company_payment_provider_channel_mapping_id 
			AND comp_prov_cm.company_id = comp_product.company_id`).
		Joins(`INNER JOIN payment_provider_channel_mappings prov_cm 
		ON prov_cm.payment_provider_id = comp_prov_cm.payment_provider_id 
		AND prov_cm.payment_channel_id = comp_prov_cm.payment_channel_id
		AND prov_cm.capability = comp_prov_cm.capability`).
		Joins(`INNER JOIN payment_providers payment_prov
		ON payment_prov.id = prov_cm.payment_provider_id  `).
		Joins(`INNER JOIN payment_channels payment_chan
		ON payment_chan.id = prov_cm.payment_channel_id`).
		Where(`comp_product_cm.deleted_at IS NULL`).
		Where(`comp_product_cm.company_product_id = ?`, productID).
		Order(`comp_product_cm.id DESC`).
		Find(&gormDatas).Error
	if err != nil {
		return
	}

	for _, gormData := range gormDatas {
		res = append(res, gormData.ToDomain())
	}

	return
}

func (r gormRepository) GetOneWithDetailByCompany(ctx context.Context, companyID int, productCode, provChannelCode string) (res domain.CompanyProductProviderChannelMapping, err error) {
	gormData := entity.GORMDetail{}
	err = r.db.Debug().Table(r.tableName+" comp_product_cm").
		Select(`
		comp_product_cm.id, comp_product_cm.company_product_id, comp_product_cm.company_payment_provider_channel_mapping_id,
			comp_product_cm.fee_fix_value, comp_product_cm.fee_percentage, comp_product_cm.status, comp_product_cm.created_at, comp_product_cm.updated_at,  
		comp_product.id "company_product_id", 
			comp_product.code "company_product_code", comp_product.product_name "company_product_name",
		prov_cm.id "company_provider_channel_mapping_id", 
			prov_cm.code "company_provider_channel_mapping_code", 
			prov_cm.provider_channel_code "company_provider_channel_mapping_provider_code",   
		payment_prov.id "payment_provider_id", 
			payment_prov.code "payment_provider_code", payment_prov."name" "payment_provider_name",
		payment_chan.id "payment_channel_id", payment_chan."name" "payment_channel_name"`).
		Joins(`INNER JOIN company_products comp_product 
			ON comp_product.id = comp_product_cm.company_product_id`).
		Joins(`INNER JOIN company_payment_provider_channel_mappings comp_prov_cm 
			ON comp_prov_cm.id = comp_product_cm.company_payment_provider_channel_mapping_id 
			AND comp_prov_cm.company_id = comp_product.company_id`).
		Joins(`INNER JOIN payment_provider_channel_mappings prov_cm 
		ON prov_cm.payment_provider_id = comp_prov_cm.payment_provider_id 
		AND prov_cm.payment_channel_id = comp_prov_cm.payment_channel_id
		AND prov_cm.capability = comp_prov_cm.capability`).
		Joins(`INNER JOIN payment_providers payment_prov
		ON payment_prov.id = prov_cm.payment_provider_id  `).
		Joins(`INNER JOIN payment_channels payment_chan
		ON payment_chan.id = prov_cm.payment_channel_id`).
		Where(`comp_product_cm.deleted_at IS NULL`).
		Where(`comp_product.company_id = ?`, companyID).
		Where(`comp_product.code = ?`, productCode).
		Where(`prov_cm.code = ?`, provChannelCode).
		Order(`comp_product_cm.id DESC`).
		Limit(1).
		Take(&gormData).Error
	if err != nil {
		return
	}

	res = gormData.ToDomain()

	return
}

func (r gormRepository) UpdateBulk(ctx context.Context, tx *gorm.DB, datas []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	now := time.Now()
	isNeedTX := false
	if tx == nil {
		tx = r.db.Begin()
		isNeedTX = true
	}
	defer func() {
		if isNeedTX {
			tx.Rollback()
		}
	}()

	for _, data := range datas {
		if data.ID != 0 {
			tx.Debug().Model(&entity.GORM{}).Where("id", data.ID).Updates(map[string]interface{}{"fee_fix_value": data.FeeFixValue, "fee_percentage": data.FeePercentage, "status": data.Status, "updated_at": now})
		}
	}

	if isNeedTX {
		err = tx.Commit().Error
	}

	res = datas
	return
}

func (r gormRepository) DeleteBulk(ctx context.Context, tx *gorm.DB, ids []int) (err error) {
	if tx == nil {
		tx = r.db
	}
	gormDatas := []entity.GORM{}
	err = tx.Debug().Delete(&gormDatas, ids).Error
	if err != nil {
		return
	}

	return
}
