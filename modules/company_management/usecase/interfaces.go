package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type CompanyManagementRepo interface {
	CreateCompany(ctx context.Context, req *domain.Companies) (err error)
	UpdateCompany(ctx context.Context, req *domain.Companies) (err error)
	GetCompanyById(ctx context.Context, id string) (resp *domain.Companies, err error)
	DeleteCompany(ctx context.Context, req *domain.Companies) (err error)
	SearchCompany(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error)
	CompanyList(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error)
	CompanyListAll(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error)
	CompanyListAllActive(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error)
	CompanyListAllNotActive(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error)
	GetCompanyBySecrets(ctx context.Context, secret string) (resp *domain.Companies, err error)
	GetCompanyByCode(ctx context.Context, code string) (resp *domain.Companies, err error)
	GetCompanyBySecret(ctx context.Context, secret string) (resp *domain.Companies, err error)
	GetLastCompany(ctx context.Context) (resp *domain.Companies, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.Companies, err error)
	IsActive(ctx context.Context, companyID int64) (resp bool, err error)
	ChekExistingRelationData(ctx context.Context, companyID int, isCheckStatus bool) (resp bool, err error)
	GetWebhookByCompanyIdAndEventType(ctx context.Context, companyId int, eventType string) (*domain.Companies, error)
	UpdateOrCreateWebhook(ctx context.Context, companyId int, webhook *domain.CompanyWebhook) error
	UpdateStatus(ctx context.Context, id string, status bool) (err error)
}

type CompanyManagementUsecase interface {
	GetCompanyByIdShow(ctx context.Context, id string) (resp entity.CompanyShow, err error)
	GenerateRandomApiKey(ctx context.Context) (resp string, err error)
	GetCompanyById(ctx context.Context, id string) (resp *domain.Companies, err error)
	CreateCompany(ctx context.Context, req *domain.Companies) (err error)
	UpdateCompany(ctx context.Context, req *domain.Companies) (err error)
	DeleteCompany(ctx context.Context, id string) (err error)
	SearchCompany(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error)
	GetCompanyListAll(ctx context.Context, status string) (resp *[]domain.Companies, totalData int64, err error)
	GetCompanyByCode(ctx context.Context, code string) (resp *domain.Companies, err error)
	GetCompanyBySecrets(ctx context.Context, secrets string) (resp *domain.Companies, err error)
	ValidatorCompanySecret(ctx context.Context, auth string) (resp *domain.Companies, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.Companies, err error)
	IsActive(ctx context.Context, companyID int64) (resp bool, err error)
	UpdateStatus(ctx context.Context, id string, status bool) error
}
