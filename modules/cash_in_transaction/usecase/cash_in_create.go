package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCashinTransaction) CreateCashinTranscation(ctx context.Context, req entity.CashinRequest, auth string) (resp entity.CashinTranscationResponse, err error) {
	ctx = logger.SetCtxData(ctx, map[string]interface{}{
		"invNumber": req.InvoiceNumber,
	})
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	product, err := s.companyProductService.GetCompanyProductByCodeAndCompanyId(ctx, req.CompanyProductCode, strconv.Itoa(company.ID))
	if err != nil {
		return
	}

	if !product.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "product is not active")
		logger.Error(ctx, err.Error())
		return
	}

	channelPayment, err := s.channelMappingRepo.GetChannelMappingByCode(ctx, req.PaymentMethodCode)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method not found")
		logger.Error(ctx, err.Error())
		return
	}

	if channelPayment.Capability != constants.CashInCapability {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment method does not support cash in transactions")
		logger.Error(ctx, err.Error())
		return
	}

	companyId := strconv.Itoa(company.ID)
	providerId := strconv.Itoa(int(channelPayment.PaymentProviderID))
	channelId := strconv.Itoa(int(channelPayment.PaymentChannelID))

	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err != nil {
		return
	}
	if !providerMapping.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "company payment provider is not active")
		logger.Error(ctx, err.Error())
		return
	}
	cashInCabalitty := strconv.Itoa(constants.CashInCapability)
	companyMapping, err := s.companyMappingRepo.GetCompanyProviderChannelMappingRequestBy(ctx, companyId, providerId, channelId, cashInCabalitty)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method not found in company")
		logger.Error(ctx, err.Error())
		return
	}
	if !companyMapping.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment channel is not active")
		logger.Error(ctx, err.Error())
		return
	}

	channelResp, err := s.channelService.GetPaymentChannelById(ctx, channelId)
	if err != nil {
		return
	}
	if !channelResp.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "channel status is not active")
		logger.Error(ctx, err.Error())
		return
	}
	providerResp, err := s.providerService.GetPaymentProviderById(ctx, providerId)
	if err != nil {
		return
	}
	if !providerResp.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment provider status is not active")
		logger.Error(ctx, err.Error())
		return
	}

	// get company product provider channel mappings
	productMapping, err := s.companyProductMappingService.GetOneByProductIDAndChannelID(ctx, product.ID, int(companyMapping.ID))
	if err != nil && err != gorm.ErrRecordNotFound {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error get product mapping %v", err))
		return
	}

	partners, err := s.partnerService.GetPartnerByCompanyId(ctx, companyId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	counter := 0
	for i := 0; i < len(req.Items); i++ {
		for k := 0; k < len(*partners); k++ {
			if req.Items[i].PartnerCode == (*partners)[k].Code {
				counter = counter + 1
			}
		}
		if counter == 0 {
			err = errors.SetErrorMessage(http.StatusNotFound, "partner not found in company")
			logger.Error(ctx, err.Error())
			return
		}
	}

	var totalAmountItems float64
	var transactionItems []domain.CashInTransactionItems
	for j := 0; j < len(req.Items); j++ {
		partnerRes, errRes := s.partnerService.GetPartnerByCode(ctx, req.Items[j].PartnerCode)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
			logger.Error(ctx, err.Error())
			return
		}
		if !partnerRes.Status {
			err = errors.SetErrorMessage(http.StatusBadRequest, "partner status is not active")
			logger.Error(ctx, err.Error())
			return
		}
		trxItem := domain.CashInTransactionItems{
			RefInvoiceNumber: req.Items[j].RefInvoiceNumber,
			PartnerId:        partnerRes.ID,
			ItemName:         req.Items[j].ItemName,
			Amount:           req.Items[j].Amount,
			Status:           constants.CashInItemStatusPending,
			Note:             req.Items[j].Note,
		}

		totalAmountItems = totalAmountItems + req.Items[j].Amount
		transactionItems = append(transactionItems, trxItem)
	}

	cashInTranscation := req.ConverToCashinTranscation(company.ID, product.ID, int(channelPayment.PaymentProviderID), int(channelPayment.PaymentChannelID), transactionItems)
	// voucher bersifat general, satu cash in, tidak beradasar item yg mana
	// jadi jumlah amount item = amount + voucher
	if (totalAmountItems - req.Voucher) != cashInTranscation.Total {
		err = errors.SetErrorMessage(http.StatusBadRequest, "total amount not valid")
		logger.Error(ctx, err.Error())
		return
	}

	cashInTranscation.Total = math.Round(cashInTranscation.Total)
	cashInTranscation.AdminFee = companyMapping.FeeFixValue + ((companyMapping.FeePercentage / 100) * req.Total)
	cashInTranscation.AdminFee = math.Round(cashInTranscation.AdminFee)
	if productMapping.Status {
		logger.Info(ctx, fmt.Sprintf("product channel active, use product mapping %+v", productMapping))
		cashInTranscation.ProductFee = productMapping.FeeFixValue + ((productMapping.FeePercentage / 100) * req.Total)
	} else {
		logger.Info(ctx, "product channel not active / not set, use product fee")
		cashInTranscation.ProductFee = product.FeeFixValue + ((product.FeePercentage / 100) * req.Total)
	}
	cashInTranscation.ProductFee = math.Round(cashInTranscation.ProductFee)

	if cashInTranscation.Total+cashInTranscation.ProductFee < 10000 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "total amount + admin fee must be more than Rp10.000")
		logger.Error(ctx, err.Error())
		return
	}

	if cashInTranscation.Total+cashInTranscation.ProductFee > channelPayment.MaxTransaction {
		err = errors.SetErrorMessage(http.StatusBadRequest, "total transaction amount exceed limit")
		logger.Error(ctx, err.Error())
		return
	}

	// company product fee
	cashInTranscation.CompanyProductFee = product.CashoutFeeFixValue + ((product.CashoutFeePercentage / 100) * req.Total)
	cashInTranscation.CompanyProductFee = math.Round(cashInTranscation.CompanyProductFee)

	processAt := time.Now()
	expiredTime := processAt.Add(time.Duration(companyMapping.ExpiredTime) * time.Second)
	cashInTranscation.ExpiredAt = &expiredTime
	reqTransaction := entity.CashinTransactionProcess{
		CashinTransaction: cashInTranscation,
		PaymentChannel: domain.PaymentChannels{
			ID:                   channelResp.ID,
			Name:                 channelResp.Name,
			Status:               channelResp.Status,
			PaymentChannelTypeId: channelResp.PaymentChannelTypeId,
		},
		CompanyMapping:      *companyMapping,
		ProviderName:        providerResp.Name,
		ChannelName:         channelResp.Name,
		ProviderChannelCode: channelPayment.ProviderChannelCode,
		ProcessAt:           processAt,
		ExpiredTime:         expiredTime,
		Credential:          providerMapping.ProviderSecrets,
		Description:         req.Description,
		Command:             entity.CommandCreateTransaction,
		MobileNumber:        req.MobileNumber,
		SuccessRedirectUrl:  req.SuccessRedirectUrl,
		FailureRedirectUrl:  req.FailureRedirectUrl,
	}

	resp, err = s.transactionProviderProcess(ctx, &reqTransaction)
	if err != nil {
		return
	}

	respByte, _ := json.Marshal(resp)
	keyCache := fmt.Sprintf("trx-%v-%v-%v", resp.ID, providerId, channelId)
	s.cache.Set(ctx, keyCache, string(respByte), time.Duration(companyMapping.ExpiredTime)*time.Second)

	return
}
