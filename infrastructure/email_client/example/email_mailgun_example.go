package main

import (
	"context"
	"log"

	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email"
)

type Parameters struct {
	CompanyID   int64  `json:"company_id"`
	PartnerID   int64  `json:"partner_id"`
	BatchNumber string `json:"batch_number"`
}

func main() {
	e := email.NewMailgunEmailProvider("demo-finance.nusatek.id", "**************************************************", "2107485a67d19cac412898ca778c9114")

	parameters := Parameters{
		CompanyID:   9,
		PartnerID:   4,
		BatchNumber: "**********",
	}

	result, err := e.Send(context.Background(), "<EMAIL>", "Test", "Test", []string{"<EMAIL>"}, email.WithParameters(parameters))
	if err != nil {
		log.Println(err)
	}

	log.Println(result)
}
