package handler

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/usecase"
	cashOut "repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/model"
	"repo.nusatek.id/nusatek/payment/utils/response"
	"repo.nusatek.id/nusatek/payment/utils/str"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

type CashinHandler struct {
	cashinUsecase  usecase.CashinTransactionUsecase
	cashoutUsecase cashOut.CashOutUseCase
}

func NewHandler(cashinUsecase usecase.CashinTransactionUsecase, cashoutUsecase cashOut.CashOutUseCase) *CashinHandler {
	return &CashinHandler{cashinUsecase, cashoutUsecase}
}

func (h *CashinHandler) CreateCashinTransactionAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var req entity.CashinRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	resp, err := h.cashinUsecase.CreateCashinTranscation(c.UserContext(), req, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) UpdateCashinTransactionAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	id := c.Params("id")

	var req entity.CashinUpdateTransaction
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	resp, err := h.cashinUsecase.UpdateTranscactionCashIn(c.UserContext(), req, id, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) SearchCashinTranscation(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if paginate.FromDate != "" || paginate.ToDate != "" {
		err = utils.ValidateStruct(paginate)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return errors.ErrorHandle(c, err)
		}
	}
	p := entity.CashinListParam{
		Pagination: paginate,
	}
	p.Enrich(c)

	resp, totalData, err := h.cashinUsecase.SearchCashinTranscation(c.UserContext(), p)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *CashinHandler) GetByIDDetail(c *fiber.Ctx) (err error) {
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	resp, err := h.cashinUsecase.GetByIDDetail(c.UserContext(), id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	var manualPaymentAt *string

	if resp.ManualPaymentAt != nil {
		manualPaymentAt = resp.ManualPaymentAt.NullString(time.RFC3339)
	}

	res := entity.CashInDetailRes{
		ID:                            resp.ID,
		CompanyID:                     resp.CompanyID,
		CompanyName:                   resp.CompanyName,
		ProductName:                   resp.ProductName,
		CompanyProductID:              resp.CompanyProductID,
		InvoiceNumber:                 resp.InvoiceNumber,
		PaymentAt:                     resp.PaymentAt.String(time.RFC3339),
		ManualPaymentAt:               manualPaymentAt,
		CustomerName:                  resp.CustomerName,
		CustomerPhone:                 resp.CustomerPhone,
		CustomerEmail:                 resp.CustomerEmail,
		PaymentProviderID:             resp.PaymentProviderID,
		PaymentProviderName:           resp.PaymentProviderName,
		PaymentChannelID:              resp.PaymentChannelID,
		PaymentChannelName:            resp.PaymentChannelName,
		PaymentChannelTypePaymentType: resp.PaymentChannelTypePaymentType,
		Total:                         resp.Total,
		AdminFee:                      resp.AdminFee,
		Discount:                      resp.Discount,
		Voucher:                       resp.Voucher,
		PgDeliveryFee:                 resp.PgDeliveryFee,
		PaymentStatus:                 resp.PaymentStatus,
		Status:                        resp.Status,
		CreatedAt:                     resp.CreatedAt,
		UpdatedAt:                     resp.UpdatedAt,
		ProductFee:                    resp.ProductFee,
		ExpiredAt:                     resp.ExpiredAt,
		SubTotalItem:                  resp.GetSubTotalItem(),
		CashInTotal:                   resp.GetCashInTotal(),
		GrandTotal:                    resp.GetGrandTotal(),
		CompanyProductCashoutFee:      resp.CompanyProductFee, //utils.GetCompProductCashoutFee(resp.GetSubTotalItem(), resp.CompanyProductCashoutFeeFixValue, resp.CompanyProductCashoutFeePercentage),
	}

	return response.HandleSuccess(c, res)
}

func (h *CashinHandler) GetCashinActivityLog(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	id := c.Params("id")
	resp, totalData, err := h.cashinUsecase.CashinPaymentActivityLogByTransactionID(c.UserContext(), paginate, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *CashinHandler) GetListItemsByTransactionID(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	id, _ := strconv.Atoi(c.Params("id"))
	resp, totalData, err := h.cashinUsecase.GetListItemsByTransactionID(c.UserContext(), paginate, int64(id))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, entity.FromDomainsCashInTransactionItemComplete(resp))
}

func (h *CashinHandler) CheckStatusCashinAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	id := c.Params("id")

	resp, err := h.cashinUsecase.CheckStatusCashinAPI(c.UserContext(), id, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) GetListCashinTransactionAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if paginate.FromDate != "" || paginate.ToDate != "" {
		err = utils.ValidateStruct(paginate)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return errors.ErrorHandle(c, err)
		}
	}
	p := entity.CashinListParam{
		Pagination: paginate,
	}
	p.Enrich(c)

	resp, totalData, err := h.cashinUsecase.GetListCashinTransactionAPI(c.UserContext(), p, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *CashinHandler) RefundCashinTransactionAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var req entity.CashinRefundRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	resp, err := h.cashinUsecase.RefundCashinTranscation(c.UserContext(), req, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) RefundPaymentCashinTransaction(c *fiber.Ctx) (err error) {
	var req entity.CashinBulkRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = h.cashinUsecase.RefundPaymentCashinTranscation(c.UserContext(), req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, nil)
}

func (h *CashinHandler) BatchingCashInToCashOut() {
	ctx := context.TODO()
	err := h.cashinUsecase.BatchingCashInToCashOut(ctx)
	if err != nil {
		logger.Error(ctx, "BatchingCashInToCashOut", logger.Err(err))
	} else {
		logger.Info(ctx, "Success")
	}
}

func (h *CashinHandler) BuildCashOutTransaction() {
	ctx := context.Background()
	ctx = logger.SetCorrelationId(ctx, uuid.New().String())
	err := h.cashinUsecase.BuildCashOutTransaction(ctx)
	if err != nil {
		logger.Error(ctx, "BuildCashOutTransaction", logger.Err(err))
	} else {
		logger.Info(ctx, "Success")
	}
}

func (h *CashinHandler) CheckPendingCashInTransaction() {
	ctx := context.Background()
	ctx = logger.SetCorrelationId(ctx, uuid.New().String())
	err := h.cashinUsecase.CheckPendingCashInTransaction(ctx)
	if err != nil {
		logger.Error(ctx, "CheckPendingCashInTransaction", logger.Err(err))
	} else {
		logger.Info(ctx, "Success")
	}
}

func (h *CashinHandler) Export(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	req := new(domain.CashinExportReq)
	req.DateFilterType = c.Query("date_filter_type")
	req.StartDatetime, err = timepkg.StringDateToTime(c.Query("start_date"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid start date")
		return errors.ErrorHandle(c, err)
	}
	req.EndDatetime, err = timepkg.StringDateToTime(c.Query("end_date"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid end date")
		return errors.ErrorHandle(c, err)
	}
	req.PaymentProviderID = str.SplitToInts(c.Query("payment_provider_id"), ",")
	req.PaymentChannelID = str.SplitToInts(c.Query("payment_channel_id"), ",")
	req.PartnerID = str.SplitToInts(c.Query("partner_id"), ",")
	req.CompanyID, _ = strconv.Atoi(c.Query("company_id"))

	qStatus := c.Query("statuses")
	if len(qStatus) > 0 {
		req.Statuses = strings.Split(qStatus, ",")
	}

	filename, err := h.cashinUsecase.Export(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	defer func(filename string) {
		err := os.Remove(filename)
		if err != nil {
			fmt.Println("error delete file export", err)
		}
	}(filename)

	return c.SendFile(filename)
}

func (h *CashinHandler) CheckUnpaidBillVA(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var req entity.CheckUnpaidBillVARequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	resp, err := h.cashinUsecase.CheckUnpaidBillVA(c.UserContext(), &req, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) GetPaymentInstructions(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	invNumber := c.Query("inv_number")
	if len(invNumber) == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "inv number empty")
		return errors.ErrorHandle(c, err)
	}

	resp, err := h.cashinUsecase.GetPaymentInstructions(c.UserContext(), invNumber, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashinHandler) RetryCallback(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	cashInID, _ := c.ParamsInt("id")

	err = h.cashinUsecase.RetryCallback(ctx, cashInID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, "")
}

func (h *CashinHandler) ManualPayment(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	cashInID, _ := c.ParamsInt("id")

	var req entity.ManualPaymentRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}

	user := c.Locals("token").(model.JwtToken)

	req.Id = cashInID
	req.UpdatedBy = user.UserId

	err = h.cashinUsecase.ManualPayment(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, nil)
}

func (h *CashinHandler) CancelCashinTransactionAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	provider_transcation_id := c.Params("provider_transcation_id")

	var req entity.CashInCancelationRequest
	req.ProviderTransactionID = provider_transcation_id

	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	if err := h.cashinUsecase.CancelCashInTransaction(c.UserContext(), &req, string(auth)); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"message": "success",
	})
}

func (h *CashinHandler) SwitchCashinPaymentChannelAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	provider_transcation_id := c.Params("provider_transcation_id")

	var req entity.CashInChangeChannelRequest
	req.ProviderTransactionID = provider_transcation_id

	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	resp, err := h.cashinUsecase.SwitchCashinPaymentChannel(c.UserContext(), &req, string(auth))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, resp)
}
