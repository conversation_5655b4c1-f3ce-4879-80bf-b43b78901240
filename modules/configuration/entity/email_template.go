package entity

import (
	"time"

	"github.com/lib/pq"
	"repo.nusatek.id/nusatek/payment/domain"
)

type Template struct {
	ID         int               `gorm:"primaryKey" json:"id"`
	CompanyID  int64             `json:"company_id"`
	Company    domain.Companies  `gorm:"foreignKey:ID;references:CompanyID" json:"company"`
	PartnerIds []int64           `gorm:"-" json:"partner_ids,omitempty"`
	Partners   []domain.Partners `gorm:"many2many:template_partners;foreignKey:ID;joinForeignKey:TemplateID;References:ID;joinReferences:PartnerID" json:"partners"`
	EmailCcs   pq.StringArray    `gorm:"type:text[]" json:"email_cc"`
	Subject    string            `json:"subject"`
	Content    string            `json:"content"`
	IsActive   bool              `json:"is_active"`
	IsDefault  bool              `json:"is_default"`
	CreatedAt  time.Time         `json:"created_at"`
	UpdatedAt  time.Time         `json:"updated_at"`
}

func (t *Template) TableName() string {
	return "email_templates"
}

type TemplateRequest struct {
	ID         int64    `json:"id,omitempty"`
	CompanyID  int64    `json:"company_id" validate:"required"`
	PartnerIds []int64  `json:"partner_ids" validate:"required"`
	EmailCcs   []string `json:"email_cc"`
	Subject    string   `json:"subject" validate:"required"`
	Content    string   `json:"content" validate:"required"`
	IsActive   bool     `json:"is_active"`
	IsDefault  bool     `json:"is_default"`
}
