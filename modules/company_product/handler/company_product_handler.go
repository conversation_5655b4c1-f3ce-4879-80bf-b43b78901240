package handler

import (
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type CompanyProductHandler struct {
	companyProductService usecase.CompanyProductUsecase
}

func NewHandler(companyProductService usecase.CompanyProductUsecase) *CompanyProductHandler {
	return &CompanyProductHandler{companyProductService}
}

func (h *CompanyProductHandler) CreateCompanyProduct(c *fiber.Ctx) (err error) {
	var req domain.CompanyProducts
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.companyProductService.CreateCompanyProduct(c.UserContext(), &req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, req)
}

func (h *CompanyProductHandler) GetCompanyProductByID(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.companyProductService.GetCompanyProductById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *CompanyProductHandler) UpdateCompanyProduct(c *fiber.Ctx) (err error) {
	var req domain.CompanyProducts
	id, _ := c.ParamsInt("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	req.ID = id
	err = h.companyProductService.UpdateCompanyProduct(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}
func (h *CompanyProductHandler) UpdateCompanyProductStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id := c.Params("id")
	req := new(request)

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = h.companyProductService.UpdateCompanyProductStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}

func (h *CompanyProductHandler) DeleteCompanyProduct(c *fiber.Ctx) (err error) {
	id, _ := c.ParamsInt("id")
	err = h.companyProductService.DeleteCompanyProduct(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *CompanyProductHandler) SearchCompanyProduct(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}
	tr := strings.ReplaceAll(paginate.Value, " ", "&")
	paginate.Value = tr
	resp, totalData, err := h.companyProductService.SearchCompanyProduct(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *CompanyProductHandler) GetCompanyProductListAll(c *fiber.Ctx) (err error) {
	resp, _, err := h.companyProductService.GetCompanyProductListAll(c.UserContext())
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}
