CREATE TABLE snap_clients (
	id TEXT NOT NULL PRIMARY KEY, -- client_id
	name TEXT NOT NULL, -- client_name
	secret TEXT NOT NULL, -- client_secret
	public_key TEXT NOT NULL,
	deleted_at TIMESTAMPTZ NULL
)

-- inquiryRequestId (wajib di store)

-- paymentRequestId: (jika dari inquiry , nilai harus sama dengan inquiryRequestId, bisa jadi tidak berasal dari inquiry)
-- - cara tahu dari inquiry atau tidak ? perlu simpan inquiryRequestId ke id transaksi tiap client, waktu payment perlu get 
--   last inquiry id transaksi di client tersebut, jika ada maka lakukan validasi ?

-- get status butuh
-- - inquiryRequestId (Mandatory)
-- - paymentRequestId (Optional)

-- perlu simpan relasi external id dan payment request id
-- for save unique request identifier for virtual account inquiry and payment from clients
CREATE TABLE snap_va_request_ids (
	id SERIAL NOT NULL PRIMARY KEY,
	client_id TEXT NOT NULL REFERENCES snap_clients(id),
	request_id TEXT NOT NULL, -- get from inquiry request id
	va_number TEXT NOT NULL,
	cash_in_transaction_id INT NOT NULL REFERENCES cash_in_transactions(id),
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- used for get last request id
CREATE INDEX idx_snap_request_ids_get_last_row ON snap_va_request_ids(cash_in_transaction_id,client_id,va_number,id desc);

ALTER TABLE company_payment_provider_mappings ALTER COLUMN provider_secrets TYPE TEXT; -- for saving secret credential 

-- mapping for customer fixed va and cash_in_transactions
-- CREATE TABLE fva_customer_cash_in_transactions (
-- 	id SERIAL NOT NULL PRIMARY KEY,
-- 	fva_customer_id INT NOT NULL REFERENCES fva_customers(id),
-- 	cash_in_transaction_id INT NOT NULL REFERENCES cash_in_transactions(id),
-- 	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- 	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- 	deleted_at TIMESTAMPTZ NULL
-- );

-- CREATE INDEX idx_fva_customer_cash_in_transactions_get_last_by_fva_customer_id ON fva_customer_cash_in_transactions(cash_in_transaction_id, deleted_at NULLS FIRST, id desc);


-- CREATE TABLE snap_cashin_log_requests (
-- 	id SERIAL NOT NULL PRIMARY KEY,
-- 	client_id TEXT NOT NULL REFERENCES snap_clients(id),
-- 	external_id TEXT NOT NULL,
-- 	"type" TEXT NOT NULL, -- inquiry / payment
-- 	request_id TEXT NOT NULL,
-- 	cash_in_transaction_id INT NOT NULL REFERENCES cash_in_transactions(id),
-- 	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- 	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- 	deleted_at TIMESTAMPTZ NULL
-- );

-- ALTER TABLE company_payment_provider_channel_mappings ADD COLUMN snap_comp_code;

