package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCompanyProduct) CreateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error) {
	err = s.companyProductRepo.CreateCompanyProduct(ctx, req)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheCompanyProductCode(ctx, req)
	s.setCacheCompanyProductId(ctx, req)
	return
}

func (s *defaultCompanyProduct) GetCompanyProductByCode(ctx context.Context, code string) (resp *domain.CompanyProducts, err error) {
	resp, err = s.getCacheCompanyProductCode(ctx, code)
	return
}

func (s *defaultCompanyProduct) GetCompanyProductById(ctx context.Context, id string) (resp *domain.CompanyProducts, err error) {
	resp, err = s.getCacheCompanyProductId(ctx, id)
	if err != nil {
		return
	}
	return
}

func (s *defaultCompanyProduct) SelectByIds(ctx context.Context, ids []int) (resp []domain.CompanyProducts, err error) {

	return s.companyProductRepo.SelectByIds(ctx, ids)
}

func (s *defaultCompanyProduct) UpdateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error) {
	product, err := s.companyProductRepo.GetCompanyProductById(ctx, req.ID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "CompanyProduct Not Found")
		logger.Error(ctx, err.Error())
		return
	}
	companyID := strconv.Itoa(req.CompanyID)
	_, err = s.companyRepo.GetCompanyById(ctx, companyID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "CompanyID not found")
		logger.Error(ctx, err.Error())
		return
	}
	idCompany := req.ID
	req.CreatedAt = product.CreatedAt
	req.ID = idCompany

	err = s.companyProductRepo.UpdateCompanyProduct(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheCompanyProductCode(ctx, req)
	s.setCacheCompanyProductId(ctx, req)

	return
}

func (s *defaultCompanyProduct) DeleteCompanyProduct(ctx context.Context, id int) (err error) {
	product, err := s.companyProductRepo.GetCompanyProductById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
		logger.Error(ctx, err.Error())
		return
	}
	err = s.companyProductRepo.DeleteCompanyProduct(ctx, product)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheCompanyProductCode(ctx, product.Code)
	s.deleteCacheCompanyProductId(ctx, strconv.Itoa(product.ID))
	return
}

func (s *defaultCompanyProduct) GetCompanyProductListAll(ctx context.Context) (resp *[]entity.CompanyProductShow, totalData int64, err error) {
	resp, totalData, err = s.companyProductRepo.GetCompanyProductListAll(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyProduct) GetCompanyProductByCodeAndCompanyId(ctx context.Context, productCode, companyId string) (resp *domain.CompanyProducts, err error) {
	product, err := s.GetCompanyProductByCode(ctx, productCode)
	if err != nil {
		return
	}
	if strconv.Itoa(product.CompanyID) != companyId {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found in company")
		logger.Error(ctx, err.Error())
		return
	}

	resp = product
	return
}

func (s *defaultCompanyProduct) GetCompanyProductByIdAndCompanyId(ctx context.Context, companyProductId, companyId string) (resp *domain.CompanyProducts, err error) {
	product, err := s.GetCompanyProductById(ctx, companyProductId)
	if err != nil {
		return
	}
	if strconv.Itoa(product.CompanyID) != companyId {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found in company")
		logger.Error(ctx, err.Error())
		return
	}

	resp = product
	return
}

func (s *defaultCompanyProduct) SearchCompanyProduct(ctx context.Context, paginate utils.Pagination) (resp *[]entity.CompanyProductShow, totalData int64, err error) {
	resp, totalData, err = s.companyProductRepo.SearchCompanyProduct(ctx, paginate)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyProduct) UpdateCompanyProductStatus(ctx context.Context, id string, status bool) (err error) {
	productId, _ := strconv.Atoi(id)
	product, err := s.companyProductRepo.GetCompanyProductById(ctx, productId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
		logger.Error(ctx, err.Error())
		return
	}

	product.Status = status
	err = s.companyProductRepo.UpdateCompanyProduct(ctx, product)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheCompanyProductCode(ctx, product)
	s.setCacheCompanyProductId(ctx, product)
	return
}

func (s *defaultCompanyProduct) getCacheCompanyProductCode(ctx context.Context, productCode string) (resp *domain.CompanyProducts, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductCode, productCode)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil && errRes != redis.Nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.companyProductRepo.GetCompanyProductByCode(ctx, productCode)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCompanyProductCode(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.CompanyProducts
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCompanyProduct) setCacheCompanyProductCode(ctx context.Context, req *domain.CompanyProducts) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductCode, req.Code)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyProduct) deleteCacheCompanyProductCode(ctx context.Context, productCode string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductCode, productCode)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyProduct) getCacheCompanyProductId(ctx context.Context, productId string) (resp *domain.CompanyProducts, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductId, productId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil && errRes != redis.Nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		productIdInt, _ := strconv.Atoi(productId)
		prod, errRes := s.companyProductRepo.GetCompanyProductById(ctx, productIdInt)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCompanyProductId(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.CompanyProducts
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCompanyProduct) setCacheCompanyProductId(ctx context.Context, req *domain.CompanyProducts) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductId, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyProduct) deleteCacheCompanyProductId(ctx context.Context, productId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanyProductId, productId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}
