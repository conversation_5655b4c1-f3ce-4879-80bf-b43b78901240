package usecase

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/partner/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *partnerUseCase) CreatePartnerAPI(ctx context.Context, req *domain.Partners, auth string) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	_, err = s.channelRepo.GetPaymentChannelById(ctx, strconv.Itoa(req.PaymentChannelId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}

	list, err := s.companyMappingRepo.GetListAllCompanyProviderMappingChannelByCompanyID(ctx, strconv.Itoa(company.ID))
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	counter := 0
	for i := 0; i < len(*list); i++ {
		if (*list)[i].ID == req.PaymentChannelId {
			counter = counter + 1
		}
	}

	if counter == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment channel not found in company for partner cashout")
		logger.Error(ctx, err.Error())
		return
	}

	tx := s.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	req.CompanyID = company.ID
	err = s.partnerRepository.CreatePartner(ctx, req, tx)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in partner code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	tx.Commit()
	_ = s.setCachePartner(ctx, req)
	_, _ = s.setCachePartnerTags(ctx, strconv.Itoa(req.ID))
	return
}

func (s *partnerUseCase) UpdatePartnerAPI(ctx context.Context, req *domain.Partners, id, auth string) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	partner, err := s.partnerRepository.GetPartnerByID(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
		logger.Error(ctx, err.Error())
		return
	}

	if company.ID != partner.CompanyID {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "company does not have access to this partner")
		logger.Error(ctx, err.Error())
		return
	}

	channel, err := s.channelRepo.GetPaymentChannelById(ctx, strconv.Itoa(req.PaymentChannelId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}

	list, err := s.companyMappingRepo.GetListAllCompanyProviderMappingChannelByCompanyID(ctx, strconv.Itoa(company.ID))
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	counter := 0
	for i := 0; i < len(*list); i++ {
		if (*list)[i].ID == req.PaymentChannelId {
			counter = counter + 1
		}
	}

	if counter == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment channel not found in company for partner cashout")
		logger.Error(ctx, err.Error())
		return
	}

	req.ID = partner.ID
	req.CompanyID = company.ID
	req.CreatedAt = partner.CreatedAt
	tx := s.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	err = s.partnerRepository.UpdatePartner(ctx, req, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	reqTrx := domain.CashOutTransactions{
		PartnerName:          partner.Name,
		PartnerAccountName:   partner.ArAccount,
		PartnerAccountNumber: partner.ApAccount,
		PartnerBankName:      channel.Name,
		PaymentChannelID:     channel.ID,
	}
	err = s.partnerRepository.UpdatePaymentChannelPartnerInCashOutTrx(ctx, &reqTrx, strconv.Itoa(partner.ID), constants.CashOutStatusReady, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "failed update payment channel partner in cash out trx", logger.Err(err))
		return
	}

	tx.Commit()
	_ = s.setCachePartner(ctx, req)
	_, _ = s.setCachePartnerTags(ctx, strconv.Itoa(req.ID))
	return
}

func (s *partnerUseCase) GetListPartnerAPI(ctx context.Context, paginate utils.Pagination, auth string) (resp *[]entity.PartnersResponseAPI, totalData int64, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	param := map[string]interface{}{}
	param["key"] = "company_id"
	param["value"] = company.ID
	param["search"] = paginate.Search
	param["status"] = paginate.Status

	orderParam := map[string]interface{}{
		"start": (paginate.Page - 1) * paginate.Limit,
		"limit": paginate.Limit,
	}

	partnerList, total, errRes := s.partnerRepository.List(ctx, param, orderParam)
	if errRes != nil {
		err = errors.SetError(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var partners []entity.PartnersResponseAPI
	for i := 0; i < len(*partnerList); i++ {
		partner := entity.PartnersResponseAPI{
			ID:                 (*partnerList)[i].ID,
			CompanyID:          company.ID,
			Name:               (*partnerList)[i].Partner,
			Phone:              (*partnerList)[i].Phone,
			ArAccount:          (*partnerList)[i].AccountReceivable,
			ApAccount:          (*partnerList)[i].BankAccountNumber,
			FeePercentage:      (*partnerList)[i].FeePercentage,
			FeeFixValue:        (*partnerList)[i].FeeFixValue,
			Sla:                (*partnerList)[i].SLA,
			Status:             (*partnerList)[i].Status,
			Code:               (*partnerList)[i].PartnerCode,
			Email:              (*partnerList)[i].Email,
			PaymentChannelId:   (*partnerList)[i].PaymentChannelID,
			PaymentChannelName: (*partnerList)[i].PaymentChannelName,
			FeeBehind:          (*partnerList)[i].FeeBehind,
		}

		partners = append(partners, partner)
	}

	resp = &partners
	totalData = total
	return
}

func (s *partnerUseCase) GetPartnerCodeAPI(ctx context.Context, auth, code string) (resp *entity.PartnersResponseAPI, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	data, err := s.partnerRepository.GetPartnerByCode(ctx, code)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		} else {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		}
		logger.Error(ctx, err.Error())
		return
	}

	resp = &entity.PartnersResponseAPI{
		ID:               data.ID,
		CompanyID:        company.ID,
		Name:             data.Name,
		Phone:            data.Phone,
		ArAccount:        data.ArAccount,
		ApAccount:        data.ApAccount,
		FeePercentage:    data.FeePercentage,
		FeeFixValue:      data.FeeFixValue,
		Sla:              data.Sla,
		Status:           data.Status,
		Code:             data.Code,
		Email:            data.Email,
		PaymentChannelId: data.PaymentChannelId,
		FeeBehind:        data.FeeBehind,
	}

	if resp.CompanyID != company.ID {
		err = errors.SetError(http.StatusInternalServerError, "invalid_grant")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *partnerUseCase) DeletePartnerApi(ctx context.Context, id, auth string) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	partner, err := s.partnerRepository.GetPartnerByID(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
		logger.Error(ctx, err.Error())
		return
	}

	if company.ID != partner.CompanyID {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "company does not have access to this partner")
		logger.Error(ctx, err.Error())
		return
	}
	err = s.partnerRepository.DeletePartner(ctx, partner)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.deleteCacheParner(ctx, partner.Code)
	_ = s.deleteCachePartnerTags(ctx, strconv.Itoa(partner.ID))
	return
}
