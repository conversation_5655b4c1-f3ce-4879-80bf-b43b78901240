package entity

import (
	"encoding/json"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

type OTP struct {
	OTPCode              string `json:"otp_code" validate:"numeric,len=6"`
	CashOutTransactionID []int  `json:"cash_out_transaction_id"`
	Reason               string `json:"reason"`
}

type FeePaymentStatusRequest struct {
	CashOutTranscationID []int   `json:"cash_out_transaction_id"`
	PartnerId            int     `json:"partner_id"`
	TotalFee             float64 `json:"total_fee"`
	OTPCode              string  `json:"otp_code" validate:"numeric,len=6"`
}

type RequestDisbursement struct {
	CashoutId  int    `json:"cash_out_id" validate:"required,numeric"`
	ProviderId int    `json:"provider_id"`
	UserEmail  string `json:"user_email" validate:"required"`
}

type ListItemCashInBatchNumber struct {
	ID                  int     `json:"id"`
	ItemName            string  `json:"item_name"`
	Note                string  `json:"note"`
	ItemAmount          float64 `json:"item_amount"`
	ItemGrandTotal      float64 `json:"item_grand_total"`
	InvoiceNumber       string  `json:"invoice_number"`
	RefInvoiceNumber    string  `json:"ref_invoice_number"`
	ProductFee          float64 `json:"product_fee"`
	CompanyProductFee   float64 `json:"company_product_fee"`
	PaymentProviderName string  `json:"payment_provider_name"`
	PaymentChannelName  string  `json:"payment_channel_name"`
	SettlementDate      string  `json:"settlement_date"`
}

type UpdateRequest struct {
	ProviderId  int    `json:"provider_id" validate:"required"`
	Scheduler   bool   `json:"scheduler"`
	SchedulerAt string `json:"scheduler_at"`
}

type AddCashOutItemsByIDsRequest struct {
	CashOutID     int   `json:"cash_out_id" validate:"required"`
	CashInItemIDs []int `json:"cash_in_item_ids" validate:"required"`
}
type DeleteCashOutItemsByIDsRequest struct {
	CashOutID      int   `json:"cash_out_id" validate:"required"`
	CashOutItemIDs []int `json:"cash_out_item_ids" validate:"required"`
}

type CashInTransactionItemsOutstandingRes struct {
	InvoiceNumber       string     `json:"invoice_number"`
	PaymentChannelName  string     `json:"payment_channel_name"`
	PaymentProviderName string     `json:"payment_provider_name"`
	SettlementDate      *time.Time `json:"settlement_date"`
	ID                  int        `json:"id"`
	CashInTransactionID int        `json:"cash_in_transaction_id"`
	CustomerName        string     `json:"customer_name"`
	TotalAmount         float64    `json:"total_amount"`
	PartnerId           int        `json:"partner_id"`
	PartnerName         string     `json:"partner_name"`
	ItemName            string     `json:"item_name"`
	Amount              float64    `json:"amount"`
	Status              string     `json:"status"`
	IsCashout           bool       `json:"is_cashout"`
	SlaDate             *time.Time `json:"sla_date"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
}

type CashoutDetailRes struct {
	ID                     int64      `json:"id"`
	BatchNumber            string     `json:"batch_number"`
	PartnerName            string     `json:"partner_name"`
	PartnerAccountNumber   string     `json:"partner_account_number"`
	PartnerAccountName     string     `json:"partner_account_name"`
	PartnerBankName        string     `json:"partner_bank_name"`
	SubTotal               float64    `json:"sub_total"`
	Total                  float64    `json:"total"`
	GrandTotal             float64    `json:"grand_total"`
	AdminFee               float64    `json:"admin_fee"`
	PlatformFee            float64    `json:"platform_fee"`
	TransactionFee         float64    `json:"transaction_fee"`
	TotalDiscount          float64    `json:"total_discount"`
	TotalVoucher           float64    `json:"total_voucher"`
	TotalDisbursement      float64    `json:"total_disbursement"`
	Status                 string     `json:"status"`
	CreatedAt              string     `json:"created_at"`
	SettlementDate         string     `json:"settlement_date"`
	ProviderName           string     `json:"provider_name"`
	ProviderID             int64      `json:"provider_id"`
	CompanyName            string     `json:"company_name"`
	ChannelName            string     `json:"channel_name"`
	ChannelID              int64      `json:"channel_id"`
	CompanyID              int64      `json:"company_id"`
	DisbursementScheduleAt string     `json:"disbursement_schedule_at"`
	RequestedBy            string     `json:"requested_by"`
	RequestedAt            *time.Time `json:"requested_at"`
	ApprovedBy             string     `json:"approved_by"`
	ApprovedAt             *time.Time `json:"approved_at"`
	RejectedBy             string     `json:"rejected_by"`
	RejectedAt             *time.Time `json:"rejected_at"`
	FeePaymentStatus       bool       `json:"fee_payment_status"`
	PartnerId              int        `json:"partner_id"`
	CompanyProductId       int        `json:"company_product_id"`
	CompanyProductName     string     `json:"company_product_name"`
	CompanyProductCode     string     `json:"company_product_code"`
}

func FromDomainCashoutDetail(d domain.CashOutTransactionDetail) CashoutDetailRes {
	res := CashoutDetailRes{
		ID:                   d.ID,
		BatchNumber:          d.BatchNumber,
		PartnerName:          d.PartnerName,
		PartnerAccountNumber: d.PartnerAccountNumber,
		PartnerAccountName:   d.PartnerAccountName,
		PartnerBankName:      d.PartnerBankName,
		Total:                d.Total,
		AdminFee:             d.AdminFee,
		PlatformFee:          d.PlatformFee,
		TransactionFee:       d.CashInTotalAmount.TransactionFee,
		TotalDiscount:        d.CashInTotalAmount.TotalDiscount,
		TotalVoucher:         d.CashInTotalAmount.TotalVoucher,
		Status:               d.Status,
		CreatedAt:            d.CreatedAt,
		ProviderName:         d.ProviderName,
		ProviderID:           d.ProviderID,
		CompanyName:          d.CompanyName,
		ChannelName:          d.ChannelName,
		ChannelID:            d.ChannelID,
		CompanyID:            d.CompanyID,
		RequestedBy:          d.RequestedByName,
		RequestedAt:          d.RequestedAt,
		ApprovedAt:           d.ApprovedAt,
		FeePaymentStatus:     d.FeePaymentStatus,
		PartnerId:            d.PartnerId,
		CompanyProductId:     d.CompanyProductId,
		CompanyProductName:   d.CompanyProductName,
		CompanyProductCode:   d.CompanyProductCode,
	}

	res.SubTotal = d.Total

	// wiht partner fee behind logic
	// used for UI logic hiding admin fee or not
	// the real calculation is done when "RequestDisbursement" modules/cash_out_transaction/usecase/usecase.go
	// this logic base on this function "RequestDisbursement" modules/cash_out_transaction/usecase/usecase.go
	feePaymentStatus := false
	if d.Status == constants.CashOutStatusReady {
		feePaymentStatus = !d.PartnerFeeBehind
	} else {
		feePaymentStatus = d.FeePaymentStatus
	}

	if !feePaymentStatus {
		res.AdminFee = 0 // hide admin fee if PartnerFeeBehind
	}

	res.GrandTotal = d.Total + d.CashInTotalAmount.TransactionFee
	res.TotalDisbursement = d.Total - res.AdminFee // this only UI logic calculation, the real calculation is done when "RequestDisbursement" modules/cash_out_transaction/usecase/usecase.go
	if d.Status == constants.CashOutStatusRejected {
		res.ApprovedBy = ""
		res.ApprovedAt = nil
		res.RejectedBy = d.ApprovedByName
		res.RejectedAt = d.ApprovedAt
	}

	res.DisbursementScheduleAt = d.DisbursementScheduleAt.String(time.RFC3339)

	res.SettlementDate = d.PaymentAt.String(time.RFC3339)

	return res
}

type EmailParametersPayload struct {
	CompanyID   int64  `json:"company_id"`
	PartnerID   int64  `json:"partner_id"`
	BatchNumber string `json:"batch_number"`
}

type EmailLog struct {
	ID          int64           `json:"id"`
	CashoutID   int64           `json:"cashout_id"`
	CompanyID   int64           `json:"company_id"`
	PartnerID   int64           `json:"partner_id"`
	BatchNumber string          `json:"batch_number"`
	Email       string          `json:"email"`
	MessageBody json.RawMessage `json:"message_body"`
	SendAt      time.Time       `json:"send_at"`
	LastStatus  string          `json:"last_status"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

func (e EmailLog) TableName() string {
	return "cashout_email_logs"
}

type ManualDoneRequest struct {
	Status string `json:"status"`
	Reason string `json:"reason" validate:"required,min=5"`
}
