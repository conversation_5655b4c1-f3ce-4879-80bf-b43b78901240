package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type CompanyMappingHandler struct {
	companyMappingService usecase.CompanyMappingUsecase
}

func NewHandler(companyMappingUsecase usecase.CompanyMappingUsecase) *CompanyMappingHandler {
	return &CompanyMappingHandler{companyMappingUsecase}
}

// List Provider Channel Mapping
func (pp *CompanyMappingHandler) ListChannelMapping(c *fiber.Ctx) (err error) {
	type params struct {
		CompanyId         string `query:"company_id" validate:"required"`
		PaymentProviderId string `query:"payment_provider_id" validate:"required"`
		Limit             int    `query:"limit" validate:"required"`
		Page              int    `query:"page" validate:"required"`
		Order             string `query:"order" validate:"required"`
		Sort              string `query:"sort" validate:"required"`
		Value             string `query:"value"`
	}
	reqParams := new(params)
	err = c.QueryParser(reqParams)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(reqParams)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	req := domain.GetCompanyMappingReq{}
	req.CompanyID, _ = strconv.Atoi(reqParams.CompanyId)
	req.ProviderID, _ = strconv.Atoi(reqParams.PaymentProviderId)
	var paginate = utils.Pagination{
		Limit: reqParams.Limit,
		Page:  reqParams.Page,
		Order: reqParams.Order,
		Sort:  reqParams.Sort,
		Value: reqParams.Value,
	}

	list, totalItem, err := pp.companyMappingService.PaymentChannelList(c.UserContext(), &req, paginate)
	if err != nil {
		return
	}

	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

// List Provider Channel Mapping
func (pp *CompanyMappingHandler) ListChannelMappingAll(c *fiber.Ctx) (err error) {
	type params struct {
		CompanyId             string `query:"company_id" validate:"required"`
		PaymentProviderId     string `query:"payment_provider_id"`
		Capability            string `query:"capability"`
		ProviderChannelStatus string `query:"provider_channel_status"`
		Order                 string `query:"order"`
		Sort                  string `query:"sort"`
	}
	reqParams := new(params)
	err = c.QueryParser(reqParams)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(reqParams)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	req := domain.GetCompanyMappingReq{}
	req.CompanyID, _ = strconv.Atoi(reqParams.CompanyId)
	req.ProviderID, _ = strconv.Atoi(reqParams.PaymentProviderId)
	req.Capability, _ = strconv.Atoi(reqParams.Capability)
	pcStatus, err := strconv.ParseBool(reqParams.ProviderChannelStatus)
	if err == nil {
		req.ProviderChannelStatus = &pcStatus
	}

	list, _, err := pp.companyMappingService.PaymentChannelList(c.UserContext(), &req, utils.Pagination{})
	if err != nil {
		return
	}

	return response.HandleSuccess(c, list)
}

// View Provider ChannelMapping
func (pp *CompanyMappingHandler) ViewChannelMapping(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	data, _ := pp.companyMappingService.ViewMapping(c.UserContext(), id)
	return response.HandleSuccess(c, data)
}

// Create Provider Channel Mapping
func (pp *CompanyMappingHandler) CreateChannelMapping(c *fiber.Ctx) error {
	type request struct {
		CompanyID          int64       `json:"company_id" validate:"required"`
		PaymentProviderID  int64       `json:"payment_provider_id" validate:"required"`
		ProviderCode       string      `json:"provider_code" validate:"required"`
		FeeFixValue        float64     `json:"fee_fix_value"`
		FeePercentage      float64     `json:"fee_percentage"`
		ExpiredTime        int         `json:"expired_time"`
		Status             bool        `json:"status"`
		ChannelTypeConfigs interface{} `json:"channel_type_configs"`
	}
	r := new(request)
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	pr := new(domain.CompanyPaymentProviderChannelMappings)
	pr.CompanyID = r.CompanyID
	pr.PaymentProviderID = r.PaymentProviderID
	pr.FeeFixValue = r.FeeFixValue
	pr.FeePercentage = r.FeePercentage
	pr.ExpiredTime = r.ExpiredTime
	pr.Status = r.Status
	interfacepkg.Convert(r.ChannelTypeConfigs, &pr.ChannelTypeConfigs)

	res, err := pp.companyMappingService.CreateMapping(c.UserContext(), pr, r.ProviderCode)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, res)
}

// Update Provider Channel Mapping
func (pp *CompanyMappingHandler) UpdateChannelMapping(c *fiber.Ctx) error {
	type request struct {
		ID                 int         `json:"id" validate:"required"`
		CompanyID          int64       `json:"company_id" validate:"required"`
		PaymentProviderID  int64       `json:"payment_provider_id" validate:"required"`
		Code               string      `json:"provider_code" validate:"required"`
		FeeFixValue        float64     `json:"fee_fix_value"`
		FeePercentage      float64     `json:"fee_percentage"`
		ExpiredTime        int         `json:"expired_time"`
		Status             bool        `json:"status"`
		ChannelTypeConfigs interface{} `json:"channel_type_configs"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	pr := new(domain.CompanyPaymentProviderChannelMappings)
	pr.ID = int64(r.ID)
	pr.CompanyID = r.CompanyID
	pr.PaymentProviderID = r.PaymentProviderID
	pr.FeeFixValue = r.FeeFixValue
	pr.FeePercentage = r.FeePercentage
	pr.ExpiredTime = r.ExpiredTime
	pr.Status = r.Status
	interfacepkg.Convert(r.ChannelTypeConfigs, &pr.ChannelTypeConfigs)

	res, err := pp.companyMappingService.UpdateMapping(c.Context(), pr, r.Code)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, res)
}

// Delete Provider Channel Mapping
func (pp *CompanyMappingHandler) DeleteMapping(c *fiber.Ctx) error {
	type request struct {
		ID int `query:"id" validate:"required"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	isSuccess, err := pp.companyMappingService.DeleteMapping(c.UserContext(), int64(r.ID))
	if !isSuccess {
		return err
	}
	return response.HandleSuccess(c, nil)
}

// List Provider CompanyXProviderMapping
func (pp *CompanyMappingHandler) ListCompanyXProviderMapping(c *fiber.Ctx) error {
	type request struct {
		Page        int    `query:"page" validate:"required,min=1"`
		ItemPerPage int    `query:"limit" validate:"required,min=1"`
		Search      string `query:"search"`
	}
	r := new(request)
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	params := map[string]interface{}{}
	if r.Search != "" {
		params["search"] = r.Search
	}
	orderParam := map[string]interface{}{
		"start": (r.Page - 1) * r.ItemPerPage,
		"limit": r.ItemPerPage,
	}

	list, totalItem, _ := pp.companyMappingService.ListCompanyXProviderMapping(c.UserContext(), params, orderParam)
	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

// View Provider CompanyXProviderMapping
func (pp *CompanyMappingHandler) ViewCompanyXProviderMapping(c *fiber.Ctx) error {
	type request struct {
		ID int `query:"id" validate:"required"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	param := map[string]interface{}{
		"id": r.ID,
	}

	data, _ := pp.companyMappingService.ViewCompanyXProviderMapping(c.UserContext(), param)
	return response.HandleSuccess(c, data)
}

// Create Provider CompanyXProviderMapping
func (pp *CompanyMappingHandler) CreateCompanyXProviderMapping(c *fiber.Ctx) error {
	type request struct {
		PaymentProviderID int64  `json:"payment_provider_id" validate:"required"`
		CompanyID         int64  `json:"company_id" validate:"required"`
		ProviderSecrets   string `json:"provider_secrets"`
		Status            bool   `json:"status"`
	}
	r := new(request)
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	pr := new(domain.CompanyPaymentProviderMappings)
	pr.PaymentProviderID = r.PaymentProviderID
	pr.CompanyID = r.CompanyID
	pr.ProviderSecrets = r.ProviderSecrets
	pr.Status = r.Status

	res, err := pp.companyMappingService.CreateCompanyXProviderMapping(c.UserContext(), pr)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, res)
}

// Update Provider CompanyXProviderMapping
func (pp *CompanyMappingHandler) UpdateCompanyXProviderMapping(c *fiber.Ctx) error {
	type request struct {
		ID                int    `json:"id" validate:"required"`
		PaymentProviderID int64  `json:"payment_provider_id" validate:"required"`
		CompanyID         int64  `json:"company_id" validate:"required"`
		ProviderSecrets   string `json:"provider_secrets"`
		Status            bool   `json:"status"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	pr := new(domain.CompanyPaymentProviderMappings)
	pr.ID = int64(r.ID)
	pr.PaymentProviderID = r.PaymentProviderID
	pr.CompanyID = r.CompanyID
	pr.ProviderSecrets = r.ProviderSecrets
	pr.Status = r.Status

	res, err := pp.companyMappingService.UpdateCompanyXProviderMapping(c.UserContext(), pr)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, res)
}

// Delete Provider CompanyXProviderMapping
func (pp *CompanyMappingHandler) DeleteCompanyXProviderMapping(c *fiber.Ctx) error {
	type request struct {
		ID int `query:"id" validate:"required"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	isSuccess, err := pp.companyMappingService.DeleteCompanyXProviderMapping(c.UserContext(), int64(r.ID))
	if !isSuccess {
		return err
	}

	return response.HandleSuccess(c, nil)
}

func (h *CompanyMappingHandler) GetListAllCompanyProviderMappingChannelByCompanyID(c *fiber.Ctx) (err error) {
	companyId := c.Params("companyId")
	resp, err := h.companyMappingService.GetListAllCompanyProviderMappingChannelByCompanyID(c.Context(), companyId)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CompanyMappingHandler) GetListAllCompanyPaymentProvider(c *fiber.Ctx) (err error) {
	companyId := c.Params("companyId")
	channelId := c.Params("channelId")
	resp, err := h.companyMappingService.GetListAllCompanyPaymentProvider(c.UserContext(), companyId, channelId)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CompanyMappingHandler) GetListAllCompanyProviderMappingChannelAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	paymentTypesQ := c.Query("payment_types")
	var paymentTypes []string
	if len(paymentTypesQ) > 0 {
		paymentTypes = strings.Split(paymentTypesQ, ",")
	}

	resp, err := h.companyMappingService.GetListAllCompanyProviderMappingAPI(c.UserContext(), string(auth), domain.GetAllCompanyProviderMappingReq{
		Search:       c.Query("search"),
		PaymentTypes: paymentTypes,
		ProductCode:  c.Query("product_code"),
		Order:        c.Query("order"),
		Sort:         c.Query("sort"),
	})
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CompanyMappingHandler) GetCompanyProviderMappingCodeAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	code := c.Params("code")

	resp, err := h.companyMappingService.GetCompanyProviderMappingCodeAPI(c.UserContext(), string(auth), code)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CompanyMappingHandler) GetListMappingCashInPaymentMethodAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	resp, err := h.companyMappingService.GetListMappingCashInPaymentMethodAPI(c.UserContext(), string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (pp *CompanyMappingHandler) UpdateStatusCompanyXProviderMapping(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid id")
	}

	req := new(request)
	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	res, err := pp.companyMappingService.UpdateStatusCompanyXProviderMapping(c.UserContext(), int64(id), req.Status)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, res)
}

func (pp *CompanyMappingHandler) UpdateStatusChannelMapping(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid id")
	}

	req := new(request)
	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	res, err := pp.companyMappingService.UpdateStatusChannelMapping(c.UserContext(), int64(id), req.Status)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, res)
}
