package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"repo.nusatek.id/nusatek/payment/modules/user_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/response"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (h *UserManagementHandler) CreateUser(c *fiber.Ctx) (err error) {
	var req entity.UsersRequest
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.CreateUser(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) UpdateUser(c *fiber.Ctx) (err error) {
	var req entity.UsersRequest
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return errors.SetError(http.StatusBadRequest, "invalid id")
	}
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	req.ID = id
	err = h.userService.UpdateUser(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

// Debug
func (h *UserManagementHandler) GenerateLogin(c *fiber.Ctx) (err error) {
	var req entity.UserLogin
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	tx, _ := json.Marshal(req)
	ciphertext, err := utils.EncryptAesCbc([]byte(config.GetString("login_key")), string(tx))
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	var resp = entity.RSAEncrpty{
		Encrypt: ciphertext,
	}
	return response.HandleSuccess(c, resp)
}

func (h *UserManagementHandler) UserLogin(c *fiber.Ctx) (err error) {
	var req entity.UserLoginRSA
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	resp, err := h.userService.UserLogin(c.UserContext(), req.Login)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *UserManagementHandler) ListMenuMappingPermissionAll(c *fiber.Ctx) (err error) {
	roleid := c.Params("roleId")
	resp, err := h.userService.GetListMenuMappingUserPermissionByRoleId(c.UserContext(), roleid)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *UserManagementHandler) UpdateListMenuMappingPermission(c *fiber.Ctx) (err error) {
	var req []entity.MenuMappingPermission
	id := c.Params("roleId")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	for i := 0; i < len(req); i++ {
		err = utils.ValidateStruct(req[i])
		if err != nil {
			return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		}
	}
	err = h.userService.UpdateListMenuMappingUserPermissionByRoleId(c.UserContext(), req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) DeleteUser(c *fiber.Ctx) (err error) {
	userId, _ := strconv.Atoi(c.Params("id"))
	err = h.userService.DeleteUser(c.UserContext(), userId)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *UserManagementHandler) GetUserById(c *fiber.Ctx) (err error) {
	userId := c.Params("id")
	resp, err := h.userService.GetUserByIdShow(c.Context(), userId)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *UserManagementHandler) SearchUserList(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}
	resp, totalData, err := h.userService.SearchUserList(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *UserManagementHandler) UpdateStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid id")
	}

	req := new(request)
	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = h.userService.UpdateStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}
