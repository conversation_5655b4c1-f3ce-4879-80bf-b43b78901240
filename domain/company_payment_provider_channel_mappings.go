package domain

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
)

type GetCompanyMappingReq struct {
	CompanyID             int
	ProviderID            int
	Capability            int
	ProviderChannelStatus *bool
}

type CompanyPaymentProviderChannelMappings struct {
	ID                 int64              `gorm:"primaryKey" json:"id"`
	CompanyID          int64              `json:"company_id"`
	PaymentProviderID  int64              `json:"payment_provider_id"`
	PaymentChannelID   int64              `json:"payment_channel_id"`
	FeeFixValue        float64            `json:"fee_fix_value"`
	FeePercentage      float64            `json:"fee_percetage"`
	Capability         int                `json:"capability"`
	ExpiredTime        int                `json:"expired_time"`
	Status             bool               `json:"status"`
	CreatedAt          time.Time          `json:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at"`
	ChannelTypeConfigs ChannelTypeConfigs `json:"channel_type_configs" gorm:"column:channel_type_configs"`
	DeletedAt          gorm.DeletedAt     `gorm:"index" json:"deleted_at"`
}

type ChannelTypeConfigs map[string]interface{}

func (c ChannelTypeConfigs) Value() (driver.Value, error) {
	return json.Marshal(c)
}

func (c *ChannelTypeConfigs) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		// return errors.New("type assertion to []byte failed")
		return nil
	}

	return json.Unmarshal(b, &c)
}

func (c ChannelTypeConfigs) ToVA() (res ChannelTypeConfigVA) {
	_ = interfacepkg.Convert(c, &res)

	return
}

type ChannelTypeConfigVA struct {
	IsActiveFixedVirtualAccount bool   `json:"is_active_fixed_virtual_account"` // is active fixed virtual account
	MerchantCode                string `json:"merchant_code"`
	FixedVirtualAccountStart    int64  `json:"fixed_virtual_account_start"` // virtual account start
	FixedVirtualAccountEnd      int64  `json:"fixed_virtual_account_end"`   // virtual account end
}

func (c ChannelTypeConfigVA) Valid() error {
	if c.IsActiveFixedVirtualAccount {
		if len(c.MerchantCode) <= 0 {
			return errors.New("merchant code empty")
		}
		if c.FixedVirtualAccountEnd <= 0 {
			return errors.New("fixed virtual account end cannot less than 0")
		}
		if c.FixedVirtualAccountStart > c.FixedVirtualAccountEnd {
			return errors.New("fixed virtual account start greater than fixed virtual account end")
		}
	}
	return nil
}

func (c ChannelTypeConfigVA) Generate(counter int64) (vaNumber string) {
	lenVa := len(fmt.Sprintf("%d", c.FixedVirtualAccountEnd))
	formatVA := `%0` + fmt.Sprintf(`%d`, lenVa) + `d`
	vaNumber = c.MerchantCode + fmt.Sprintf(formatVA, counter)

	return
}

type ChannelTypeConfigSNAP struct {
	CompCode string `json:"comp_code"` // company code, client will put this on req header "X-PARTNER-ID"
}

func (c ChannelTypeConfigs) ToSNAP() (res ChannelTypeConfigSNAP) {
	_ = interfacepkg.Convert(c, &res)

	return
}

type GetAllCompanyProviderMappingReq struct {
	PaymentTypes []string
	ProductCode  string
	Search       string
	Order        string
	Sort         string
}
