package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/modules/file/usecase"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/model"
	"repo.nusatek.id/nusatek/payment/utils/response"
	"repo.nusatek.id/nusatek/payment/utils/str"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

type FileHandler struct {
	fileService usecase.FileUsecase
}

func NewHandler(companyProductService usecase.FileUsecase) *FileHandler {
	return &FileHandler{companyProductService}
}

// Upload ...
func (h *FileHandler) Upload(c *fiber.Ctx) (err error) {
	user := c.Locals("token").(model.JwtToken)

	// Read file type
	fileType := c.FormValue("type")
	if !str.Contains(constants.FileTypeWhitelist, fileType) {
		return errors.SetError(http.StatusBadRequest, "invalid_file_type")
	}

	// Upload file to local temporary
	fileHeader, err := c.FormFile("file")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	res, err := h.fileService.Upload(c.UserContext(), strconv.Itoa(user.UserId), fileType, fileHeader)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, res)
}
