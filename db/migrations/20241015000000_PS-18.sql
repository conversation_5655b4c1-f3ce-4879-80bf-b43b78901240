-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.
-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS email_logs_id_seq;
-- Table Definition
CREATE TABLE "public"."email_logs" (
    "id" int8 NOT NULL DEFAULT nextval('email_logs_id_seq'::regclass),
    "cashout_email_log_id" int8 NOT NULL,
    "company_id" int8 NOT NULL,
    "partner_id" int8 NOT NULL,
    "batch_number" varchar(255),
    "email" varchar(255) NOT NULL,
    "send_date" timestamptz NOT NULL,
    "status" varchar(50) NOT NULL,
    "error_message" text,
    "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "parent_id" int8,
    "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_email_logs_cashout_log" FOREIGN KEY ("cashout_email_log_id") REFERENCES "public"."cashout_email_logs"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_email_logs_company" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_email_logs_parent" FOREIGN KEY ("parent_id") REFERENCES "public"."email_logs"("id"),
    CONSTRAINT "fk_email_logs_partner" FOREIGN KEY ("partner_id") REFERENCES "public"."partners"("id") ON DELETE CASCADE,
    PRIMARY KEY ("id")
);
-- Column Comments
COMMENT ON COLUMN "public"."email_logs"."id" IS 'Unique identifier for the email log';
COMMENT ON COLUMN "public"."email_logs"."company_id" IS 'ID of the company associated with this email log';
COMMENT ON COLUMN "public"."email_logs"."partner_id" IS 'ID of the partner associated with this email log';
COMMENT ON COLUMN "public"."email_logs"."batch_number" IS 'Batch number for grouped emails';
COMMENT ON COLUMN "public"."email_logs"."email" IS 'Email address of the recipient';
COMMENT ON COLUMN "public"."email_logs"."send_date" IS 'Date and time when the email was sent';
COMMENT ON COLUMN "public"."email_logs"."status" IS 'Current status of the email (pending, sent, failed, opened, clicked)';
COMMENT ON COLUMN "public"."email_logs"."error_message" IS 'Error message if the email failed to send';
COMMENT ON COLUMN "public"."email_logs"."created_at" IS 'Timestamp when the log entry was created';
-- Comments
COMMENT ON TABLE "public"."email_logs" IS 'Stores logs of emails sent through the system';
-- Indices
CREATE INDEX idx_email_logs_company_id ON public.email_logs USING btree (company_id) CREATE INDEX idx_email_logs_partner_id ON public.email_logs USING btree (partner_id) CREATE INDEX idx_email_logs_batch_number ON public.email_logs USING btree (batch_number) CREATE INDEX idx_email_logs_status ON public.email_logs USING btree (status) CREATE INDEX idx_email_logs_send_date ON public.email_logs USING btree (send_date) CREATE INDEX idx_email_logs_parent_id ON public.email_logs USING btree (parent_id);
-- This script only contains the table creation statements and does not fully represent the table in the database. Do not use it as a backup.
-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS cashout_email_logs_id_seq;
-- Table Definition
CREATE TABLE "public"."cashout_email_logs" (
    "id" int8 NOT NULL DEFAULT nextval('cashout_email_logs_id_seq'::regclass),
    "cashout_id" int4 NOT NULL,
    "company_id" int8 NOT NULL,
    "partner_id" int8 NOT NULL,
    "batch_number" varchar(255),
    "email" varchar(255) NOT NULL,
    "send_at" timestamptz NOT NULL,
    "message_body" text NOT NULL,
    "last_status" varchar(255),
    "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_co_email_logs_cashout" FOREIGN KEY ("cashout_id") REFERENCES "public"."cash_out_transactions"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_co_email_logs_company" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_co_email_logs_partner" FOREIGN KEY ("partner_id") REFERENCES "public"."partners"("id") ON DELETE CASCADE,
    PRIMARY KEY ("id")
);
-- Indices
CREATE INDEX idx_co_email_logs_partner_id ON public.cashout_email_logs USING btree (partner_id) CREATE INDEX idx_co_email_logs_company_id ON public.cashout_email_logs USING btree (company_id) CREATE INDEX idx_co_email_logs_cashout_id ON public.cashout_email_logs USING btree (cashout_id) CREATE INDEX idx_co_email_logs_batch_number ON public.cashout_email_logs USING btree (batch_number) CREATE INDEX idx_co_email_logs_status ON public.cashout_email_logs USING btree (last_status) CREATE INDEX idx_co_email_logs_send_date ON public.cashout_email_logs USING btree (send_at);