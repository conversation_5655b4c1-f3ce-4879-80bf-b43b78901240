package infrastructure

import (
	"fmt"

	"github.com/streadway/amqp"
	mbConfig "repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	mbConsumer "repo.nusatek.id/moaja/backend/libraries/message-broker/consumer"
	"repo.nusatek.id/moaja/backend/libraries/message-broker/message"
	mbPublisher "repo.nusatek.id/moaja/backend/libraries/message-broker/publisher"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

func getConfig() mbConfig.RabbitMQConfig {
	amqphost := config.GetString("rabbit.host")
	amqport := config.GetInt("rabbit.port")
	amqpuser := config.GetString("rabbit.user")
	amqppass := config.GetString("rabbit.password")

	amqpConfig := mbConfig.RabbitMQConfig{
		Host: amqphost,
		Port: amqport,
		User: amqpuser,
		Pass: amqppass,
	}

	return amqpConfig
}

func PublishMessage(routingKey string, msg *message.Message) {
	exchangeName := config.GetString("rabbit.exchange_name")
	queueName := config.GetString("rabbit.queue_name")

	if routingKey == "" {
		routingKey = config.GetString("rabbit.routing_key")
	}

	amqpConfig := getConfig()
	publisher := mbPublisher.NewPublisher(amqpConfig, mbConfig.ChannelConfig{
		QueueName:    queueName,
		ExchangeName: exchangeName,
		RoutingKey:   routingKey,
	})
	publisher.Publish(routingKey, msg)
}

func ConsumeMessage(topic string, consume func(*amqp.Delivery) error) {
	exchangeName := config.GetString("rabbit.exchange_name")
	queueName := topic

	amqpConfig := getConfig()
	consumer, err := mbConsumer.NewConsumer(amqpConfig, mbConfig.ChannelConfig{
		ExchangeName: exchangeName,
		QueueName:    queueName,
		RoutingKey:   queueName,
	})
	if err != nil {
		panic(err)
	}

	go func() {
		for {
			if consumer.IsClose() {
				if err := consumer.Reconect(); err != nil {
					fmt.Println("err", err.Error())
				}
				fmt.Println("connected....")
			}

			res, err := consumer.Consume()
			if err != nil {
				panic(err.Error())
			}

			for data := range res {
				consume(&data)
			}
		}
	}()

}
