package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCashinTransaction) UpdateTranscactionCashIn(ctx context.Context, req entity.CashinUpdateTransaction, cashInTransactionId, auth string) (resp entity.CashinTranscationResponse, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	cashIn, err := s.cashinRepo.GetCashinTransactionById(ctx, cashInTransactionId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
		logger.Error(ctx, err.Error())
		return
	}

	channelPayment, err := s.channelMappingRepo.GetChannelMappingByCode(ctx, req.PaymentMethodCode)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method not found")
		logger.Error(ctx, err.Error())
		return
	}

	keyCache := fmt.Sprintf("trx-%v-%v-%v", cashIn.ID, channelPayment.PaymentProviderID, channelPayment.PaymentChannelID)
	if cashIn.PaymentChannelID == int(channelPayment.PaymentChannelID) && cashIn.PaymentProviderID == int(channelPayment.PaymentProviderID) {
		trx, errRes := s.cache.Get(ctx, keyCache).Result()
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, "payment provider and payment channel are the same")
			logger.Error(ctx, err.Error())
			return
		}

		var trxRes entity.CashinTranscationResponse
		_ = json.Unmarshal([]byte(trx), &trxRes)

		resp = trxRes
		return
	}

	if cashIn.Status != constants.StatusPending {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment status can't be changed anymore")
		logger.Error(ctx, err.Error())
		return
	}
	companyId := strconv.Itoa(company.ID)
	providerId := strconv.Itoa(int(channelPayment.PaymentProviderID))
	channelId := strconv.Itoa(int(channelPayment.PaymentChannelID))

	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err != nil {
		return
	}

	cashInCabalitty := strconv.Itoa(constants.CashInCapability)
	companyMapping, err := s.companyMappingRepo.GetCompanyProviderChannelMappingRequestBy(ctx, companyId, providerId, channelId, cashInCabalitty)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method cash in not found in company")
		logger.Error(ctx, err.Error())
		return
	}
	provider, err := s.providerService.GetPaymentProviderById(ctx, providerId)
	if err != nil {
		return
	}
	channel, err := s.channelService.GetPaymentChannelById(ctx, channelId)
	if err != nil {
		return
	}
	invoice := strings.Split(cashIn.InvoiceNumber, "-")
	if len(invoice) == 0 {
		err = errors.SetErrorMessage(http.StatusForbidden, "failed update payment method")
		logger.Error(ctx, err.Error())
		return
	}

	cashIn.PaymentProviderID = int(channelPayment.PaymentProviderID)
	cashIn.PaymentChannelID = int(channelPayment.PaymentChannelID)
	cashIn.InvoiceNumber = invoice[0]

	expiredTime := time.Duration(companyMapping.ExpiredTime) * time.Second
	reqTransaction := entity.CashinTransactionProcess{
		CashinTransaction:   *cashIn,
		Credential:          providerMapping.ProviderSecrets,
		ProviderName:        provider.Name,
		ProviderChannelCode: channelPayment.ProviderChannelCode,
		ExpiredTime:         time.Now().Add(expiredTime),
		Description:         req.Description,
		Command:             entity.CommandUpdateTransaction,
		MobileNumber:        req.MobileNumber,
		SuccessRedirectUrl:  req.SuccessRedirectUrl,
		PaymentChannel: domain.PaymentChannels{
			ID:                   channel.ID,
			Name:                 channel.Name,
			Status:               channel.Status,
			PaymentChannelTypeId: channel.PaymentChannelTypeId,
		},
	}

	resp, err = s.transactionProviderProcess(ctx, &reqTransaction)
	if err != nil {
		return
	}

	keyCacheOld := fmt.Sprintf("trx-%v-%v-%v", cashIn.ID, cashIn.PaymentProviderID, cashIn.PaymentChannelID)
	s.cache.Del(ctx, keyCacheOld)

	respByte, _ := json.Marshal(resp)
	s.cache.Set(ctx, keyCache, string(respByte), expiredTime)

	return
}
