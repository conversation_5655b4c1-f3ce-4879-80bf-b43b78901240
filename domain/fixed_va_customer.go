package domain

import "time"

type FixedVACustomer struct {
	ID                                     int64
	CompanyPaymentProviderChannelMappingId int64
	Counter                                int64
	/* the idea is always generate format va number every request to payment gateway,
	if we only use "va number" field, there is case when admin dash<PERSON> doesn't give the correct config,
	so we save the incorrect va_number*/
	VANumber          string
	Name              string
	Phone             string
	LastPgReferenceId string
	ExpiredAt         time.Time
	CreatedAt         time.Time
	UpdatedAt         time.Time
}
