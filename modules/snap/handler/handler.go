package handler

import (
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/modules/snap/usecase"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

type Handler struct {
	uc              usecase.Usecase
	ignoreSignature bool
}

func NewHandler(uc usecase.Usecase) *Handler {
	return &Handler{uc, config.GetBool("snap.ignore_signature")}
}

func (h *Handler) GenerateSignatureAuth(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()

	req := domain.SnapGenerateSignatureAuthReq{
		ClientId:   string(c.Request().Header.Peek(snapclient.HeaderClientKey)),
		Timestamp:  domain.SnapStrTimestamp(string(c.Request().Header.Peek(snapclient.HeaderTimestamp))),
		PrivateKey: str.Base64str(c.Request().Header.Peek("Private_Key")), // in base 64
	}

	resp, err := h.uc.GenerateSignatureAuth(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("GenerateSignatureAuth %v", err))
		return h.sendErr(c, err)
	}

	return h.send(c, resp)
}

func (h *Handler) GenerateSignatureService(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()

	req := domain.SnapGenerateSignatureServiceReq{
		Timestamp:    domain.SnapStrTimestamp(string(c.Request().Header.Peek(snapclient.HeaderTimestamp))),
		ClientSecret: string(c.Request().Header.Peek("X-CLIENT-SECRET")),
		HttpMethod:   string(c.Request().Header.Peek("HttpMethod")),
		EndpoinUrl:   string(c.Request().Header.Peek("EndpoinUrl")),
		AccessToken:  string(c.Request().Header.Peek("AccessToken")),
		ReqBody:      c.Request().Body(),
	}

	resp, err := h.uc.GenerateSignatureService(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("GenerateSignatureService %v", err))
		return h.sendErr(c, err)
	}

	return h.send(c, resp)
}

func (h *Handler) GetAccessTokenB2B(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	br := snaputil.NewBaseRespCtx(ctx)

	var req domain.SnapAccesTokenB2BReq
	err = c.BodyParser(&req)
	if err != nil {
		return h.send(c, br.Set(http.StatusBadRequest, "00", "Bad Request"))
	}

	resp, err := h.uc.GetAccessTokenB2B(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("GetAccessTokenB2B %v", err))
		return h.sendErr(c, err)
	}

	return h.send(c, resp)
}

func (h *Handler) TransferVaInquiry(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	br := snaputil.NewBaseRespCtx(ctx)

	var req domain.SnapTransferVaInquiryReq
	err = c.BodyParser(&req)
	if err != nil {
		return h.send(c, br.Set(http.StatusBadRequest, "00", "Bad Request"))
	}

	resp, err := h.uc.TransferVaInquiry(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("TransferVaInquiry %v", err))
		return h.send(c, resp)
	}

	return h.send(c, resp)
}

func (h *Handler) TransferVaPayment(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	br := snaputil.NewBaseRespCtx(ctx)

	var req domain.SnapTransferVaPaymentReq
	err = c.BodyParser(&req)
	if err != nil {
		return h.send(c, br.Set(http.StatusBadRequest, "00", "Bad Request"))
	}

	resp, err := h.uc.TransferVaPayment(ctx, req)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("TransferVaPayment %v", err))
		return h.send(c, resp)
	}

	return h.send(c, resp)
}
