package utils

import (
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

func ValidateStruct(req interface{}) error {
	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			fmt.Println(err)
			return err
		}

		var message string
		for _, err := range err.(validator.ValidationErrors) {
			message = fmt.Sprintf("field validator for input %v failed on the %v tag", err.<PERSON>(), err.ActualTag())
		}
		return errors.New(message)
	}
	return err
}

func ValidateDate(value string) bool {
	match, _ := regexp.MatchString("\\d{4}-\\d{2}-\\d{2}", value)
	return match
}

func ValidatorBasicAuth(auth string) (secret string, err error) {
	parts := strings.Split(auth, " ")
	if len(parts) != 2 {
		return "", fmt.<PERSON><PERSON><PERSON>("invalid authorization header format")
	}

	if !strings.EqualFold(parts[0], "Basic") {
		return "", fmt.Errorf("authorization type must be Basic")
	}

	secret = parts[1]
	return secret, nil
}
