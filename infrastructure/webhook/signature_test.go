package webhook

import (
	"encoding/base64"
	"strings"
	"testing"
	"time"
)

func TestSignatureVerifier(t *testing.T) {
	payload := []byte(`{"event": "user.created", "id": "123"}`)
	secret := "test-secret-key"

	testCases := []struct {
		name            string
		config          SignatureConfig
		modifySignature func(string, string) (string, string)
		wantErr         bool
		errorMessage    string
	}{
		{
			name: "valid SHA512 signature",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) { return s, d },
			wantErr:         false,
		},
		{
			name: "valid SHA256 signature",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA256,
			},
			modifySignature: func(s, d string) (string, string) { return s, d },
			wantErr:         false,
		},
		{
			name: "mismatched scheme",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) {
				return string(SchemeHMACSHA256) + s[len(SchemeHMACSHA512):], d
			},
			wantErr:      true,
			errorMessage: "invalid signature scheme",
		},
		{
			name: "invalid signature format",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) {
				return "invalid.format", d
			},
			wantErr:      true,
			errorMessage: "invalid signature scheme",
		},
		{
			name: "expired timestamp",
			config: SignatureConfig{
				Secret:          secret,
				Scheme:          SchemeHMACSHA512,
				TimestampMaxAge: 1 * time.Second,
			},
			modifySignature: func(s, d string) (string, string) { return s, d },
			wantErr:         true,
			errorMessage:    "signature has expired",
		},
		{
			name: "future timestamp",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) {
				// Return same signature but with future timestamp
				return s, d
			},
			wantErr:      true,
			errorMessage: "signature timestamp is in the future",
		},
		{
			name: "tampered signature",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) {
				parts := strings.Split(s, ".")
				return parts[0] + "." + parts[1] + ".tampered", d
			},
			wantErr:      true,
			errorMessage: "invalid signature",
		},
		{
			name: "tampered digest",
			config: SignatureConfig{
				Secret: secret,
				Scheme: SchemeHMACSHA512,
			},
			modifySignature: func(s, d string) (string, string) {
				return s, "tampered-digest"
			},
			wantErr:      true,
			errorMessage: "invalid payload digest",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			verifier := NewSignatureVerifier(tc.config)
			timestamp := time.Now().Unix()

			if tc.name == "expired timestamp" {
				timestamp = time.Now().Add(-2 * time.Second).Unix()
			} else if tc.name == "future timestamp" {
				timestamp = time.Now().Add(1 * time.Hour).Unix()
			}

			signature, digest := verifier.GenerateSignature(payload, timestamp, "application/json")
			signature, digest = tc.modifySignature(signature, digest)

			err := verifier.VerifySignature(payload, signature, digest, timestamp)

			if tc.wantErr {
				if err == nil {
					t.Error("expected error but got nil")
					return
				}
				if tc.errorMessage != "" && !strings.Contains(err.Error(), tc.errorMessage) {
					t.Errorf("expected error containing %q but got %q", tc.errorMessage, err.Error())
				}
				return
			} else if err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

func TestSignatureFormat(t *testing.T) {
	verifier := NewSignatureVerifier(SignatureConfig{
		Secret: "test-secret",
		Scheme: SchemeHMACSHA512,
	})

	payload := []byte("test")
	timestamp := time.Now().Unix()
	signature, digest := verifier.GenerateSignature(payload, timestamp, "application/json")

	parts := strings.Split(signature, ".")
	if len(parts) != 2 {
		t.Errorf("expected 2 parts in signature but got %d", len(parts))
	}

	if SignatureScheme(parts[0]) != SchemeHMACSHA512 {
		t.Errorf("expected scheme %s but got %s", SchemeHMACSHA512, parts[0])
	}

	// Verify the generated digest separately since it's not in signature anymore
	h := verifier.getHasher()()
	h.Write(payload)
	expectedDigest := base64.StdEncoding.EncodeToString(h.Sum(nil))
	if digest != expectedDigest {
		t.Errorf("generated digest %s does not match expected digest %s", digest, expectedDigest)
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultSignatureConfig()

	if config.Scheme != SchemeHMACSHA256 {
		t.Errorf("expected default scheme to be %s, got %s", SchemeHMACSHA256, config.Scheme)
	}

	if config.HeaderName != "Signature" {
		t.Errorf("expected default header name to be Signature, got %s", config.HeaderName)
	}

	if config.TimestampMaxAge != 5*time.Minute {
		t.Errorf("expected default timestamp max age to be 5m, got %v", config.TimestampMaxAge)
	}

	if config.DigestHeader != "Digest" {
		t.Errorf("expected default digest header to be Digest, got %s", config.DigestHeader)
	}

}

func TestGeneratePayloadDigest(t *testing.T) {
	verifier := NewSignatureVerifier(SignatureConfig{
		Secret: "test-secret",
		Scheme: SchemeHMACSHA512,
	})

	payload := []byte("test-payload")
	digest := verifier.GeneratePayloadDigest(payload)

	if digest == "" {
		t.Error("expected non-empty digest")
	}

	// Same payload should generate same digest
	digest2 := verifier.GeneratePayloadDigest(payload)
	if digest != digest2 {
		t.Error("digest should be deterministic for same payload")
	}

	// Different payload should generate different digest
	differentPayload := []byte("different-payload")
	differentDigest := verifier.GeneratePayloadDigest(differentPayload)
	if digest == differentDigest {
		t.Error("different payloads should generate different digests")
	}
}
