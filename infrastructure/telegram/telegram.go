package telegram

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"text/template"
	"time"

	appConfig "repo.nusatek.id/nusatek/payment/utils/config"
)

// Config holds the configuration for the Telegram service
type Config struct {
	APIEndpoint string
	BearerToken string
	ChatID      string
	HTTPTimeout time.Duration
}

// Message represents the structure of a message to be sent
type Message struct {
	CompanyName       string
	BatchNumber       string
	PartnerName       string
	TotalDisbursement string
	FailedEmail       string
	ErrorMessage      string
}

// Service defines the interface for sending messages
type Service interface {
	SendErrorNotification(ctx context.Context, msg Message) error
}

// service implements the Service interface
type service struct {
	config    Config
	client    HTTPClient
	templates Template<PERSON>enderer
}

// HTTPClient interface allows for easier testing
type HTTPClient interface {
	Do(*http.Request) (*http.Response, error)
}

// TemplateRenderer interface for template operations
type TemplateRenderer interface {
	Render(msg Message) (string, error)
}

// templateRenderer implements TemplateRenderer
type templateRenderer struct {
	template *template.Template
}

// Default configuration values
const (
	defaultTimeout = 10 * time.Second
)

// SendTelegram provides a simplified way to send telegram messages using default configuration
func SendTelegram(e Message) error {
	// Create default configuration
	config := Config{
		APIEndpoint: appConfig.GetString("telegram_nusatek.domain"),
		BearerToken: appConfig.GetString("telegram_nusatek.auth"),
		ChatID:      appConfig.GetString("telegram_nusatek.chat_id"),
		HTTPTimeout: defaultTimeout,
	}

	// Create service with default configuration
	svc, err := NewService(config)
	if err != nil {
		return fmt.Errorf("failed to create telegram service: %w", err)
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
	defer cancel()

	// Send the message
	if err := svc.SendErrorNotification(ctx, e); err != nil {
		return fmt.Errorf("failed to send telegram message: %w", err)
	}

	return nil
}

// NewService creates a new Telegram service instance
func NewService(config Config) (Service, error) {
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	tmpl, err := createMessageTemplate()
	if err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	client := &http.Client{
		Timeout: config.HTTPTimeout,
	}

	return &service{
		config:    config,
		client:    client,
		templates: &templateRenderer{template: tmpl},
	}, nil
}

// SendErrorNotification sends an error notification message
func (s *service) SendErrorNotification(ctx context.Context, msg Message) error {
	renderedMessage, err := s.templates.Render(msg)
	if err != nil {
		return fmt.Errorf("failed to render message: %w", err)
	}

	return s.sendMessage(ctx, renderedMessage)
}

// Render renders the message template
func (t *templateRenderer) Render(msg Message) (string, error) {
	var buf bytes.Buffer
	if err := t.template.Execute(&buf, msg); err != nil {
		return "", fmt.Errorf("template execution failed: %w", err)
	}
	return buf.String(), nil
}

// sendMessage handles the HTTP communication with Telegram
func (s *service) sendMessage(ctx context.Context, message string) error {
	payload := map[string]string{
		"message": message,
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	url := fmt.Sprintf("%s/%s", s.config.APIEndpoint, s.config.ChatID)
	req, err := http.NewRequestWithContext(ctx, http.MethodPut, url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+s.config.BearerToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}

// createMessageTemplate creates and validates the message template
func createMessageTemplate() (*template.Template, error) {
	const templateText = `*Company Name*: *{{ .CompanyName }}*
*Batch Number*: *{{ .BatchNumber }}*
*Partner Name*: *{{ .PartnerName }}*
*Total Disbursement*: *{{ .TotalDisbursement }}*

*FAILED TO SEND E-MAIL*: *{{ .FailedEmail }}*

Error:
` + "```" + `
{{ .ErrorMessage }}
` + "```"

	tmpl, err := template.New("errorNotification").Parse(templateText)
	if err != nil {
		return nil, fmt.Errorf("template parsing failed: %w", err)
	}
	return tmpl, nil
}

// validateConfig validates the service configuration
func validateConfig(config Config) error {
	if config.APIEndpoint == "" {
		return fmt.Errorf("API endpoint is required")
	}
	if config.BearerToken == "" {
		return fmt.Errorf("bearer token is required")
	}
	if config.ChatID == "" {
		return fmt.Errorf("chat ID is required")
	}
	if config.HTTPTimeout == 0 {
		config.HTTPTimeout = 10 * time.Second // Default timeout
	}
	return nil
}
