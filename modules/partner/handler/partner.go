package handler

import (
	"strconv"
	"time"

	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/partner/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

type PartnerHandler struct {
	PartnerUseCase usecase.PartnerUseCase
}

func NewHandler(partnerUsecase usecase.PartnerUseCase) *PartnerHandler {
	return &PartnerHandler{partnerUsecase}
}

// List Partner
func (ph *PartnerHandler) List(c *fiber.Ctx) error {
	type request struct {
		Page        int    `query:"page" validate:"required,min=1"`
		ItemPerPage int    `query:"limit" validate:"required,min=1"`
		Key         string `query:"key"`
		Value       string `query:"value"`
		Tag         string `query:"tag"`
	}
	r := new(request)
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	param := map[string]interface{}{}
	if r.Value != "" {
		param["value"] = r.Value
	}

	if r.Key != "" {
		param["key"] = r.Key
	}

	if r.Tag != "" {
		param["tag"] = r.Tag
	}

	orderParam := map[string]interface{}{
		// "start": (r.Page - 1) * r.ItemPerPage,
		"start": r.Page,
		"limit": r.ItemPerPage,
	}

	list, totalItem, _ := ph.PartnerUseCase.List(c.UserContext(), param, orderParam)

	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

// View Partner
func (ph *PartnerHandler) View(c *fiber.Ctx) error {
	type request struct {
		ID int `json:"id" validate:"required"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	param := map[string]interface{}{
		"partner_id": r.ID,
	}

	data, err := ph.PartnerUseCase.View(c.UserContext(), param)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, data)
}

// Delete Partner
func (ph *PartnerHandler) Delete(c *fiber.Ctx) (err error) {
	type request struct {
		ID int `json:"id" validate:"required"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	err = ph.PartnerUseCase.DeletePartner(c.Context(), strconv.Itoa(r.ID))
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

// Create Partner
func (ph *PartnerHandler) Create(c *fiber.Ctx) error {
	type request struct {
		Partner          string   `json:"partner" validate:"required"`
		CompanyCode      string   `json:"company_code" validate:"required"`
		Phone            string   `json:"phone" validate:"required"`
		ArAccount        string   `json:"account_receivable" validate:"required"`
		ApAccount        string   `json:"bank_account_number" validate:"required,max=16"`
		FeePercentage    float64  `json:"fee_percentage"`
		FeeFixValue      float64  `json:"fee_fix_value"`
		Sla              int      `json:"sla" validate:"required"`
		PartnerCode      string   `json:"partner_code" validate:"min=4,alphanum"`
		Email            string   `json:"email"`
		Emails           []string `json:"emails"`
		IsSendEmail      bool     `json:"is_send_email"`
		Status           bool     `json:"status"`
		PaymentChannelId int      `json:"payment_channel_id"`
		FeeBehind        bool     `json:"fee_behind"`
		Tags             []int    `json:"tags"`
	}
	r := new(request)
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if !(r.FeePercentage > 0 || r.FeePercentage < 100) {
		return errors.SetErrorMessage(http.StatusBadRequest, "fee percentage start from 0 to 100 %")
	}
	if r.FeeFixValue < 0 {
		return errors.SetErrorMessage(http.StatusBadRequest, "fee fix value must be greater than 0")
	}

	pr := new(domain.Partners)
	pr.Name = r.Partner
	pr.Phone = r.Phone
	pr.ArAccount = r.ArAccount
	pr.ApAccount = r.ApAccount
	pr.FeePercentage = r.FeePercentage
	pr.FeeFixValue = r.FeeFixValue
	pr.Sla = r.Sla
	pr.Status = r.Status
	pr.Code = r.PartnerCode
	pr.Email = r.Email
	pr.Emails = r.Emails
	pr.IsSendEmail = r.IsSendEmail
	pr.PaymentChannelId = r.PaymentChannelId
	pr.FeeBehind = r.FeeBehind

	err := ph.PartnerUseCase.CreatePartner(c.UserContext(), pr, r.CompanyCode, r.Tags)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, r)
}

// Update Partner
func (ph *PartnerHandler) Update(c *fiber.Ctx) error {
	type request struct {
		ID               int      `json:"id" validate:"required"`
		Partner          string   `json:"partner" validate:"required"`
		CompanyCode      string   `json:"company_code" validate:"required"`
		Phone            string   `json:"phone" validate:"required"`
		ArAccount        string   `json:"account_receivable" validate:"required"`
		ApAccount        string   `json:"bank_account_number" validate:"required,max=18"`
		FeePercentage    float64  `json:"fee_percentage"`
		FeeFixValue      float64  `json:"fee_fix_value"`
		Sla              int      `json:"sla" validate:"required"`
		PartnerCode      string   `json:"partner_code" validate:"required"`
		Email            string   `json:"email"`
		Emails           []string `json:"emails"`
		IsSendEmail      bool     `json:"is_send_email"`
		Status           bool     `json:"status"`
		PaymentChannelId int      `json:"payment_channel_id"`
		FeeBehind        bool     `json:"fee_behind"`
		Tags             []int    `json:"tags"`
	}
	r := new(request)
	r.ID, _ = c.ParamsInt("id")
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if !(r.FeePercentage > 0 || r.FeePercentage < 100) {
		return errors.SetErrorMessage(http.StatusBadRequest, "fee percentage start from 0 to 100 %")
	}
	if r.FeeFixValue < 0 {
		return errors.SetErrorMessage(http.StatusBadRequest, "fee fix value must be greater than 0")
	}

	pr := new(domain.Partners)
	pr.ID = r.ID
	pr.Name = r.Partner
	pr.Phone = r.Phone
	pr.ArAccount = r.ArAccount
	pr.ApAccount = r.ApAccount
	pr.FeePercentage = r.FeePercentage
	pr.FeeFixValue = r.FeeFixValue
	pr.Sla = r.Sla
	pr.Status = r.Status
	pr.Code = r.PartnerCode
	pr.Email = r.Email
	pr.Emails = r.Emails
	pr.PaymentChannelId = r.PaymentChannelId
	pr.IsSendEmail = r.IsSendEmail
	pr.FeeBehind = r.FeeBehind

	err := ph.PartnerUseCase.UpdatePartner(c.UserContext(), pr, r.CompanyCode, r.Tags)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, r)
}

type PatchStatusBulkReq struct {
	IDs    []int `json:"ids" validate:"required"`
	Status bool  `json:"status"`
}

func (ph *PartnerHandler) PatchStatusBulk(c *fiber.Ctx) error {
	r := new(PatchStatusBulkReq)
	if err := c.BodyParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	err := ph.PartnerUseCase.PatchStatusBulk(c.UserContext(), r.IDs, r.Status)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, r)
}

// List History
func (ph *PartnerHandler) ListHistory(c *fiber.Ctx) error {
	type request struct {
		Page        int    `query:"page" validate:"required,min=1"`
		ItemPerPage int    `query:"limit" validate:"required,min=1"`
		FromDate    string `query:"from_date"`
		ToDate      string `query:"to_date"`
	}
	r := new(request)
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	params := map[string]interface{}{}
	if r.FromDate != "" || r.ToDate != "" {
		if !utils.ValidateDate(r.FromDate) {
			return errors.SetErrorMessage(http.StatusBadRequest, "wrong date format")
		}

		if !utils.ValidateDate(r.ToDate) {
			return errors.SetErrorMessage(http.StatusBadRequest, "wrong date format")
		}

		dateFrom, _ := time.Parse("2006-01-02", r.FromDate)
		dateTo, _ := time.Parse("2006-01-02", r.ToDate)

		params["from"] = dateFrom
		params["to"] = dateTo.Add(time.Hour * 23).Add(time.Minute * 59).Add(time.Second * 59)
	}

	orderParam := map[string]interface{}{
		"start": (r.Page - 1) * r.ItemPerPage,
		"limit": r.ItemPerPage,
	}

	list, totalItem, _ := ph.PartnerUseCase.ListHistory(c.UserContext(), params, orderParam)
	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

// View History
func (ph *PartnerHandler) ViewHistory(c *fiber.Ctx) error {
	type request struct {
		Page        int    `query:"page" validate:"required,min=1"`
		ItemPerPage int    `query:"limit" validate:"required,min=1"`
		BatchNumber string `query:"batch_number" validate:"required"`
	}
	r := new(request)
	if err := c.QueryParser(r); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	if err := utils.ValidateStruct(r); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	orderParam := map[string]interface{}{
		"start": (r.Page - 1) * r.ItemPerPage,
		"limit": r.ItemPerPage,
	}

	list, totalItem, _ := ph.PartnerUseCase.ViewHistory(c.UserContext(), r.BatchNumber, orderParam)

	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

func (h *PartnerHandler) GetPartnersByCompanyId(c *fiber.Ctx) (err error) {
	companyId := c.Params("companyId")
	resp, err := h.PartnerUseCase.GetPartnerByCompanyId(c.UserContext(), companyId)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (ph *PartnerHandler) UpdateStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id := c.Params("id")
	req := new(request)

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = ph.PartnerUseCase.UpdateStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}
