-- migrate:up
CREATE TABLE company_product_provider_channel_mappings (
	id SERIAL NOT NULL PRIMARY KEY,
	company_product_id INT NOT NULL REFERENCES company_products(id),
	company_payment_provider_channel_mapping_id INT NOT NULL REFERENCES company_payment_provider_channel_mappings(id),
	fee_fix_value NUMERIC(20, 3) NOT NULL DEFAULT 0,
	fee_percentage NUMERIC(20, 3) NOT NULL DEFAULT 0,
	status BOOLEAN NOT NULL DEFAULT false,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

-- migrate:down
DROP TABLE company_product_provider_channel_mappings;