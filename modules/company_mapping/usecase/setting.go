package usecase

import (
	"github.com/go-redis/redis/v8"

	channelMapping "repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	company "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	channel "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	provider "repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	"repo.nusatek.id/nusatek/payment/utils/s3"
)

type defaultCompanyMappingUsecase struct {
	companyMappingRepo     CompanyMappingRepository
	counterRepo            CounterRepository
	customerFVARepo        CustomerFVARepository
	paymentProviderService provider.PaymentProviderUsecase
	channelMappingRepo     channelMapping.ChannelMappingRepository
	companyService         company.CompanyManagementUsecase
	channelService         channel.PaymentChannelUsecase
	cache                  *redis.Client
	s3                     *s3.Credential
}

func Setup() *defaultCompanyMappingUsecase {
	return &defaultCompanyMappingUsecase{}
}

func (s *defaultCompanyMappingUsecase) SetCompanyMappingRepo(t CompanyMappingRepository) *defaultCompanyMappingUsecase {
	s.companyMappingRepo = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetCounterRepo(t CounterRepository) *defaultCompanyMappingUsecase {
	s.counterRepo = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetCustomerFVARepo(t CustomerFVARepository) *defaultCompanyMappingUsecase {
	s.customerFVARepo = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetPaymentProviderService(t provider.PaymentProviderUsecase) *defaultCompanyMappingUsecase {
	s.paymentProviderService = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetChannelMappingRepo(t channelMapping.ChannelMappingRepository) *defaultCompanyMappingUsecase {
	s.channelMappingRepo = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetCompanyService(t company.CompanyManagementUsecase) *defaultCompanyMappingUsecase {
	s.companyService = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetChannelService(t channel.PaymentChannelUsecase) *defaultCompanyMappingUsecase {
	s.channelService = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetRedisClient(t *redis.Client) *defaultCompanyMappingUsecase {
	s.cache = t
	return s
}

func (s *defaultCompanyMappingUsecase) SetS3(t *s3.Credential) *defaultCompanyMappingUsecase {
	s.s3 = t
	return s
}

func (s *defaultCompanyMappingUsecase) Validate() CompanyMappingUsecase {
	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.channelMappingRepo == nil {
		panic("channel mapping repo is nil")
	}

	if s.channelService == nil {
		panic("channel service is nil")
	}

	if s.companyMappingRepo == nil {
		panic("company mapping repo is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	if s.paymentProviderService == nil {
		panic("payment provider service is nil")
	}

	if s.s3 == nil {
		panic("s3 is nil")
	}

	return s
}
