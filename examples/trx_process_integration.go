//go:build examples_only
// +build examples_only

// This file shows how to integrate Otto<PERSON>ay into the existing trx_process.go
// Add these methods to your cashinTransactionUsecase struct

package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"

	"repo.nusatek.id/nusatek/ottopay"
)

// Add OttoPay to the main ProcessCashinTransaction switch statement
func (u *cashinTransactionUsecase) ProcessCashinTransactionWithOttoPay(ctx context.Context, req entity.CashinTransactionReq) (entity.CashinTransactionRes, error) {
	// ... existing validation code ...

	// Get payment provider, channel, and mapping (existing code)
	paymentProvider, err := u.paymentProviderRepo.GetPaymentProviderByID(ctx, req.PaymentProviderID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	paymentChannel, err := u.paymentChannelRepo.GetPaymentChannelByID(ctx, req.PaymentChannelID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	companyPaymentProviderChannelMapping, err := u.companyPaymentProviderChannelMappingRepo.GetCompanyPaymentProviderChannelMappingByCompanyIDAndPaymentProviderIDAndPaymentChannelID(ctx, req.CompanyID, req.PaymentProviderID, req.PaymentChannelID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	// Add OttoPay to the provider switch statement
	switch paymentProvider.Name {
	case constants.ProviderXfers:
		return u.processXfersTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderXendit:
		return u.processXenditTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderNicePay:
		return u.processNicePayTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderOttocash:
		return u.processOttocashTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderOttoPay: // Add this case
		return u.processOttoPayTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	default:
		return entity.CashinTransactionRes{}, errors.New("payment provider not supported")
	}
}

// Add this new method to handle OttoPay transactions
func (u *cashinTransactionUsecase) processOttoPayTransaction(
	ctx context.Context,
	req entity.CashinTransactionReq,
	paymentProvider *domain.PaymentProviders,
	paymentChannel *domain.PaymentChannels,
	mapping *domain.CompanyPaymentProviderChannelMappings,
) (entity.CashinTransactionRes, error) {

	// Get OttoPay channel type from payment channel configuration
	// This allows flexible mapping from database payment channel codes to OttoPay channels
	channelType, err := u.getOttoPayChannelType(ctx, paymentChannel)
	if err != nil {
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to determine OttoPay channel type: %w", err)
	}

	// Generate VA number using OttoPay format
	prefix := ottopay.BankPrefixes[channelType]
	customerNumber := req.CustomerPhone
	if len(customerNumber) > 11 {
		customerNumber = customerNumber[len(customerNumber)-11:]
	}
	vaNumber := prefix + customerNumber

	// Generate invoice number (use existing method)
	invoiceNumber := u.generateInvoiceNumber()

	// Create cash_in_transaction record
	cashInTransaction := &domain.CashInTransactions{
		CompanyID:                              req.CompanyID,
		CompanyProductID:                       req.CompanyProductID,
		InvoiceNumber:                          invoiceNumber,
		CustomerName:                           req.CustomerName,
		CustomerPhone:                          req.CustomerPhone,
		CustomerEmail:                          req.CustomerEmail,
		PaymentProviderID:                      paymentProvider.ID,
		PaymentChannelID:                       paymentChannel.ID,
		CompanyPaymentProviderChannelMappingID: mapping.ID,
		Total:                                  req.Total,
		AdminFee:                               req.AdminFee,
		Discount:                               req.Discount,
		Voucher:                                req.Voucher,
		PgDeliveryFee:                          req.PgDeliveryFee,
		PaymentStatus:                          constants.PaymentPending,
		Status:                                 constants.CashInStatusPending,
		VirtualAccount:                         vaNumber,
		ExpiredAt:                              &req.ExpiredAt,
		CreatedAt:                              time.Now(),
		UpdatedAt:                              time.Now(),
	}

	// Begin transaction
	tx := u.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Save cash_in_transaction to database
	err := u.cashinRepo.CreateCashinTransaction(ctx, tx, cashInTransaction)
	if err != nil {
		tx.Rollback()
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to create cash_in_transaction: %w", err)
	}

	// Create transaction items
	for _, item := range req.Items {
		cashInItem := &domain.CashInTransactionItems{
			CashInTransactionID: cashInTransaction.ID,
			ProductName:         item.ProductName,
			ProductCode:         item.ProductCode,
			Quantity:            item.Quantity,
			Price:               item.Price,
			Status:              constants.CashInItemStatusPending,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}

		err = u.cashinRepo.CreateCashinTransactionItem(ctx, tx, cashInItem)
		if err != nil {
			tx.Rollback()
			return entity.CashinTransactionRes{}, fmt.Errorf("failed to create cash_in_transaction_item: %w", err)
		}
	}

	// Create transaction history
	history := &domain.CashInTransactionHistories{
		CashInTransactionID: cashInTransaction.ID,
		Description:         "OttoPay Virtual Account transaction created",
		PaymentStatus:       constants.PaymentPending,
		Status:              constants.CashInStatusPending,
		VirtualAccount:      vaNumber,
		ProviderId:          paymentProvider.ID,
		ChannelId:           paymentChannel.ID,
		PgReferenceInformation: fmt.Sprintf(`{
			"va_number": "%s",
			"channel_type": "%s",
			"bank_prefix": "%s",
			"customer_number": "%s",
			"provider": "ottopay"
		}`, vaNumber, channelType, prefix, customerNumber),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = u.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
	if err != nil {
		tx.Rollback()
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to create transaction history: %w", err)
	}

	// Commit transaction
	err = tx.Commit().Error
	if err != nil {
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Calculate grand total
	grandTotal := cashInTransaction.Total + cashInTransaction.AdminFee + cashInTransaction.PgDeliveryFee - cashInTransaction.Discount - cashInTransaction.Voucher

	// Return response following the same pattern as other providers
	return entity.CashinTransactionRes{
		ID:                  cashInTransaction.ID,
		InvoiceNumber:       cashInTransaction.InvoiceNumber,
		VirtualAccount:      vaNumber,
		Total:               cashInTransaction.Total,
		AdminFee:            cashInTransaction.AdminFee,
		Discount:            cashInTransaction.Discount,
		Voucher:             cashInTransaction.Voucher,
		PgDeliveryFee:       cashInTransaction.PgDeliveryFee,
		GrandTotal:          grandTotal,
		ExpiredAt:           cashInTransaction.ExpiredAt,
		PaymentProviderName: paymentProvider.Name,
		PaymentChannelName:  paymentChannel.Name,
		Status:              cashInTransaction.Status,
		PaymentStatus:       cashInTransaction.PaymentStatus,
		CreatedAt:           cashInTransaction.CreatedAt,
		UpdatedAt:           cashInTransaction.UpdatedAt,
	}, nil
}

// getOttoPayChannelType determines the OttoPay channel type from payment channel configuration
// This method allows flexible mapping from database payment channel codes to OttoPay channels
func (u *cashinTransactionUsecase) getOttoPayChannelType(ctx context.Context, paymentChannel *domain.PaymentChannels) (string, error) {
	// Option 1: Use channel configuration/metadata stored in database
	// You can store the OttoPay channel mapping in payment_channels.configuration or a separate table

	// Option 2: Use a configurable mapping (recommended)
	// This can be loaded from config file, environment variables, or database settings
	channelMapping := u.getOttoPayChannelMapping()

	if channelType, exists := channelMapping[paymentChannel.Code]; exists {
		return channelType, nil
	}

	// Option 3: Fallback to channel name-based mapping if code mapping not found
	return u.getChannelTypeFromName(paymentChannel.Name)
}

// getOttoPayChannelMapping returns the mapping from payment channel codes to OttoPay channel types
// This should be configurable and loaded from your application configuration
func (u *cashinTransactionUsecase) getOttoPayChannelMapping() map[string]string {
	// This mapping should be loaded from:
	// 1. Configuration file (config.json, config.yaml)
	// 2. Environment variables
	// 3. Database settings table
	// 4. Application configuration service

	// Example configuration that can be customized per deployment:
	return map[string]string{
		// Default codes - these can be changed based on your database setup
		"OTTOPAY_MANDIRI": ottopay.ChannelMandiri,
		"OTTOPAY_BRI":     ottopay.ChannelBRI,
		"OTTOPAY_BINA":    ottopay.ChannelBINA,
		"OTTOPAY_BNI":     ottopay.ChannelBNI,

		// Alternative codes that might be used in different environments
		"OTTO_MANDIRI": ottopay.ChannelMandiri,
		"OTTO_BRI":     ottopay.ChannelBRI,
		"OTTO_BINA":    ottopay.ChannelBINA,
		"OTTO_BNI":     ottopay.ChannelBNI,

		// Bank-specific codes
		"MANDIRI_VA": ottopay.ChannelMandiri,
		"BRI_VA":     ottopay.ChannelBRI,
		"BINA_VA":    ottopay.ChannelBINA,
		"BNI_VA":     ottopay.ChannelBNI,

		// Custom codes based on your database setup
		// Add your actual payment channel codes here
	}
}

// getChannelTypeFromName provides fallback mapping based on channel name
func (u *cashinTransactionUsecase) getChannelTypeFromName(channelName string) (string, error) {
	channelNameLower := strings.ToLower(channelName)

	switch {
	case strings.Contains(channelNameLower, "mandiri"):
		return ottopay.ChannelMandiri, nil
	case strings.Contains(channelNameLower, "bri"):
		return ottopay.ChannelBRI, nil
	case strings.Contains(channelNameLower, "bina"):
		return ottopay.ChannelBINA, nil
	case strings.Contains(channelNameLower, "bni"):
		return ottopay.ChannelBNI, nil
	default:
		return "", fmt.Errorf("unsupported channel name: %s", channelName)
	}
}

// Helper method to generate invoice number (if not already exists)
func (u *cashinTransactionUsecase) generateInvoiceNumber() string {
	// Use existing invoice generation logic or implement new one
	// This should follow your existing pattern
	timestamp := time.Now().Format("**************")
	return fmt.Sprintf("OTTOPAY%s", timestamp)
}

// Add method to get cash_in_transaction by VA number (if not exists)
func (u *cashinTransactionUsecase) GetCashinTransactionByVirtualAccount(ctx context.Context, vaNumber string) (*domain.CashInTransactions, error) {
	return u.cashinRepo.GetCashinTransactionByVirtualAccount(ctx, vaNumber)
}

// Example of how to add OttoPay configuration to your usecase
type OttoPayConfig struct {
	JWTSecret     string
	TokenDuration time.Duration
	AllowedIPs    []string
	EnableIPAuth  bool
}

// Add OttoPay service to your usecase struct (optional)
type cashinTransactionUsecaseWithOttoPay struct {
	*cashinTransactionUsecase
	ottoPayService ottopay.OttoPayService
	ottoPayConfig  OttoPayConfig
}

// Initialize OttoPay service
func (u *cashinTransactionUsecaseWithOttoPay) initOttoPayService() error {
	config := ottopay.NewConfigBuilder().
		WithJWTSecret(u.ottoPayConfig.JWTSecret).
		WithTokenDuration(u.ottoPayConfig.TokenDuration).
		WithAllowedIPs(u.ottoPayConfig.AllowedIPs).
		WithIPAuth(u.ottoPayConfig.EnableIPAuth).
		WithRequestTimeout(3 * time.Second).
		Build()

	// Create database adapter (implement based on your needs)
	dbAdapter := NewOttoPayDatabaseAdapter(u.cashinRepo, u.db)
	businessHandler := ottopay.NewDefaultBusinessHandler(dbAdapter, config)
	config.BusinessHandler = businessHandler

	u.ottoPayService = ottopay.NewService().Setup(config)
	return nil
}

// Example of how to validate OttoPay callback token
func (u *cashinTransactionUsecaseWithOttoPay) ValidateOttoPayToken(ctx context.Context, token string) (*ottopay.TokenClaims, error) {
	if u.ottoPayService == nil {
		return nil, errors.New("OttoPay service not initialized")
	}
	return u.ottoPayService.ValidateToken(ctx, token)
}
