package snapbca

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

type Credential struct {
	ClientID         string `json:"client_id"`
	ClientSecret     string `json:"client_secret"`
	PrivateKeyBase64 string `json:"private_key_base64"`
}

func (c Credential) PrivateKey() string {
	privKey, _ := base64.StdEncoding.DecodeString(c.PrivateKeyBase64)
	return string(privKey)
}

func GetCredential(secret string) (resp Credential, err error) {
	dec, err := utils.DecryptAES(secret, config.GetString("secret_key_company"))
	if err != nil {
		return
	}
	var cred Credential
	err = json.Unmarshal([]byte(dec), &cred)
	if err != nil {
		err = fmt.Errorf("unmarshan credential error: %v", err.<PERSON>rror())
		return
	}

	resp = cred
	return
}

type DoReq struct {
	Header  map[string]string
	Method  string
	Path    string
	ReqBody interface{}
}

type baseResp struct {
	ResponseCode    string `json:"responseCode"`
	ResponseMessage string `json:"responseMessage"`
}

type ibaseResp interface {
	getBaseResp() baseResp
}

func (b baseResp) getBaseResp() baseResp {

	return b
}

func (b baseResp) Error() string {

	return b.ResponseMessage
}

type AccessTokenB2BReq struct {
	GrantType string `json:"grantType"`
}

type AccessTokenB2BResp struct {
	baseResp
	AccessToken string `json:"accessToken"`
	TokenType   string `json:"tokenType"`
	ExpiresIn   string `json:"expiresIn"`
}

type TransferVaStatusReq struct {
	PartnerServiceID string `json:"partnerServiceId"`
	CustomerNo       string `json:"customerNo"`
	VirtualAccountNo string `json:"virtualAccountNo"`
	InquiryRequestID string `json:"inquiryRequestId"`
	PaymentRequestID string `json:"paymentRequestId"`
	AdditionalInfo   struct {
	} `json:"additionalInfo"`
}

func (r *TransferVaStatusReq) Build(partnerServiceID, custNo, reqId string) {
	partnerServiceID = snaputil.AddLeftPaddingSpace(partnerServiceID, 8)
	r.PartnerServiceID = partnerServiceID
	r.CustomerNo = custNo
	r.VirtualAccountNo = r.PartnerServiceID + r.CustomerNo
	r.InquiryRequestID = reqId
	r.PaymentRequestID = reqId

}

type TransferVaStatusResp struct {
	baseResp
	VirtualAccountData TransferVaStatusVADataResp `json:"virtualAccountData"`
	AdditionalInfo     struct {
	} `json:"additionalInfo"`
}

type TransferVaStatusVADataResp struct {
	PaymentFlagStatus string `json:"paymentFlagStatus"`
	PaymentFlagReason struct {
		Indonesia string `json:"indonesia"`
		English   string `json:"english"`
	} `json:"paymentFlagReason"`
	PartnerServiceID string `json:"partnerServiceId"`
	CustomerNo       string `json:"customerNo"`
	VirtualAccountNo string `json:"virtualAccountNo"`
	InquiryRequestID string `json:"inquiryRequestId"`
	PaymentRequestID string `json:"paymentRequestId"`
	PaidAmount       struct {
		Value    string `json:"value"`
		Currency string `json:"currency"`
	} `json:"paidAmount"`
	PaidBills   string `json:"paidBills"`
	TotalAmount struct {
		Value    string `json:"value"`
		Currency string `json:"currency"`
	} `json:"totalAmount"`
	TrxDateTime     time.Time `json:"trxDateTime"`
	TransactionDate time.Time `json:"transactionDate"`
	ReferenceNo     string    `json:"referenceNo"`
	PaymentType     string    `json:"paymentType"`
	FlagAdvise      string    `json:"flagAdvise"`
	BillDetails     []struct {
		BillCode        string `json:"billCode"`
		BillNo          string `json:"billNo"`
		BillName        string `json:"billName"`
		BillShortName   string `json:"billShortName"`
		BillDescription struct {
			English   string `json:"english"`
			Indonesia string `json:"indonesia"`
		} `json:"billDescription"`
		BillSubCompany string `json:"billSubCompany"`
		BillAmount     struct {
			Value    string `json:"value"`
			Currency string `json:"currency"`
		} `json:"billAmount"`
		AdditionalInfo struct {
			Value string `json:"value"`
		} `json:"additionalInfo"`
		BillReferenceNo string `json:"billReferenceNo"`
		Status          string `json:"status"`
		Reason          struct {
			English   string `json:"english"`
			Indonesia string `json:"indonesia"`
		} `json:"reason"`
	} `json:"billDetails"`
	FreeTexts []struct {
		English   string `json:"english"`
		Indonesia string `json:"indonesia"`
	} `json:"freeTexts"`
}
