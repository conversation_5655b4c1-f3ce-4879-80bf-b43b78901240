package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/modules/configuration/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type EmailTemplateHandler interface {
	CreateTemplate(c *fiber.Ctx) error
	UpdateTemplate(c *fiber.Ctx) error
	GetTemplates(c *fiber.Ctx) error
	GetTemplate(c *fiber.Ctx) error
	DeleteTemplate(c *fiber.Ctx) error
	GetTemplateByCompanyID(c *fiber.Ctx) error
	GetPartnersByCompanyId(c *fiber.Ctx) error
}

type templateHandler struct {
	uc usecase.TemplateUsecase
}

func NewEmailTemplateHandler(uc usecase.TemplateUsecase) EmailTemplateHandler {
	return &templateHandler{uc: uc}
}

func (h *templateHandler) CreateTemplate(c *fiber.Ctx) error {
	var templateReq entity.TemplateRequest

	if err := c.BodyParser(&templateReq); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	createdTemplate, err := h.uc.CreateTemplate(c.Context(), &templateReq)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	return response.HandleSuccess(c, createdTemplate)
}

func (h *templateHandler) UpdateTemplate(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "id is invalid")
	}

	var templateReq entity.TemplateRequest
	if err := c.BodyParser(&templateReq); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	updatedTemplate, err := h.uc.UpdateTemplate(c.Context(), int64(id), &templateReq)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	return response.HandleSuccess(c, updatedTemplate)
}

func (h *templateHandler) GetTemplates(c *fiber.Ctx) error {
	var paginate utils.Pagination

	if err := c.QueryParser(&paginate); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if paginate.Page == 0 {
		paginate.Page = 1
	}
	if paginate.Limit == 0 {
		paginate.Limit = 10
	}

	templates, totalData, err := h.uc.GetTemplates(c.Context(), paginate)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, templates)
}

func (h *templateHandler) GetTemplate(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid ID")
	}
	template, err := h.uc.GetTemplate(c.Context(), id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusNotFound, err.Error())
	}
	return response.HandleSuccess(c, template)
}

func (h *templateHandler) DeleteTemplate(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid ID")
	}
	if err := h.uc.DeleteTemplate(c.Context(), id); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	return response.HandleSuccess(c, nil)
}

func (h *templateHandler) GetTemplateByCompanyID(c *fiber.Ctx) error {
	companyID, err := strconv.ParseInt(c.Params("company_id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid Company ID")
	}
	templates, err := h.uc.GetTemplateByCompanyID(c.Context(), companyID)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	return response.HandleSuccess(c, templates)
}

func (h *templateHandler) GetPartnersByCompanyId(c *fiber.Ctx) error {
	companyID, err := strconv.ParseInt(c.Params("company_id"), 10, 64)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "Invalid Company ID")
	}
	partners, err := h.uc.GetPartnersByCompanyId(c.Context(), companyID)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, partners)
}
