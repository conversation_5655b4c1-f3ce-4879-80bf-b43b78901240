package snaputil

import (
	"context"
	"net/http"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
)

// key
type ctxKey int

const (
	client ctxKey = iota + 1
	serviceCode
	headerReq
)

func NewBaseRespCtx(ctx context.Context) domain.SnapBaseResp {
	br := domain.SnapBaseResp{
		ServiceCode: GetServiceCode(ctx),
	}
	br.Set(http.StatusOK, "00", "Successful")
	return br
}

func SetServiceCode(ctx context.Context, code string) context.Context {
	return context.WithValue(ctx, serviceCode, code)
}

func SetTimeout(ctx context.Context, timeout time.Duration) (context.Context, context.CancelFunc) {
	return context.WithTimeout(ctx, timeout)
}

func GetServiceCode(ctx context.Context) string {
	v, ok := ctx.Value(serviceCode).(string)
	if !ok {
		return ""
	}
	return v
}

func SetClient(ctx context.Context, c domain.SnapClient) context.Context {
	return context.WithValue(ctx, client, c)
}

func GetClient(ctx context.Context) domain.SnapClient {
	v, ok := ctx.Value(client).(domain.SnapClient)
	if !ok {
		return domain.SnapClient{}
	}
	return v
}

func SetHeaderReq(ctx context.Context, header domain.SnapHeaderReq) context.Context {
	return context.WithValue(ctx, headerReq, header)
}

func GetHeaderReq(ctx context.Context) domain.SnapHeaderReq {
	v, ok := ctx.Value(headerReq).(domain.SnapHeaderReq)
	if !ok {
		return domain.SnapHeaderReq{}
	}
	return v
}
