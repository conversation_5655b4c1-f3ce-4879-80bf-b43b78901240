package ottopay

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// DefaultConfig returns a default configuration for OttoPay service
func DefaultConfig() Config {
	return Config{
		JWTSecret:                 getEnv("OTTOPAY_JWT_SECRET", "default-secret-key"),
		TokenDuration:             getEnvDuration("OTTOPAY_TOKEN_DURATION", 24*time.Hour),
		Port:                      getEnv("OTTOPAY_PORT", "8080"),
		AllowedIPs:                getEnvStringSlice("OTTOPAY_ALLOWED_IPS", []string{"************", "*************"}),
		EnableIPAuth:              getEnvBool("OTTOPAY_ENABLE_IP_AUTH", true),
		BankPrefixes:              getDefaultBankPrefixes(),
		EnableSignatureValidation: getEnvBool("OTTOPAY_ENABLE_SIGNATURE_VALIDATION", false),
		SignatureSecret:           getEnv("OTTOPAY_SIGNATURE_SECRET", ""),
		RequestTimeout:            getEnvDuration("OTTOPAY_REQUEST_TIMEOUT", 3*time.Second),

		// Retry defaults (no env override per requirement)
		RetryMaxAttempts:       3,
		RetryInitialBackoff:    100 * time.Millisecond,
		RetryMaxBackoff:        2 * time.Second,
		RetryBackoffMultiplier: 2.0,
	}
}

// getDefaultBankPrefixes returns the default bank prefix configuration
func getDefaultBankPrefixes() map[string]string {
	prefixes := make(map[string]string)

	// Load from environment variables or use defaults
	prefixes[ChannelMandiri] = getEnv("OTTOPAY_PREFIX_MANDIRI", BankPrefixes[ChannelMandiri])
	prefixes[ChannelBRI] = getEnv("OTTOPAY_PREFIX_BRI", BankPrefixes[ChannelBRI])
	prefixes[ChannelBINA] = getEnv("OTTOPAY_PREFIX_BINA", BankPrefixes[ChannelBINA])
	prefixes[ChannelBNI] = getEnv("OTTOPAY_PREFIX_BNI", BankPrefixes[ChannelBNI])

	return prefixes
}

// LoadConfigFromEnv loads configuration from environment variables
func LoadConfigFromEnv() Config {
	config := DefaultConfig()

	// Override with environment variables if they exist
	if secret := os.Getenv("OTTOPAY_JWT_SECRET"); secret != "" {
		config.JWTSecret = secret
	}

	if duration := os.Getenv("OTTOPAY_TOKEN_DURATION"); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			config.TokenDuration = d
		}
	}

	if port := os.Getenv("OTTOPAY_PORT"); port != "" {
		config.Port = port
	}

	if ips := os.Getenv("OTTOPAY_ALLOWED_IPS"); ips != "" {
		config.AllowedIPs = strings.Split(ips, ",")
		// Trim whitespace from each IP
		for i, ip := range config.AllowedIPs {
			config.AllowedIPs[i] = strings.TrimSpace(ip)
		}
	}

	if enableIP := os.Getenv("OTTOPAY_ENABLE_IP_AUTH"); enableIP != "" {
		if enable, err := strconv.ParseBool(enableIP); err == nil {
			config.EnableIPAuth = enable
		}
	}

	if enableSig := os.Getenv("OTTOPAY_ENABLE_SIGNATURE_VALIDATION"); enableSig != "" {
		if enable, err := strconv.ParseBool(enableSig); err == nil {
			config.EnableSignatureValidation = enable
		}
	}

	if sigSecret := os.Getenv("OTTOPAY_SIGNATURE_SECRET"); sigSecret != "" {
		config.SignatureSecret = sigSecret
	}

	if timeout := os.Getenv("OTTOPAY_REQUEST_TIMEOUT"); timeout != "" {
		if d, err := time.ParseDuration(timeout); err == nil {
			config.RequestTimeout = d
		}
	}

	return config
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.JWTSecret == "" {
		return fmt.Errorf("JWT secret is required")
	}

	if c.TokenDuration <= 0 {
		return fmt.Errorf("token duration must be positive")
	}

	if c.Port == "" {
		return fmt.Errorf("port is required")
	}

	if c.EnableIPAuth && len(c.AllowedIPs) == 0 {
		return fmt.Errorf("allowed IPs must be specified when IP authentication is enabled")
	}

	if c.EnableSignatureValidation && c.SignatureSecret == "" {
		return fmt.Errorf("signature secret is required when signature validation is enabled")
	}

	if c.RequestTimeout <= 0 {
		return fmt.Errorf("request timeout must be positive")
	}

	if len(c.BankPrefixes) == 0 {
		return fmt.Errorf("bank prefixes configuration is required")
	}

	return nil
}

// GetBankPrefix returns the prefix for a given channel type
func (c *Config) GetBankPrefix(channelType string) (string, bool) {
	prefix, exists := c.BankPrefixes[channelType]
	return prefix, exists
}

// IsIPAllowed checks if an IP address is in the allowed list
func (c *Config) IsIPAllowed(ip string) bool {
	if !c.EnableIPAuth {
		return true
	}

	for _, allowedIP := range c.AllowedIPs {
		if ip == allowedIP {
			return true
		}
	}
	return false
}

// Helper functions for environment variable parsing
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		slice := strings.Split(value, ",")
		// Trim whitespace from each element
		for i, item := range slice {
			slice[i] = strings.TrimSpace(item)
		}
		return slice
	}
	return defaultValue
}

// ConfigBuilder provides a fluent interface for building configuration
type ConfigBuilder struct {
	config Config
}

// NewConfigBuilder creates a new configuration builder with default values
func NewConfigBuilder() *ConfigBuilder {
	return &ConfigBuilder{
		config: DefaultConfig(),
	}
}

// WithJWTSecret sets the JWT secret
func (b *ConfigBuilder) WithJWTSecret(secret string) *ConfigBuilder {
	b.config.JWTSecret = secret
	return b
}

// WithTokenDuration sets the token duration
func (b *ConfigBuilder) WithTokenDuration(duration time.Duration) *ConfigBuilder {
	b.config.TokenDuration = duration
	return b
}

// WithPort sets the server port
func (b *ConfigBuilder) WithPort(port string) *ConfigBuilder {
	b.config.Port = port
	return b
}

// WithAllowedIPs sets the allowed IP addresses
func (b *ConfigBuilder) WithAllowedIPs(ips []string) *ConfigBuilder {
	b.config.AllowedIPs = ips
	return b
}

// WithIPAuth enables or disables IP authentication
func (b *ConfigBuilder) WithIPAuth(enabled bool) *ConfigBuilder {
	b.config.EnableIPAuth = enabled
	return b
}

// WithBankPrefixes sets the bank prefixes
func (b *ConfigBuilder) WithBankPrefixes(prefixes map[string]string) *ConfigBuilder {
	b.config.BankPrefixes = prefixes
	return b
}

// WithSignatureValidation enables signature validation
func (b *ConfigBuilder) WithSignatureValidation(enabled bool, secret string) *ConfigBuilder {
	b.config.EnableSignatureValidation = enabled
	b.config.SignatureSecret = secret
	return b
}

// WithRequestTimeout sets the request timeout
func (b *ConfigBuilder) WithRequestTimeout(timeout time.Duration) *ConfigBuilder {
	b.config.RequestTimeout = timeout
	return b
}

// WithBusinessHandler sets the business logic handler
func (b *ConfigBuilder) WithBusinessHandler(handler BusinessLogicHandler) *ConfigBuilder {
	b.config.BusinessHandler = handler
	return b
}

// WithAPIKey sets the optional API key used to protect the token endpoint (x-api-key)
func (b *ConfigBuilder) WithAPIKey(apiKey string) *ConfigBuilder {
b.config.APIKey = apiKey
return b
}

// Build returns the final configuration
func (b *ConfigBuilder) WithRetryMaxAttempts(attempts int) *ConfigBuilder {
b.config.RetryMaxAttempts = attempts
return b
}

func (b *ConfigBuilder) WithRetryInitialBackoff(d time.Duration) *ConfigBuilder {
	b.config.RetryInitialBackoff = d
	return b
}

func (b *ConfigBuilder) WithRetryMaxBackoff(d time.Duration) *ConfigBuilder {
	b.config.RetryMaxBackoff = d
	return b
}

func (b *ConfigBuilder) WithRetryBackoffMultiplier(m float64) *ConfigBuilder {
	b.config.RetryBackoffMultiplier = m
	return b
}

func (b *ConfigBuilder) Build() Config {
	return b.config
}
