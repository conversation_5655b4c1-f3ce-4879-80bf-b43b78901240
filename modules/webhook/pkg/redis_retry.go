package redisretry

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// Config holds the configuration for the RedisRetry package.
type Config struct {
	RedisAddr       string        // Redis server address (e.g., "localhost:6379")
	Username        string        // Redis username (optional, for authentication)
	Password        string        // Redis password (optional, for authentication)
	Database        int           // Redis database number (default is 0)
	MaxConcurrency  int           // Maximum number of concurrent workers
	HandlerTimeout  time.Duration //	Timeout for handler execution
	DefaultTTL      time.Duration // Default TTL for keys in Redis
	ShutdownTimeout time.Duration // Timeout for graceful shutdown
}

// RedisRetry is a struct that holds the Redis client, context, and key handlers.
type RedisRetry struct {
	client         *redis.Client
	ctx            context.Context
	cancel         context.CancelFunc
	keyHandlers    map[string]func(string) // Map of key to its handler function
	keyHandlersMux sync.RWMutex            // Mutex to protect concurrent access to the map
	wg             sync.WaitGroup          // WaitGroup to track active handlers
	worker<PERSON><PERSON>     chan struct{}           // Worker pool for concurrency control
	config         Config                  // Configuration
}

func validateConfig(config Config) error {
	if config.RedisAddr == "" {
		return errors.New("RedisAddr is required")
	}
	if config.MaxConcurrency <= 0 {
		return errors.New("MaxConcurrency must be greater than 0")
	}
	return nil
}

func NewRedisRetry(config Config) (*RedisRetry, error) {
	if err := validateConfig(config); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(context.Background())

	client := redis.NewClient(&redis.Options{
		Addr:        config.RedisAddr,
		Username:    config.Username,
		Password:    config.Password,
		DB:          config.Database,
		ReadTimeout: -1,
	})

	workerPool := make(chan struct{}, config.MaxConcurrency)
	for i := 0; i < config.MaxConcurrency; i++ {
		workerPool <- struct{}{}
	}

	return &RedisRetry{
		client:      client,
		ctx:         ctx,
		cancel:      cancel,
		keyHandlers: make(map[string]func(string)),
		workerPool:  workerPool,
		config:      config,
	}, nil
}

func (rr *RedisRetry) GetClient() *redis.Client {
	if rr.client == nil {
		panic("redis client is nil")
	}

	return rr.client
}

// SaveDataWithTTL saves data to Redis with a TTL and stores the value in a separate key.
func (rr *RedisRetry) SaveDataWithTTL(key string, value interface{}, ttl time.Duration) error {
	// Save the original key with TTL
	err := rr.client.Set(rr.ctx, key, value, ttl).Err()
	if err != nil {
		return fmt.Errorf("failed to save data to Redis: %v", err)
	}

	// Save the value in a separate key with no TTL (or a longer TTL)
	err = rr.client.Set(rr.ctx, key+":value", value, time.Duration(24*2*time.Hour)).Err()
	if err != nil {
		return fmt.Errorf("failed to save value to Redis: %v", err)
	}

	return nil
}

// GetData retrieves the value associated with a key from Redis.
func (rr *RedisRetry) GetData(key string) (string, error) {
	value, err := rr.client.Get(rr.ctx, key+":value").Result()
	if err != nil {
		return "", fmt.Errorf("failed to get data for key %s: %v", key, err)
	}
	return value, nil
}

// ExtendTTL extends the TTL of a key by the specified duration.
func (rr *RedisRetry) ExtendTTL(key string, additionalTTL time.Duration) error {
	err := rr.client.Expire(rr.ctx, key, additionalTTL).Err()
	if err != nil {
		return fmt.Errorf("failed to extend TTL for key %s: %v", key, err)
	}
	return nil
}

// ResetTTL resets the TTL of a key to the specified duration.
func (rr *RedisRetry) ResetTTL(key string, newTTL time.Duration) error {
	err := rr.client.Expire(rr.ctx, key, newTTL).Err()
	if err != nil {
		return fmt.Errorf("failed to reset TTL for key %s: %v", key, err)
	}
	return nil
}

// RegisterKeyHandler registers a key and its handler function.
func (rr *RedisRetry) RegisterKeyHandler(key string, handler func(string)) {
	rr.keyHandlersMux.Lock()
	defer rr.keyHandlersMux.Unlock()

	rr.keyHandlers[key] = handler
}

// UnregisterKeyHandler unregisters a key and its handler function.
func (rr *RedisRetry) UnregisterKeyHandler(key string) {
	rr.keyHandlersMux.Lock()
	defer rr.keyHandlersMux.Unlock()

	delete(rr.keyHandlers, key)
}

// WaitForExpiration listens for key expiration events and processes them.
func (rr *RedisRetry) WaitForExpiration() error {
	// Set Redis configuration to enable key expiration notifications
	_, err := rr.client.ConfigSet(rr.ctx, "notify-keyspace-events", "Ex").Result()
	if err != nil {
		return fmt.Errorf("failed to set Redis config: %v", err)
	}
	log.Println("Redis config updated: notify-keyspace-events set to 'Ex'")

	// Subscribe to key space notifications for the configured database
	channel := fmt.Sprintf("__keyevent@%d__:expired", rr.config.Database)
	log.Printf("Running Redis retry service on database %d...\n", rr.config.Database)

	pubsub := rr.client.PSubscribe(rr.ctx, channel)
	defer pubsub.Close()

	// Listen for expiration events
	for {
		select {
		case <-rr.ctx.Done():
			// Context canceled, stop listening
			log.Printf("Redis retry service stopped: %v\n", rr.ctx.Err().Error())
			return nil
		default:
			msg, err := pubsub.ReceiveMessage(rr.ctx)
			if err != nil {
				if rr.ctx.Err() != nil {
					// Context canceled, stop listening
					log.Fatalf("Context canceled, stopping Redis retry service: %v\n", rr.ctx.Err().Error())
				}
				if err == redis.ErrClosed {
					// PubSub connection closed, stop listening
					log.Println("PubSub connection closed, stopping Redis retry service")
					return nil
				}
				return fmt.Errorf("failed to receive message: %v", err)
			}

			expiredKey := msg.Payload

			// Check if the expired key has a registered handler
			rr.keyHandlersMux.RLock()
			handler, exists := rr.keyHandlers[expiredKey]
			rr.keyHandlersMux.RUnlock()

			if exists {
				// Increment WaitGroup counter
				rr.wg.Add(1)

				// Acquire a worker from the pool
				<-rr.workerPool

				// Execute the handler for the expired key in a goroutine
				go func(key string) {
					defer func() {
						rr.wg.Done()                // Decrement WaitGroup counter when done
						rr.workerPool <- struct{}{} // Release the worker back to the pool
					}()

					// Execute the handler with a timeout
					ctx, cancel := context.WithTimeout(rr.ctx, rr.config.HandlerTimeout)
					defer cancel()

					done := make(chan struct{})
					go func() {
						handler(key)
						close(done)
					}()

					select {
					case <-done:
						// Handler completed successfully
					case <-ctx.Done():
						log.Printf("Handler for key %s timed out\n", key)
					}
				}(expiredKey)
			} else {
				log.Printf("No handler registered for expired key: %s\n", expiredKey)
			}
		}
	}
}

func (rr *RedisRetry) Shutdown() {
	// Cancel the context to stop the WaitForExpiration loop
	rr.cancel()

	// Wait for all active handlers to complete or timeout
	shutdownDone := make(chan struct{})
	go func() {
		rr.wg.Wait()
		close(shutdownDone)
	}()

	select {
	case <-shutdownDone:
		// All handlers completed
	case <-time.After(rr.config.ShutdownTimeout):
		// Shutdown timeout reached
		log.Println("Shutdown timeout reached, forcing shutdown...")
	}

	// Close the Redis client
	if err := rr.client.Close(); err != nil {
		log.Printf("Error closing Redis client: %v\n", err)
	}

	log.Println("RedisRetry shutdown complete")
}
