package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
)

func (r *defaultUserManagement) CreateRolePermission(ctx context.Context, req *domain.RolePermissions, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultUserManagement) UpdateRolePermission(ctx context.Context, req *domain.RolePermissions, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultUserManagement) GetRolePermissionById(ctx context.Context, id string) (resp *domain.RolePermissions, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultUserManagement) GetRolePermissionByRoleId(ctx context.Context, id string) (resp *[]domain.RolePermissions, err error) {
	err = r.db.WithContext(ctx).Where("role_id = ?", id).Order("updated_at DESC").Find(&resp).Error
	return
}

func (r *defaultUserManagement) GetRolePermissionByMenuId(ctx context.Context, id string) (resp *domain.RolePermissions, err error) {
	err = r.db.WithContext(ctx).Where("menu_id = ?", id).Take(&resp).Error
	return
}

func (r *defaultUserManagement) DeleteRolePermission(ctx context.Context, req *domain.RolePermissions) (err error) {
	err = r.db.WithContext(ctx).Delete(req).Error
	return
}



