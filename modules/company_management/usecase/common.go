package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCompanyManagement) getCacheCompany(ctx context.Context, companyCode string) (resp *domain.Companies, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompany, companyCode)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	switch true {
	case cache == "":
		prod, errRes := s.companyRepo.GetCompanyByCode(ctx, companyCode)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCompany(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Companies
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCompanyManagement) setCacheCompany(ctx context.Context, req *domain.Companies) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCompany, req.Code)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyManagement) deleteCacheCompany(ctx context.Context, companyCode string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompany, companyCode)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyManagement) getCacheCompanySecrets(ctx context.Context, secrets string) (resp *domain.Companies, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanySecrets, secrets)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}

	}

	switch true {
	case cache == "":
		prod, errRes := s.companyRepo.GetCompanyBySecret(ctx, secrets)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCompanySecrets(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Companies
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCompanyManagement) setCacheCompanySecrets(ctx context.Context, req *domain.Companies) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCompanySecrets, req.Secrets)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyManagement) deleteCacheCompanySecrets(ctx context.Context, secrets string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCompanySecrets, secrets)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}
