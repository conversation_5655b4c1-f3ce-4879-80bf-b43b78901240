package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/user_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

func (r *defaultUserManagement) BeginTrans() *gorm.DB {
	return r.db.Begin()
}

func (r *defaultUserManagement) CreateUser(ctx context.Context, req *domain.Users, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultUserManagement) UpdateUser(ctx context.Context, req *domain.Users, tx *gorm.DB) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultUserManagement) GetUserById(ctx context.Context, id int) (resp *domain.Users, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultUserManagement) GetUserByEmail(ctx context.Context, email string) (resp *domain.Users, err error) {
	err = r.db.WithContext(ctx).Where("email = ?", email).Take(&resp).Error
	return
}

func (r *defaultUserManagement) DeleteUser(ctx context.Context, req *domain.Users) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("user_id = ?", req.ID).Delete(&domain.UserCompanies{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultUserManagement) SearchUserList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.UserList, totalData int64, err error) {
	query := func(db *gorm.DB) *gorm.DB {
		condition := db.Where("u.deleted_at IS NULL")
		if paginate.Value != "" {
			searchValue := "%" + paginate.Value + "%"
			switch paginate.Key {
			case "email":
				condition.Where("u.email ILIKE ?", searchValue)
			case "name":
				condition.Where("u.\"name\" ILIKE ?", searchValue)
			case "role":
				condition.Where("s.\"name\" ILIKE ?", searchValue)
			case "status":
				condition.Where("s.status = ?", searchValue)
			default:
				condition.Where("CONCAT(u.\"name\", ' ', u.email) ILIKE ?", searchValue)
			}
		}
		return condition
	}

	joinQuery := "JOIN roles s ON u.role_id = s.id"
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("users u").
		Select("u.id, u.\"name\", u.email, s.\"name\" as role_name, u.last_login_at, u.status, u.role_id").Joins(joinQuery).
		Scopes(query).Order(paginate.GetOrderBy("u")).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Table("users u").Joins(joinQuery).Scopes(query).Count(&totalData).Error
	return
}
