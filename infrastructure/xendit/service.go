package xendit

import "context"

type RestXendit interface {
	CreatePaymentVA(ctx context.Context, req CreateVARequest, credential Credential) (resp VaResponse, err error)
	UpdatePaymentVA(ctx context.Context, req UpdateVARequest, credential Credential) (resp VaResponse, err error)
	CheckStatusPaymentVA(ctx context.Context, paymentId string, credential Credential) (resp VaResponse, err error)
	CreatePaymentEwallet(ctx context.Context, req CreateEwalletRequest, credential Credential) (resp CreateEwalletResponse, err error)
	CheckStatusPaymentEwallet(ctx context.Context, chargeId string, credential Credential) (resp CreateEwalletResponse, err error)
	CreatePaymentInvoice(ctx context.Context, req CreateInvoiceRequest, credential Credential) (resp CreateInvoiceResponse, err error)
	CheckStatusPaymentInvoice(ctx context.Context, id string, credential Credential) (resp CreateInvoiceResponse, err error)
	CheckByExternalId(ctx context.Context, externalId string, credential Credential) (resp []DisbursementCheckStatusResponse, err error)
	CreateDisbursement(ctx context.Context, req DisbursementRequest, credential Credential) (resp DisbursementResponse, statusCode int, err error)
	CheckStatusDisbursement(ctx context.Context, id string, credential Credential) (resp DisbursementCheckStatusResponse, err error)
	// retail
	CreateFixedPaymentCode(ctx context.Context, req CreateFixedPaymentCodeRequest, credential Credential) (resp FixedPaymentCodeResponse, err error)
	UpdateFixedPaymentCode(ctx context.Context, req UpdateFixedPaymentCodeRequest, credential Credential) (resp FixedPaymentCodeResponse, err error)
	GetFixedPaymentCode(ctx context.Context, fixedPaymentCodeId string, credential Credential) (resp GetFixedPaymentCodeResponse, err error)
	GetFixedPaymentCodePayments(ctx context.Context, fixedPaymentCodeId string, credential Credential) (resp GetFixedPaymentCodePaymentsResponse, err error)
}
