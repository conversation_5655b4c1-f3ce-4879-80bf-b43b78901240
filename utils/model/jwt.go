package model

type JwtTokenShow struct {
	UserId        int               `json:"user_id"`
	Username      string            `json:"username"`
	Exp           uint32            `json:"exp"`
	RoleId        int               `json:"role_id"`
	UserCompanyId []int             `json:"user_company_id"`
	RoleName      string            `json:"role_name"`
	Token         string            `json:"token"`
	Permissions   []RolePermissions `json:"role_permissions"`
}

type RolePermissions struct {
	ID          int             `json:"id"`
	MenuId      int             `json:"menu_id"`
	MenuCode    string          `json:"menu_code"`
	Permissions map[string]bool `json:"permission"`
}

type JwtToken struct {
	UserId        int    `json:"user_id"`
	Exp           uint32 `json:"exp"`
	RoleId        int    `json:"role_id"`
	Permissions   string `json:"permissions"`
}

type RoleUserPermission struct {
	View   bool `json:"view"`
	Create bool `json:"create"`
	Edit   bool `json:"edit"`
	Delete bool `json:"delete"`
}
