// SNAP BCA implementation
package snapbca

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/go-resty/resty/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

const (
	cacheAccessTokenKey = "snap-bca-access-token:%s" // client id

	headerAuthorization = "Authorization"
	headerTimestamp     = "X-TIMESTAMP"
	headerClientKey     = "X-CLIENT-KEY"
	headerSignature     = "X-SIGNATURE"
	headerChannelID     = "CHANNEL-ID"
	headerPartnerID     = "X-PARTNER-ID"
	headerExternalID    = "X-EXTERNAL-ID"
)

type Wrapper struct {
	Endpoint  string
	isSandbox bool
	client    *resty.Client
	cache     *redis.Client
}

func NewWrapper() *Wrapper {
	return &Wrapper{}
}

func (w *Wrapper) Setup() *Wrapper {
	var (
		EndpointSandbox = config.GetString("snap_bca.sandbox")
		Endpoint        = config.GetString("snap_bca.host")
	)

	w.isSandbox = config.GetBool("snap_bca.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	r := resty.New()
	r = r.SetTimeout(config.GetDuration("snap_bca.timeout")).SetDebug(config.GetBool("snap_bca.debug"))

	if config.GetBool("snap_bca.skiptls") {
		r = r.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}
	if config.GetString("snap_bca.proxy") != "" {
		r = r.SetProxy(config.GetString("snap_bca.proxy"))
	} else {
		r = r.RemoveProxy()
	}

	w.client = r

	return w
}

func (w *Wrapper) WithRedis(rc *redis.Client) *Wrapper {
	w.cache = rc
	return w
}

func (w *Wrapper) do(ctx context.Context, req DoReq, result ibaseResp) (res *resty.Response, err error) {
	fullURL := w.Endpoint + req.Path

	// default header
	if req.Header == nil {
		req.Header = make(map[string]string)
	}
	req.Header["Content-Type"] = "application/json"

	// prepare http client
	r := w.client.R().
		SetHeaders(req.Header)
	if req.ReqBody != nil {
		r.SetBody(req.ReqBody)
	}

	var rr *resty.Response
	logger.Info(ctx, "request info", logger.String("fullURL", fullURL), logger.Any("header", r.Header), logger.Any("body", req.ReqBody))
	switch req.Method {
	case http.MethodGet:
		rr, err = r.Get(fullURL)
	case http.MethodPost:
		rr, err = r.Post(fullURL)
	case http.MethodPut:
		rr, err = r.Put(fullURL)
	case http.MethodPatch:
		rr, err = r.Patch(fullURL)
	case http.MethodDelete:
		rr, err = r.Delete(fullURL)
	}
	res = rr
	if err != nil {
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response info", logger.Any("header", rr.Header()), logger.Int("statusCode", rr.StatusCode()), logger.String("body", string(rr.Body())))

	err = json.Unmarshal(rr.Body(), &result)
	if err != nil {
		logger.Error(ctx, "err unmarshal", logger.Err(err))
		return
	}

	if rr.StatusCode() < 200 || rr.StatusCode() > 299 {
		// if rr.StatusCode() == http.StatusUnauthorized {

		// }
		err = result.getBaseResp()
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	return res, nil
}

func (w *Wrapper) getAccessToken(ctx context.Context, cred Credential) (accessToken string, err error) {
	cacheKey := fmt.Sprintf(cacheAccessTokenKey, cred.ClientID)
	cacheToken, err := w.cache.Get(ctx, cacheKey).Result()
	if err != nil && err != redis.Nil {
		logger.Error(ctx, "err get cache", logger.Err(err))
		return
	}

	if err == nil {
		accessToken = cacheToken
		return
	}

	resp, err := w.AccessTokenB2B(ctx, AccessTokenB2BReq{
		GrantType: "client_credentials",
	}, cred)
	if err != nil {
		logger.Error(ctx, "access token b2b", logger.Err(err))
		return
	}

	exp, _ := strconv.Atoi(resp.ExpiresIn) // in second
	if exp <= 0 {
		exp = 600 // default for bca
	}
	// exp := 300
	accessToken = resp.AccessToken
	err = w.cache.Set(ctx, cacheKey, accessToken, time.Duration(exp)*time.Second).Err()
	if err != nil {
		logger.Error(ctx, "set access token cache", logger.Err(err))
		return
	}

	return
}

func (w *Wrapper) doWithSignature(ctx context.Context, req DoReq, result ibaseResp, cred Credential) (err error) {
	for i := 1; i <= 3; i++ { // 3 attempt
		accessToken, err := w.getAccessToken(ctx, cred)
		if err != nil {
			return err
		}
		req.Header[headerAuthorization] = "Bearer " + accessToken

		err = w.buildReqHeader(ctx, accessToken, req, cred)
		if err != nil {
			logger.Error(ctx, "err build header", logger.Err(err))
			return err
		}

		httpRes, err := w.do(ctx, req, result)
		if err != nil {
			logger.Error(ctx, "err request", logger.Err(err))
			if httpRes.StatusCode() == http.StatusUnauthorized {
				logger.Infof(ctx, "get 'Unauthorized' attempt %v", i)
				continue // if get "StatusUnauthorized" do next iteration, so client can get new token in case get expired token
			}
			return err
		}

		return nil
	}

	return
}
