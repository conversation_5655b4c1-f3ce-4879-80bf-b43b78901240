package interfacepkg

import (
	"encoding/json"
	"fmt"
)

// <PERSON> convert interface json to string
func <PERSON>(data interface{}) (res string) {
	name, err := json.<PERSON>(data)
	if err != nil {
		return res
	}
	res = string(name)

	return res
}

func Convert(data interface{}, cb interface{}) (err error) {
	dataString := Marshal(data)
	fmt.Println("interfacepkg.Convert dataString : ", dataString)
	err = json.Unmarshal([]byte(dataString), &cb)
	if err != nil {
		fmt.Println("interfacepkg.Convert err", err)
	}
	return err
}
