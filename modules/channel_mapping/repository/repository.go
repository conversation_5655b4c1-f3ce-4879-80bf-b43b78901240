package repository

import (
	"context"
	"strings"

	"repo.nusatek.id/nusatek/payment/domain"
	entity "repo.nusatek.id/nusatek/payment/modules/channel_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	"repo.nusatek.id/nusatek/payment/utils"

	"gorm.io/gorm"
)

type defaultChannelMappingRepo struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.ChannelMappingRepository {
	return &defaultChannelMappingRepo{db}
}

func (r *defaultChannelMappingRepo) CreateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultChannelMappingRepo) UpdateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultChannelMappingRepo) GetChannelMappingById(ctx context.Context, id string) (resp *domain.PaymentProviderChannelMappings, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultChannelMappingRepo) ChekExistingRelationDataCompany(ctx context.Context, providerId, channelID int64, capability int) (existingData bool, err error) {

	var id int
	err = r.db.Debug().WithContext(ctx).Table("company_payment_provider_channel_mappings").Select(`id`).
		Where("payment_provider_id = ? AND payment_channel_id = ? AND capability = ?", providerId, channelID, capability).
		Limit(1).Order("id desc").Find(&id).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return
	}

	return id != 0, nil
}

func (r *defaultChannelMappingRepo) DeleteChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error) {
	err = r.db.Debug().WithContext(ctx).Delete(req).Error
	return
}

func (r *defaultChannelMappingRepo) GetListChannelMappingByProviderId(ctx context.Context, params domain.GetPaymentChannelsParams, providerID string) (resp *[]entity.PaymentChannelMappingList, totalData int64, err error) {
	joinQuery := `
	JOIN payment_channels c ON m.payment_channel_id = c.id 
	JOIN payment_channel_types pct on pct.id = c.payment_channel_type_id`
	whereQuery := "m.payment_provider_id = ? AND m.deleted_at IS NULL"

	orderByMap := map[string]string{
		"payment_channel_name":  "c.name",
		"code":                  "m.code",
		"provider_channel_code": "m.provider_channel_code",
		"max_transaction":       "m.max_transaction",
		"sla":                   "m.sla",
		"cash_in":               "m.capability",
		"cash_out":              "m.capability",
	}

	if params.Pagination.Order == "cash_out" {
		if strings.ToLower(params.Pagination.Sort) == "asc" {
			params.Pagination.Sort = "desc"
		} else if strings.ToLower(params.Pagination.Sort) == "desc" {
			params.Pagination.Sort = "asc"
		}
	}

	q := r.db.Debug().WithContext(ctx).Table("payment_provider_channel_mappings m").
		Select(`m.id, c."name" as payment_channel_name, m.code, c.id as payment_channel_id, m.max_transaction, 
		m.sla, m.capability, m.provider_channel_code, pct.config_templates "channel_type_config_templates", m.payment_instructions`).
		Joins(joinQuery).Where(whereQuery, providerID)

	if params.IsAvailable && params.CompanyID > 0 {
		q = q.Joins(`LEFT JOIN company_payment_provider_channel_mappings cppcm ON cppcm.payment_provider_id = m.payment_provider_id 
		AND cppcm.payment_channel_id = m.payment_channel_id  AND cppcm.capability = m.capability AND cppcm.company_id = ? AND cppcm.deleted_at IS NULL`, params.CompanyID).
			Where("cppcm.id IS NULL")
	}

	if params.Pagination.Search != "" || (params.Pagination.Key == "" && params.Pagination.Value != "") {
		searchValue := "%" + params.Pagination.Search + "%"
		if params.Pagination.Value != "" {
			searchValue = "%" + params.Pagination.Value + "%"
		}
		q = q.Where("c.name ILIKE ? OR m.code ILIKE ? OR m.provider_channel_code ILIKE ?", searchValue, searchValue, searchValue)
	}

	if params.Pagination.Limit != 0 && params.Pagination.Page != 0 {
		err = r.db.WithContext(ctx).Table("payment_provider_channel_mappings m").Joins(joinQuery).
			Where(whereQuery, providerID).Count(&totalData).Error
		if err != nil {
			return
		}
		q = q.Scopes(utils.Paginate(params.Pagination.Page, params.Pagination.Limit))
	}

	err = q.Order(params.Pagination.GetOrderByMaps("m", orderByMap)).Find(&resp).Error
	if err != nil {
		return
	}

	return
}

func (r *defaultChannelMappingRepo) GetChannelMappingCashIn(ctx context.Context, providerId, channelId string) (resp *domain.PaymentProviderChannelMappings, err error) {
	err = r.db.WithContext(ctx).Where("capability = 1 AND payment_provider_id = ? AND payment_channel_id = ?", providerId, channelId).First(&resp).Error
	return
}

func (r *defaultChannelMappingRepo) GetChannelMappingByProviderChannelIdAndCapability(ctx context.Context, providerId, channelId, cabability string) (resp *domain.PaymentProviderChannelMappings, err error) {
	err = r.db.WithContext(ctx).Where("payment_provider_id = ? AND payment_channel_id = ? AND capability = ?", providerId, channelId, cabability).Take(&resp).Error
	return
}

func (r *defaultChannelMappingRepo) GetChannelMappingByCode(ctx context.Context, code string) (resp *domain.PaymentProviderChannelMappings, err error) {
	// TODO: implement caching
	err = r.db.WithContext(ctx).Where("code = ?", code).Take(&resp).Error
	return
}
