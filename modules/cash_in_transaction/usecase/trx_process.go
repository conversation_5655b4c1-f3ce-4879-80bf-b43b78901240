package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers/payment_method"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (s *defaultCashinTransaction) transactionProviderProcess(ctx context.Context, req *entity.CashinTransactionProcess) (resp entity.CashinTranscationResponse, err error) {
	tx := s.cashinRepo.BeginTrans()
	defer tx.Rollback()
	req.Tx = tx
	// commenting because we dont know what purpose of this
	// referenceId := fmt.Sprintf("%s-%s%v-%s%v", req.CashinTransaction.InvoiceNumber, "PRO", req.CashinTransaction.PaymentProviderID, "CH", req.CashinTransaction.PaymentChannelID)
	referenceId := req.CashinTransaction.InvoiceNumber
	req.CashinTransaction.InvoiceNumber = referenceId

	switch req.Command {
	case entity.CommandCreateTransaction:
		_, err = s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, req.CashinTransaction.InvoiceNumber)
		if err == nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, "invoice number has already been taken")
			logger.Error(ctx, err.Error())
			return
		}

		err = s.cashinRepo.CreateCashinTransaction(ctx, tx, &req.CashinTransaction)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		ctx = logger.AppendCtxData(ctx, "cashinID", req.CashinTransaction.ID)

	case entity.CommandUpdateTransaction:
		err = s.cashinRepo.UpdateCashinTransaction(ctx, tx, &req.CashinTransaction)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		ctx = logger.AppendCtxData(ctx, "cashinID", req.CashinTransaction.ID)

	default:
		err = errors.SetErrorMessage(http.StatusForbidden, "command not recognized")
		logger.Error(ctx, err.Error())
		return
	}

	history := &domain.CashInTransactionHistories{
		Description:            req.Description,
		Status:                 constants.StatusPending,
		PaymentStatus:          constants.PaymentDraft,
		ChannelId:              req.CashinTransaction.PaymentChannelID,
		ProviderId:             req.CashinTransaction.PaymentProviderID,
		CashInTransactionID:    req.CashinTransaction.ID,
		InvoiceNumber:          referenceId,
		PgReferenceInformation: "{}",
	}

	err = s.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var resByte []byte
	var virtualAccount string
	trxTotal := req.CashinTransaction.Total + req.CashinTransaction.ProductFee
	switch true {
	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderXfers):
		resp, resByte, virtualAccount, err = s.xfersTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}

	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderXendit):
		resp, resByte, virtualAccount, err = s.xenditTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}

	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderOttocash):
		resp, resByte, virtualAccount, err = s.ottocashTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}

	case strings.ToLower(req.ProviderName) == constants.ProviderNicePay:
		resp, resByte, virtualAccount, err = s.nicepayTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}
	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderDoku):
		resp, resByte, virtualAccount, err = s.dokuTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}

	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderCashlez):
		resp, resByte, virtualAccount, err = s.cashlezTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}
	case strings.Contains(strings.ToLower(req.ProviderName), constants.ProviderBankBCA):
		resp, resByte, virtualAccount, err = s.bankBcaTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}
	case strings.ToLower(req.ProviderName) == constants.ProviderSnapNicePay:
		resp, resByte, virtualAccount, err = s.snapNicepayTransactionProcess(ctx, req, trxTotal, referenceId)
		if err != nil {
			return
		}

	default:
		err = errors.SetErrorMessage(http.StatusNotFound, "payment provider is not yet in service")
		return
	}
	history.VirtualAccount = virtualAccount
	ctx = logger.AppendCtxData(ctx, "va", virtualAccount)
	history.PgReferenceInformation = string(resByte)
	errRes := s.cashinRepo.UpdateCashinTransactionHistory(ctx, tx, history)
	if errRes != nil {
		logger.Error(ctx, errRes.Error())
	}

	tx.Commit()
	if errRes := s.setCacheCashinTransaction(ctx, &req.CashinTransaction); errRes != nil {
		logger.Error(ctx, errRes.Error())
	}
	return
}

func (s *defaultCashinTransaction) xenditTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, err := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if err != nil {
		return
	}

	credential, err := xendit.GetXenditCredential(req.Credential)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	switch true {
	case channelType.PaymentType == constants.TrxVirtualAccount:
		loc, _ := time.LoadLocation("UTC")
		txTime := req.ExpiredTime.In(loc)
		var respXendit xendit.VaResponse

		vaSetting := req.CompanyMapping.ChannelTypeConfigs.ToVA()
		addInfo := req.CashinTransaction.AdditionalInfo.ToVA()
		logger.Info(ctx, fmt.Sprintf("vaSetting %+v", vaSetting))
		var fvaCust domain.FixedVACustomer

		switch true {
		case !vaSetting.IsActiveFixedVirtualAccount || addInfo.IsAllowedRandomVA:
			reqXendit := xendit.CreateVARequest{
				ExternalId:     referenceId,
				BankCode:       req.ProviderChannelCode,
				Name:           req.CashinTransaction.CustomerName,
				IsClosed:       true,
				ExpectedAmount: totalAmount,
				ExpirationDate: txTime.In(time.FixedZone("UTC+7", 7*3600)).Format(time.RFC3339),
				IsSingleUse:    true,
			}

			respXendit, err = s.xenditRest.CreatePaymentVA(ctx, reqXendit, credential)
			if err != nil {
				return
			}

		case vaSetting.IsActiveFixedVirtualAccount:
			fvaCust, err = s.companyMappingService.GetOrCreateCustomerFVA(ctx, false, req.CashinTransaction.CustomerPhone, req.CompanyMapping)
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when create fixed va customer %v", err.Error()))
				logger.Error(ctx, err.Error())
				return
			}
			logger.Info(ctx, fmt.Sprintf("fvaCust %+v", fvaCust))

			if len(fvaCust.LastPgReferenceId) <= 0 { // create va, if va not exist
				reqXendit := xendit.CreateVARequest{
					ExternalId:           referenceId,
					BankCode:             req.ProviderChannelCode,
					Name:                 req.CashinTransaction.CustomerName,
					VirtualAccountNumber: vaSetting.Generate(fvaCust.Counter),
					IsClosed:             true,
					ExpectedAmount:       totalAmount,
					ExpirationDate:       txTime.In(time.FixedZone("UTC+7", 7*3600)).Format(time.RFC3339),
					IsSingleUse:          false,
				}

				respXendit, err = s.xenditRest.CreatePaymentVA(ctx, reqXendit, credential)
				errCode := errors.GetErrorCode(err)
				if err != nil && errCode != http.StatusConflict {
					return
				}
			}

			if len(fvaCust.LastPgReferenceId) > 0 { // id already have VA, check payment status and update va amount
				var xenditExistVA xendit.VaResponse
				xenditExistVA, err = s.xenditRest.CheckStatusPaymentVA(ctx, fvaCust.LastPgReferenceId, credential)
				errCode := errors.GetErrorCode(err)
				if err != nil && errCode != http.StatusNotFound {
					err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when get lastest fixed va transaction %v", err.Error()))
					logger.Error(ctx, err.Error())
					return
				}

				if xenditExistVA.Status == constants.XenditVAStatusActive {
					err = errors.SetErrorMessage(http.StatusBadRequest, "Anda memiliki tagihan yang masih berlangsung.")
					logger.Error(ctx, err.Error())
					return
				}

				// need to update VA if va already exist
				updateVaReq := xendit.UpdateVARequest{
					ID:             fvaCust.LastPgReferenceId,
					ExternalId:     referenceId,
					ExpectedAmount: totalAmount,
					ExpirationDate: txTime.In(time.FixedZone("UTC+7", 7*3600)).Format(time.RFC3339),
				}
				respXendit, err = s.xenditRest.UpdatePaymentVA(ctx, updateVaReq, credential)
				if err != nil {
					err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when update fixed va xendit %v", err.Error()))
					logger.Error(ctx, err.Error())
					return
				}
			}

			// always update latest fixed va info
			err = s.companyMappingService.UpdateFixedVACustomer(ctx, req.Tx, &domain.FixedVACustomer{
				ID:                fvaCust.ID,
				Name:              respXendit.Name,
				LastPgReferenceId: respXendit.ID,
				VANumber:          respXendit.AccountNumber, // include merchant code
				ExpiredAt:         txTime,
			})
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when update fixed va customer %v", err.Error()))
				logger.Error(ctx, err.Error())
				return
			}

		default:
			err = fmt.Errorf("invalid case va")
			return
		}

		providerRes, _ = json.Marshal(respXendit)
		resp = generateResponseXenditVACashin(respXendit, req)
		virtualAccount = respXendit.AccountNumber

	case channelType.PaymentType == constants.TrxEwallet:
		reqXendit := xendit.CreateEwalletRequest{
			ReferenceId:    referenceId,
			Currency:       "IDR",
			Amount:         totalAmount,
			CheckoutMethod: "ONE_TIME_PAYMENT",
			ChannelCode:    req.ProviderChannelCode,
			ChannelProperties: xendit.ChannelProperties{
				MobileNumber:       req.MobileNumber,
				SuccessRedirectUrl: req.SuccessRedirectUrl,
				FailureRedirectUrl: req.FailureRedirectUrl,
			},
		}
		respXendit, errRes := s.xenditRest.CreatePaymentEwallet(ctx, reqXendit, credential)
		if errRes != nil {
			err = errRes
			return
		}

		providerRes, _ = json.Marshal(respXendit)
		resp = generateResponseXenditEwallet(respXendit, req)
		virtualAccount = req.MobileNumber

	case channelType.PaymentType == constants.TrxCc:
		diff := time.Until(req.ExpiredTime).Seconds()
		reqXendit := xendit.CreateInvoiceRequest{
			ExternalId:      referenceId,
			Amount:          totalAmount,
			PayerEmail:      req.CashinTransaction.CustomerEmail,
			Description:     referenceId,
			InvoiceDuration: int(diff),
		}

		respXendit, errRes := s.xenditRest.CreatePaymentInvoice(ctx, reqXendit, credential)
		if errRes != nil {
			err = errRes
			return
		}

		providerRes, _ = json.Marshal(respXendit)
		resp = generateResponseXenditInvoiceCashin(respXendit, req)

	case channelType.PaymentType == constants.TrxRetail:
		loc, _ := time.LoadLocation("UTC")
		txTime := req.ExpiredTime.In(loc)
		reqXendit := xendit.CreateFixedPaymentCodeRequest{
			ExternalID:       referenceId,
			RetailOutletName: req.ProviderChannelCode,
			Name:             req.CashinTransaction.CustomerName,
			ExpectedAmount:   totalAmount,
			IsSingleUse:      true, //value that determines whether a fixed payment code will be inactive after it is paid or not
			ExpirationDate:   txTime.In(time.FixedZone("UTC+7", 7*3600)).Format(time.RFC3339),
		}
		respXendit, errRes := s.xenditRest.CreateFixedPaymentCode(ctx, reqXendit, credential)
		if errRes != nil {
			err = errRes
			return
		}

		providerRes, _ = json.Marshal(respXendit)
		resp = generateResponseXenditFixedPaymentCodeCashin(respXendit, req)
	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) xfersTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}

	credential, errRes := xfers.GetXfersCredential(req.Credential)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var reqPayments xfers.PaymentRequest
	switch true {
	case channelType.PaymentType == constants.TrxVirtualAccount:
		reqPayments = xfers.PaymentRequest{
			PaymentData: xfers.PaymentDataReq{
				Attributes: xfers.AttributeValueRequest{
					PaymentMethodType: payment_method.VirtualAccount,
					Amount:            totalAmount,
					ReferenceID:       referenceId,
					ExpiredAt:         req.ExpiredTime,
					Description:       req.Description,
					PaymentMethodOptions: xfers.PaymentOptionVA{
						BankShortCode: req.ProviderChannelCode,
						DisplayName:   req.CashinTransaction.CustomerName,
						// SuffixNo:      req.CashinTransaction.CustomerPhone[len(req.CashinTransaction.CustomerPhone)-6:],
					},
				},
			},
		}

		respPayment, errResp := s.xfersRest.Payments(ctx, reqPayments, credential)
		if errResp != nil {
			err = errResp
			return
		}

		providerRes, _ = json.Marshal(respPayment)
		resp = generateResponseXfersCashin(respPayment, req)
		virtualAccount = respPayment.Data.PaymentDataResp.PaymentMethodResp.InstructionsResp.AccountNo

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) ottocashTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}

	credential, errRes := ottocash.GetOttocashCredential(req.Credential)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	switch true {
	case channelType.PaymentType == constants.TrxEwallet:
		reqXendit := ottocash.InquiryRequest{
			MerchantID:   req.CashinTransaction.CustomerPhone,
			MerchantName: req.CashinTransaction.CustomerName,
			PhoneNumber:  str.PhoneNormalization(req.CashinTransaction.CustomerPhone),
			TrxID:        referenceId,
			Amount:       totalAmount,
			MerchantURL:  "https://ottoapi.pactindo.com/ottocash-admin/ind/back",
			SuccessURL:   "https://ottoapi.pactindo.com/ottocash-admin/ind/success",
			FailedURL:    "https://ottoapi.pactindo.com/ottocash-admin/ind/failed",
		}

		respOttocash, errRes := s.ottocashRest.Inquiry(ctx, reqXendit, credential)
		if errRes != nil {
			err = errRes
			return
		}

		providerRes, _ = json.Marshal(respOttocash)
		resp = generateResponseOttocashEwalletCashin(respOttocash, req)

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	resp.ExpiredTime = req.ExpiredTime.Format(time.RFC3339)

	return
}

func (s *defaultCashinTransaction) nicepayTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}

	credential, errRes := nicepay.GetNicepayCredential(req.Credential)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	switch true {
	case channelType.PaymentType == constants.TrxVirtualAccount:
		loc, _ := time.LoadLocation("Asia/Jakarta") // Asia/Jakarta / UTC ??
		txTime := time.Now().In(loc)
		reqNicePay := nicepay.RegistrationRequest{
			TimeStamp:      txTime.Format(nicepay.DateTimeFormat),
			IMid:           credential.MerchantID,
			PayMethod:      nicepay.PayMethodVirtualAccount,
			Currency:       nicepay.CurrencyIDR,
			Amt:            fmt.Sprintf("%.0f", totalAmount),
			ReferenceNo:    referenceId,
			GoodsNm:        req.CashinTransaction.CustomerName,
			BillingNm:      req.CashinTransaction.CustomerName,
			BillingPhone:   req.CashinTransaction.CustomerPhone,
			BillingEmail:   req.CashinTransaction.CustomerEmail,
			BillingAddr:    "-",
			BillingCity:    "-",
			BillingState:   "-",
			BillingPostCd:  "-",
			BillingCountry: "-",
			CartData:       "{}",
			BankCd:         req.ProviderChannelCode,
			VacctValidDt:   req.ExpiredTime.Format(nicepay.DateFormat),
			VacctValidTm:   req.ExpiredTime.Format(nicepay.TimeFormat),
			MerchantToken:  nicepay.GetMerchantToken(txTime.Format(nicepay.DateTimeFormat), credential, referenceId, fmt.Sprintf("%.0f", totalAmount)),
		}

		respNicePay, errRes := s.nicePayRest.Registration(ctx, &reqNicePay, credential)
		if errRes != nil {
			err = errRes
			return
		}

		providerRes, _ = json.Marshal(respNicePay)
		resp = generateResponseNicepayCashin(respNicePay, req)
		virtualAccount = respNicePay.VacctNo

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) snapNicepayTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}

	var snapCred snapclient.NicepayCred
	errRes = snapclient.GetSnapCredentialCB(req.Credential, &snapCred)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	switch true {
	case channelType.PaymentType == constants.TrxVirtualAccount:
		createVaReq := snapclient.TransferVaCreateVaReq{
			PartnerServiceID:    "",
			CustomerNo:          "",
			VirtualAccountNo:    "",
			VirtualAccountName:  req.CashinTransaction.CustomerName,
			VirtualAccountEmail: req.CashinTransaction.CustomerEmail,
			VirtualAccountPhone: req.CashinTransaction.CustomerPhone,
			TrxID:               referenceId,
			TotalAmount:         snapclient.NewMoney(snapclient.CurrencyIDR, totalAmount),
			AdditionalInfo: snapclient.AdditionalInfo{
				Data: map[string]interface{}{
					"bankCd":       req.ProviderChannelCode,
					"goodsNm":      req.CashinTransaction.CustomerName,
					"dbProcessUrl": config.GetString("snap_nicepay.db_process_url"),
					"vacctValidDt": req.ExpiredTime.Format(nicepay.DateFormat),
					"vacctValidTm": req.ExpiredTime.Format(nicepay.TimeFormat),
					"msId":         "",
					"msFee":        "",
					"msFeeType":    "",
					"mbFee":        "",
					"mbFeeType":    "",
				},
			},
		}
		htr := snapclient.HTrxReq{
			XPartnerId: snapCred.ClientID,
			ChannelId:  snapCred.ChannelId,
		}
		snapRes, errRes := s.snapNicepay.TransferVaCreateVA(ctx, snapCred, htr, &createVaReq)
		if errRes != nil {
			err = errRes
			return
		}

		txIdVa, _ := snapRes.VirtualAccountData.AdditionalInfo.Get("tXidVA").(string)
		providerRes, _ = json.Marshal(snapclient.NicepayVaHistoryInfo{
			TotalAmount:      snapRes.VirtualAccountData.TotalAmount,
			HeaderExternalId: createVaReq.Raw.Header.Get(snapclient.HeaderExternalId),
			TrxID:            snapRes.VirtualAccountData.TrxID,
			TXidVA:           txIdVa,
		})
		resp = generateResponseSnapNicepayVACashin(snapRes, req)
		virtualAccount = snapRes.VirtualAccountData.VirtualAccountNo

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) dokuTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, err := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if err != nil {
		return
	}

	credential, err := doku.GetDokuCredential(req.Credential)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	expiredTime := req.ExpiredTime.Sub(req.ProcessAt)

	switch true {
	case channelType.PaymentType == constants.TrxVirtualAccount:
		var respDoku doku.VAPaymentCodeRes

		reqDoku := doku.VAPaymentCodeReq{
			Order: doku.VAPaymentCodeOrderReq{
				InvoiceNumber: referenceId,
				Amount:        totalAmount,
			},
			VirtualAccountInfo: doku.VAPaymentCodeInfoReq{
				BillingType:             doku.VA_BILLING_TYPE_FIX_BILL,
				MerchantUniqueReference: strconv.Itoa(req.CashinTransaction.ID),
				ExpiredTime:             int(expiredTime.Minutes()),
				ReusableStatus:          false,
				Info1:                   "",
				Info2:                   "",
				Info3:                   "",
			},
			Customer: doku.VAPaymentCodeCustomerReq{
				Name:  req.CashinTransaction.CustomerName,
				Email: req.CashinTransaction.CustomerEmail,
			},
		}

		providerRes, respDoku, err = s.dokuRest.VAPaymentCode(ctx, credential, req.PaymentChannel.Name, reqDoku)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "error doku va create payment code")
			return
		}

		resp = generateResponseDokuVACashin(respDoku, req)
		virtualAccount = respDoku.VirtualAccountInfo.VirtualAccountNumber

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

// nolint: unused
func (s *defaultCashinTransaction) cashlezTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, _ float64, _ string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}
	switch true {
	case channelType.PaymentType == constants.TrxOfflinePG: // no api integration
		addInfo := req.CashinTransaction.AdditionalInfo.ToCashlez() // cashlezz reference information
		resp = generateResponseCashlezCashin(addInfo, req)
		providerRes, _ = json.Marshal(addInfo.Cashlez)
	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) bankBcaTransactionProcess(ctx context.Context, req *entity.CashinTransactionProcess, totalAmount float64, referenceId string) (resp entity.CashinTranscationResponse, providerRes []byte, virtualAccount string, err error) {
	channelType, errRes := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(req.PaymentChannel.PaymentChannelTypeId))
	if errRes != nil {
		err = errRes
		return
	}

	switch true {
	case channelType.PaymentType == constants.TrxSNAP:
		addInfo := req.CashinTransaction.AdditionalInfo.ToVA()
		snapSetting := req.CompanyMapping.ChannelTypeConfigs.ToSNAP()
		var fvaCust domain.FixedVACustomer
		snapCompCode := snapSetting.CompCode
		virtualAccount = snapCompCode + req.CashinTransaction.CustomerPhone //  generate va
		cust := domain.FixedVACustomer{
			VANumber:          virtualAccount,
			Name:              req.CashinTransaction.CustomerName,
			Phone:             req.CashinTransaction.CustomerPhone,
			LastPgReferenceId: req.CashinTransaction.InvoiceNumber,
		}

		fvaCust, err = s.companyMappingService.GetOrCreateCustomerFVAByVaNumber(ctx, true, cust, req.CompanyMapping)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when create fixed va customer %v", err.Error()))
			logger.Error(ctx, err.Error())
			return
		}
		logger.Info(ctx, fmt.Sprintf("snap fvaCust %+v", fvaCust))

		// check existing cash in
		if len(fvaCust.LastPgReferenceId) > 0 && !req.IsOverwriteTransaction { // id already have VA, check payment status
			var cashIn *domain.CashInTransactions
			cashIn, err = s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, fvaCust.LastPgReferenceId)
			if err != nil && err != gorm.ErrRecordNotFound {
				err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when get cash in by va %v", err.Error()))
				logger.Error(ctx, err.Error())
				return
			}

			if addInfo.IsOverwriteTransaction {
				// failed current transaction
				cb := cashIn.ToSnapCb(domain.ToSnapCbOpts{
					CustomerNumber:      req.CashinTransaction.CustomerName,
					PaymentProviderName: constants.ProviderBankBCA,
					PartnerId:           snapCompCode,
				})
				err = s.UpdateStatusSnapCashinTransaction(ctx, cashIn, cb.ToStatus(constants.CashInStatusFailed), domain.UpdateWachingStatusCashinOpts{
					HistoryDescription: "overwrite transaction",
				})
				if err != nil {
					err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when failed current transaction for overwrite trx %v", err.Error()))
					logger.Error(ctx, err.Error())
					return
				}
				// create new transaction, with recursive function
				req.IsOverwriteTransaction = true
				return s.bankBcaTransactionProcess(ctx, req, totalAmount, referenceId)
			}

			if cashIn != nil && cashIn.PaymentStatus == constants.PaymentDraft {
				err = errors.SetErrorMessage(http.StatusConflict, fmt.Sprintf("there is unpaid cash in %s", cashIn.InvoiceNumber))
				logger.Error(ctx, err.Error())
				return
			}
		}

		// always update latest fixed va info
		err = s.companyMappingService.UpdateFixedVACustomer(ctx, req.Tx, &domain.FixedVACustomer{
			ID:                fvaCust.ID,
			Name:              req.CashinTransaction.CustomerName,
			LastPgReferenceId: req.CashinTransaction.InvoiceNumber,
			VANumber:          virtualAccount,
			ExpiredAt:         req.ExpiredTime,
		})
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when update fixed va customer %v", err.Error()))
			logger.Error(ctx, err.Error())
			return
		}

		resp = generateResponseBankBcaCashin(virtualAccount, req)
		provResp := req.CashinTransaction.ToSnapCb(domain.ToSnapCbOpts{
			CustomerNumber:      req.CashinTransaction.CustomerPhone,
			PaymentProviderName: req.ProviderName,
			PartnerId:           snapSetting.CompCode,
		})
		providerRes, _ = json.Marshal(provResp)
	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment type is not yet in provider service")
		logger.Error(ctx, err.Error())
		return
	}

	return
}
