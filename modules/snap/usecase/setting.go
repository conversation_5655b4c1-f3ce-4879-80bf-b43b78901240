package usecase

import (
	"github.com/go-redis/redis/v8"
	trxReqLog "repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
)

type SnapSecretJWT struct {
	Secret         string `mapstructure:"secret"`
	ExpiredSeconds int    `mapstructure:"expired_seconds"`
}

type defaultUC struct {
	repo             Repository
	snapSecretJWT    SnapSecretJWT
	cache            *redis.Client
	fnWrapper        UcFuncWrapper
	trxReqLogService trxReqLog.TrxRequestLogUseCase
}

func Setup() *defaultUC {
	return &defaultUC{}
}

func (s *defaultUC) SetRepo(t Repository) *defaultUC {
	s.repo = t
	return s
}

func (s *defaultUC) SetSnapSecretJWT(t SnapSecretJWT) *defaultUC {
	s.snapSecretJWT = t
	return s
}

func (s *defaultUC) SetRedisClient(t *redis.Client) *defaultUC {
	s.cache = t
	return s
}

func (s *defaultUC) SetFnWrapper(t UcFuncWrapper) *defaultUC {
	s.fnWrapper = t
	return s
}

func (s *defaultUC) FnWrapper() UcFuncWrapper {

	return s.fnWrapper
}

func (s *defaultUC) SetTrxReqLogService(trxReqLogService trxReqLog.TrxRequestLogUseCase) *defaultUC {
	s.trxReqLogService = trxReqLogService
	return s
}
