package constants

// payment Status
const (
	// Belum Dibayar
	PaymentDraft = "draft"
	// Telah dibayar dan menunggu settlement
	PaymentPaid = "paid"
	// Sedang di prosess
	PaymentProcessing = "processing"
	// Settlement Sukses
	PaymentSettled = "settled"
	// Telah di disburse
	PaymentFailed = "failed"
	// Expired
	PaymentExpired = "expired"
)

const (
	// Transaksi telah terbuat dan menunggu pembayaran
	StatusPending = "pending"
	// Sedang di proses di sisi seller
	StatusOnProgress = "on_progress"
	// Sedang dalam pengiriman
	StatusOnDelivery = "on_delivery"
	// Selesai
	// StatusDone          = "done"
	// StatusPaid          = "paid"
	// StatusFailed        = "failed"
	// StatusRefundRequest = "refund_request"
	// StatusRefunded      = "refunded"
)

// xfers Status payment
const (
	XfersPending    = "pending"
	XfersProcessing = "processing"
	XfersPaid       = "paid"
	XfersCompleted  = "completed"
	XfersCanceled   = "cancelled"
	XfersExpired    = "expired"
	XfersFailed     = "failed"
)

// Xendit status payment
const (
	XenditPending  = "PENDING"
	XenditFailed   = "FAILED"
	XenditSucess   = "SUCCEEDED"
	XenditVoided   = "VOIDED"
	XenditRefunded = "REFUNDED"

	XenditInvoiceStatusPaid    = "PAID"
	XenditInvoiceStatusSettled = "SETTLED"
	XenditInvoiceStatusExpired = "EXPIRED"

	XenditVAStatusInactive = "INACTIVE"
	XenditVAStatusPending  = "PENDING"
	XenditVAStatusActive   = "ACTIVE"

	XenditPaymentFixedCodeStatusActive   = "ACTIVE"
	XenditPaymentFixedCodeStatusInactive = "INACTIVE"
	XenditPaymentFixedCodeStatusExpired  = "EXPIRED"
)

// Ottocash
const (
	OttocashStatusCodeSuccess = "200"
	OttocashStatusCodePending = "408"
)
