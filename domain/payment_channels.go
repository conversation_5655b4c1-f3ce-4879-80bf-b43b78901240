package domain

import (
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/utils"
)

type PaymentChannels struct {
	ID                   int            `gorm:"primaryKey" json:"id"`
	Name                 string         `json:"name"`
	Status               bool           `json:"status"`
	PaymentChannelTypeId int            `json:"payment_channel_type_id"`
	LogoID               int            `json:"logo_id"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type GetPaymentChannelsParams struct {
	Pagination  utils.Pagination
	CompanyID   int  `query:"page"`
	IsAvailable bool `query:"is_available"` // used for showing unused channel mapping for current company
}
