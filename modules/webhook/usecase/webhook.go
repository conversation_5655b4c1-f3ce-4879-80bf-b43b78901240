package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
	"repo.nusatek.id/moaja/backend/libraries/logger"

	"repo.nusatek.id/nusatek/payment/domain"
	wbs "repo.nusatek.id/nusatek/payment/infrastructure/webhook"
	"repo.nusatek.id/nusatek/payment/modules/webhook/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (uc *defaultWebhook) GetWebhookDeliveryLogs(ctx context.Context, req *entity.WebhookLogPaginate) ([]entity.WebhookDeliveryRespose, int64, error) {
	rows, total, err := uc.webhookRepo.GetWebhookDeliveryLogs(ctx, req)
	if err != nil {
		return nil, 0, err
	}

	var response []entity.WebhookDeliveryRespose
	for _, row := range rows {
		response = append(response, entity.WebhookDeliveryRespose{
			ID:            int(row.Id),
			CompanyId:     row.Company.ID,
			CompanyName:   row.Company.Name,
			EventType:     row.EventType,
			InvoiceNumber: row.InvoiceNumber,
			Endpoint:      row.Endpoint,
			Status:        string(row.Status),
			TotalRetries:  row.Attempt,
			ResponseCode:  *row.ResponseCode,
			ErrorMessage:  row.ErrorMessage,
			CreatedAt:     row.CreatedAt.Format(time.RFC3339),
			UpdatedAt:     row.UpdatedAt.Format(time.RFC3339),
		})
	}

	return response, total, nil
}

func (uc *defaultWebhook) GetWebhookDeliveryLogById(ctx context.Context, id int64) (*entity.WebhookDeliveryRespose, error) {
	row, err := uc.webhookRepo.GetWebhookDeliveryLogById(ctx, id)
	if err != nil {
		return nil, err
	}

	return &entity.WebhookDeliveryRespose{
		ID:            int(row.Id),
		CompanyId:     row.Company.ID,
		CompanyName:   row.Company.Name,
		EventType:     row.EventType,
		InvoiceNumber: row.InvoiceNumber,
		Endpoint:      row.Endpoint,
		Status:        string(row.Status),
		TotalRetries:  row.Attempt,
		Payload:       row.Payload,
		ResponseCode:  *row.ResponseCode,
		ResponseBody:  row.ResponseBody,
		ErrorMessage:  row.ErrorMessage,
		CreatedAt:     row.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     row.UpdatedAt.Format(time.RFC3339),
	}, nil
}

func (uc *defaultWebhook) GetWebhookDeliveryLogHistory(ctx context.Context, id int64, req *entity.WebhookDeliveryLogHistoryPaginate) ([]*entity.WebhookDeliveryRespose, int64, error) {
	rows, total, err := uc.webhookRepo.GetWebhookDeliveryLogHistory(ctx, id, req)
	if err != nil {
		return nil, 0, err
	}

	var webhookDeliveryLogs []*entity.WebhookDeliveryRespose
	for _, row := range rows {
		webhookDeliveryLogs = append(webhookDeliveryLogs, &entity.WebhookDeliveryRespose{
			ID:           int(row.Id),
			CompanyId:    int(row.CompanyId),
			CompanyName:  row.Company.Name,
			EventType:    row.EventType,
			Endpoint:     row.Endpoint,
			Status:       string(row.Status),
			TotalRetries: row.Attempt,
			Payload:      row.Payload,
			ResponseCode: *row.ResponseCode,
			ResponseBody: row.ResponseBody,
			ErrorMessage: row.ErrorMessage,
			CreatedAt:    row.CreatedAt.Format(time.RFC3339),
			UpdatedAt:    row.UpdatedAt.Format(time.RFC3339),
		})
	}
	return webhookDeliveryLogs, total, nil
}

func (uc *defaultWebhook) VerifyEndpoint(ctx context.Context, req *entity.WebhookRequest) (*entity.WebhookVerifyResponse, error) {
	company, err := uc.companyRepo.GetCompanyById(ctx, strconv.Itoa(int(req.CompanyId)))
	if err != nil {
		return nil, err
	}

	var bodyRequest = entity.BuildWebhookPayload(req.EventType, req.URL)
	timestamp := time.Now().Unix()

	generator := wbs.NewSignatureVerifier(wbs.DefaultSignatureConfig())
	generator.Secret(company.Secrets)

	signature, digest := generator.GenerateSignature(bodyRequest.ToPayload(), timestamp, "application/json")
	logger.Infof(ctx, "Genererate Digest : %s | From Body : %s", digest, bodyRequest.ToPayload())
	headers := generator.BuildHeaders(timestamp, signature, digest)

	response, err := uc.webhookSvc.DeliverWebhook(ctx, req.URL, bodyRequest.ToPayload(), wbs.WithSimpleHeaders(headers))
	if err != nil {
		return nil, err
	}

	cashInPayload := bodyRequest
	cashInPayload.Headers = entity.Header{
		ContentType:   "application/json",
		Authorization: fmt.Sprintf("Basic %s", company.Secrets),
		Signature:     headers["Signature"],
		Timestamp:     headers["Timestamp"],
		RequestId:     headers["X-Request-ID"],
	}

	body := make(map[string]interface{})
	if response.IsJSON() {
		if err := json.Unmarshal(response.Body, &body); err != nil {
			return nil, err
		}
	} else {
		r, err := response.ResponseToJSON()
		if err != nil {
			return nil, err
		}

		body = map[string]interface{}{
			"message": string(r),
		}
	}

	cashInPayload.Response = entity.Response{
		ContentType: response.Headers.Get("Content-Type"),
		StatusCode:  response.StatusCode,
		Time:        float64(response.Duration.Milliseconds()),
		Payload:     body,
	}

	verifyResponse := entity.WebhookVerifyResponse{
		VerifyPayload: *cashInPayload,
	}

	if response.StatusCode == http.StatusOK {
		webhook := &domain.CompanyWebhook{
			EventType: string(req.EventType),
			URL:       req.URL,
		}
		if err := uc.companyRepo.UpdateOrCreateWebhook(ctx, company.ID, webhook); err != nil {
			return nil, err
		}
	}

	return &verifyResponse, nil
}

func (uc *defaultWebhook) CashInWebhook(ctx context.Context, cashInCallback domain.CashinTransactionCallBack, payload []byte) error {
	company, err := uc.companyRepo.GetCompanyByCode(ctx, cashInCallback.CompanyCode)
	if err != nil {
		logger.Error(ctx, "failed to get company")
		return err
	}

	if company.Webhooks == nil {
		logger.Error(ctx, "company webhooks is nil")
		return fmt.Errorf("company with code %s does not have webhooks", cashInCallback.CompanyCode)
	}

	urls, err := entity.GetWebhookURLs(company.Webhooks)
	if err != nil {
		logger.Error(ctx, "failed to get webhook urls")
		return err
	}

	if urls.CashInStatus == nil {
		logger.Error(ctx, "cash in status webhook url is empty")
		return fmt.Errorf("company with code %s does not have cash in status webhook url", cashInCallback.CompanyCode)
	}

	if err = uc.Send(ctx, company, cashInCallback.InvoiceNumber, string(entity.CashInStatusWebhook), urls.CashInStatus.URL, payload); err != nil {
		logger.Error(ctx, "failed to send webhook")
		return err
	}

	return nil
}

func (uc *defaultWebhook) CashOutWebhook(ctx context.Context, cashOutCallback domain.CashOutCallback, payload []byte) error {
	company, err := uc.companyRepo.GetCompanyByCode(ctx, cashOutCallback.CompanyCode)
	if err != nil {
		logger.Error(ctx, "failed to get company")
		return err
	}

	if company.Webhooks == nil {
		logger.Error(ctx, "company webhooks is nil")
		return fmt.Errorf("company with code %s does not have webhooks", cashOutCallback.CompanyCode)
	}

	urls, err := entity.GetWebhookURLs(company.Webhooks)
	if err != nil {
		logger.Error(ctx, "failed to get webhook urls")
		return err
	}

	if urls.CashOutStatus == nil {
		logger.Error(ctx, "cash out status webhook is nil")
		return fmt.Errorf("company with code %s does not have cash out status webhook", cashOutCallback.CompanyCode)
	}

	if err = uc.Send(ctx, company, cashOutCallback.BatchNumber, string(entity.CashOutStatusWebhook), urls.CashOutStatus.URL, payload); err != nil {
		logger.Error(ctx, "failed to send webhook")
		return err
	}

	return nil
}

func (uc *defaultWebhook) Send(ctx context.Context, company *domain.Companies, InvoiceNumber, eventType, url string, payload []byte) error {
	endpoint := wbs.WebhookEndpoint{
		ID:       uuid.NewString(),
		URL:      url,
		ClientID: strconv.Itoa(company.ID),
		Authentication: basicAuth{
			Token: company.Secrets,
		},
		SignatureConfig: wbs.SignatureConfig{
			Secret: company.Secrets,
		},
	}

	if err := uc.webhookSvc.RegisterEndpoint(&endpoint); err != nil {
		logger.Error(ctx, "failed to register webhook endpoint")
		return err
	}

	deliveryLog, err := uc.webhookRepo.GetParentWebhookDeliveryLogs(ctx, &entity.WebhookDeliveryLog{
		CompanyId:     int64(company.ID),
		Endpoint:      url,
		EventType:     eventType,
		InvoiceNumber: InvoiceNumber,
	})
	if err != nil {
		logger.Error(ctx, "failed to get parent webhook delivery logs", logger.Err(err))
	}

	deliveryLog.Id = 0
	deliveryLog.CompanyId = int64(company.ID)
	deliveryLog.Endpoint = url
	deliveryLog.Payload = string(payload)
	deliveryLog.EventType = eventType
	deliveryLog.InvoiceNumber = InvoiceNumber

	resp, err := uc.webhookSvc.DeliverWebhook(ctx, endpoint.ID, payload)

	// Handle the response and error
	if err != nil || resp.StatusCode != http.StatusOK {
		logger.Error(ctx, "failed to deliver webhook", logger.Any("response", resp))

		deliveryLog.Status = entity.WebhookStatusFailed
		if err != nil {
			deliveryLog.ErrorMessage = err.Error()
		} else {
			deliveryLog.ErrorMessage = resp.StatusCodeMessage()
		}
		deliveryLog.ResponseCode = &resp.StatusCode
		deliveryLog.Attempt = deliveryLog.Attempt + 1
		deliveryLog.ResponseBody = string(resp.Body)

		if err := uc.saveDeliveryLog(ctx, deliveryLog); err != nil {
			logger.Error(ctx, "failed to save delivery log")
		}

		// Save the failed webhook to Redis for retry
		if err := uc.saveFailedWebhookToStorage(ctx, &endpoint, deliveryLog); err != nil {
			logger.Error(ctx, "failed to save failed webhook to Redis")
		}

		return err
	}

	logger.Info(ctx, "webhook delivered successfully", logger.Any("response", resp))
	deliveryLog.Status = entity.WebhookStatusDelivered
	deliveryLog.ResponseCode = &resp.StatusCode
	deliveryLog.ResponseBody = string(resp.Body)

	if err := uc.saveDeliveryLog(ctx, deliveryLog); err != nil {
		logger.Error(ctx, "failed to save delivery log")
	}

	return nil
}

func (uc *defaultWebhook) saveDeliveryLog(ctx context.Context, log *entity.WebhookDeliveryLog) error {
	return uc.webhookRepo.CreateWebhookDeliveryLog(ctx, log)
}

type retryMeta struct {
	InvoiceNumber string `json:"invoiceNumber"`
	EventType     string `json:"eventType"`
	CompanyId     int64  `json:"companyId"`
	Url           string `json:"url"`
	Attempt       int    `json:"attempt"`
}

// saveFailedWebhookToRedis saves the failed webhook to Redis for retry.
func (uc *defaultWebhook) saveFailedWebhookToStorage(ctx context.Context, webhook *wbs.WebhookEndpoint, deliveryLog *entity.WebhookDeliveryLog) error {
	if deliveryLog.Attempt >= constants.MAX_RETRIES {
		logger.Info(ctx, "webhook retry limit exceeded", logger.Any("webhook", webhook))
		return nil
	}

	backoff := constants.WebhookBackoff[deliveryLog.Attempt]

	// Serialize the webhook and payload
	data := map[string]interface{}{
		"meta": retryMeta{
			InvoiceNumber: deliveryLog.InvoiceNumber,
			EventType:     deliveryLog.EventType,
			CompanyId:     deliveryLog.CompanyId,
			Url:           deliveryLog.Endpoint,
			Attempt:       deliveryLog.Attempt,
		},
		"payload": string(deliveryLog.Payload),
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal webhook data: %w", err)
	}

	// Save to Redis with a TTL
	key := fmt.Sprintf("failed_webhook:%s", webhook.ID)
	if err := uc.retryService.SaveDataWithTTL(key, jsonData, backoff); err != nil {
		return fmt.Errorf("failed to save webhook to Redis: %w", err)
	}

	// Register the key handler for retry
	uc.retryService.RegisterKeyHandler(key, func(key string) {
		uc.retryFailedWebhook(ctx, key)
	})

	return nil
}

func (uc *defaultWebhook) retryFailedWebhook(ctx context.Context, key string) {
	// Fetch the data from Redis
	client := uc.retryService.GetClient()
	data, err := client.Get(ctx, key+":value").Result()
	if err != nil {
		logger.Error(ctx, "failed to fetch webhook data from Redis",
			logger.String("key", key),
		)
		return
	}

	// Deserialize the data
	var storedData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &storedData); err != nil {
		logger.Error(ctx, "failed to unmarshal expired webhook data",
			logger.String("key", key),
		)
		return
	}

	// Extract the webhook and payload
	meta, ok := storedData["meta"].(map[string]interface{})
	if !ok {
		logger.Error(ctx, "invalid meta data format",
			logger.String("key", key),
		)
		return
	}

	payload, ok := storedData["payload"].(string)
	if !ok {
		logger.Error(ctx, "invalid payload format",
			logger.String("key", key),
		)
		return
	}

	metadata := retryMeta{}
	if err := mapstructure.Decode(meta, &metadata); err != nil {
		logger.Error(ctx, "failed to unmarshal webhook data",
			logger.String("key", key),
		)
		return
	}

	company, err := uc.companyRepo.GetCompanyById(ctx, strconv.Itoa(int(metadata.CompanyId)))
	if err != nil {
		logger.Error(ctx, "failed to get company",
			logger.String("key", key),
			logger.Any("meta", metadata),
		)
		return
	}

	if err := uc.Send(ctx, company, metadata.InvoiceNumber, metadata.EventType, metadata.Url, []byte(payload)); err != nil {
		logger.Error(ctx, "failed to send webhook",
			logger.String("key", key),
			logger.Any("meta", metadata),
		)
		return
	}
}
