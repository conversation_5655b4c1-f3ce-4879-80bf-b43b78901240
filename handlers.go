package ottopay

import (
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/fiber/v2/middleware/timeout"
)

// SetupRoutes configures the Fiber app with OttoPay routes
func SetupRoutes(app *fiber.App, service OttoPayService, config Config) {
	// Add middleware
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New())

	// Add timeout middleware
	app.Use(timeout.New(func(c *fiber.Ctx) error {
		return c.Next()
	}, config.RequestTimeout))

	// Add authentication middleware
	app.Use(service.AuthMiddleware())

	// Health check endpoint (no auth required)
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"service":   "ottopay",
		})
	})

	// OttoPay API routes
	api := app.Group("/ottopay")

	// Token endpoint (no auth required - handled by middleware)
	api.Post("/token", service.GetTokenHandler)

	// Inquiry endpoint (requires auth)
	api.Post("/inquiry", service.InquiryHandler)

	// Payment endpoint (requires auth)
	api.Post("/payment", service.PaymentHandler)
}

// CreateFiberApp creates a new Fiber application with OttoPay configuration
func CreateFiberApp(service OttoPayService, config Config) *fiber.App {
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}

			return c.Status(code).JSON(NewErrorResponse(code, err.Error()))
		},
		ReadTimeout:  config.RequestTimeout,
		WriteTimeout: config.RequestTimeout,
	})

	SetupRoutes(app, service, config)
	return app
}

// StartServer starts the OttoPay server
func StartServer(service OttoPayService, config Config) error {
	app := CreateFiberApp(service, config)
	return app.Listen(":" + config.Port)
}

// HandlerUtils provides utility functions for handlers
type HandlerUtils struct{}

// NewHandlerUtils creates a new HandlerUtils instance
func NewHandlerUtils() *HandlerUtils {
	return &HandlerUtils{}
}

// ExtractUserFromContext extracts user information from Fiber context
func (h *HandlerUtils) ExtractUserFromContext(c *fiber.Ctx) (userID, username string) {
	if uid := c.Locals("user_id"); uid != nil {
		if id, ok := uid.(string); ok {
			userID = id
		}
	}
	if uname := c.Locals("username"); uname != nil {
		if name, ok := uname.(string); ok {
			username = name
		}
	}
	return
}

// ValidateContentType validates that the request has the correct content type
func (h *HandlerUtils) ValidateContentType(c *fiber.Ctx) error {
	contentType := c.Get("Content-Type")
	if contentType != "application/json" && contentType != "" {
		return c.Status(fiber.StatusUnsupportedMediaType).JSON(
			NewErrorResponse(415, "Content-Type must be application/json"))
	}
	return nil
}

// LogRequest logs the incoming request for audit purposes
func (h *HandlerUtils) LogRequest(c *fiber.Ctx, endpoint string) {
	userID, username := h.ExtractUserFromContext(c)

	// Log request details (implement your logging logic here)
	_ = userID
	_ = username
	_ = endpoint
	// Example: log.Printf("Request to %s from user %s (%s) - IP: %s", endpoint, username, userID, c.IP())
}

// ResponseBuilder helps build consistent API responses
type ResponseBuilder struct{}

// NewResponseBuilder creates a new ResponseBuilder instance
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{}
}

// BuildInquiryResponse builds an inquiry response with proper formatting
func (r *ResponseBuilder) BuildInquiryResponse(req InquiryRequest, data InquiryData, isSuccess bool) InquiryResponse {
	if isSuccess {
		return InquiryResponse{
			CompanyCode:    req.CompanyCode,
			CustomerNumber: req.CustomerNumber,
			RequestID:      req.RequestID,
			InquiryStatus:  InquiryStatusSuccess,
			InquiryReason:  InquiryReasons[InquiryStatusSuccess],
			CustomerName:   data.CustomerName,
			CurrencyCode:   data.CurrencyCode,
			TotalAmount:    formatAmount(data.TotalAmount),
			SubCompany:     getSubCompanyOrDefault(data.SubCompany),
			DetailBills:    nil,
			AdditionalData: data.AdditionalData,
		}
	}

	reason := InquiryReasons[InquiryStatusFailed]
	if data.ErrorMessage != "" {
		reason.Indonesian = data.ErrorMessage
		reason.English = data.ErrorMessage
	}

	return InquiryResponse{
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		RequestID:      req.RequestID,
		InquiryStatus:  InquiryStatusFailed,
		InquiryReason:  reason,
		CustomerName:   "",
		CurrencyCode:   CurrencyIDR,
		TotalAmount:    "0.00",
		SubCompany:     DefaultSubCompany,
		DetailBills:    nil,
		AdditionalData: "",
	}
}

// BuildPaymentResponse builds a payment response with proper formatting
func (r *ResponseBuilder) BuildPaymentResponse(req PaymentRequest, data PaymentData) PaymentResponse {
	var status string
	var reason PaymentFlagReason

	if data.IsTimeout {
		status = PaymentStatusTimeout
		reason = PaymentReasons[PaymentStatusTimeout]
	} else if data.IsSuccess {
		status = PaymentStatusSuccess
		reason = PaymentReasons[PaymentStatusSuccess]
	} else {
		status = PaymentStatusFailed
		reason = PaymentReasons[PaymentStatusFailed]
		if data.ErrorMessage != "" {
			reason.Indonesian = data.ErrorMessage
			reason.English = data.ErrorMessage
		}
	}

	return PaymentResponse{
		CompanyCode:       req.CompanyCode,
		CustomerNumber:    req.CustomerNumber,
		RequestID:         req.RequestID,
		PaymentFlagStatus: status,
		PaymentFlagReason: reason,
		CustomerName:      data.CustomerName,
		CurrencyCode:      data.CurrencyCode,
		PaidAmount:        formatAmount(data.PaidAmount),
		TotalAmount:       formatAmount(data.TotalAmount),
		TransactionDate:   req.TransactionDate,
		DetailBills:       nil,
		FreeTexts:         nil,
		AdditionalData:    data.AdditionalData,
	}
}

// Helper function to format amount with 2 decimal places
func formatAmount(amount float64) string {
	return fmt.Sprintf("%.2f", amount)
}

// RequestValidator provides request validation utilities
type RequestValidator struct{}

// NewRequestValidator creates a new RequestValidator instance
func NewRequestValidator() *RequestValidator {
	return &RequestValidator{}
}

// ValidateInquiryRequest validates an inquiry request
func (v *RequestValidator) ValidateInquiryRequest(req InquiryRequest) error {
	if req.CompanyCode == "" {
		return fmt.Errorf("CompanyCode is required")
	}
	if len(req.CompanyCode) > 5 {
		return fmt.Errorf("CompanyCode must be max 5 characters")
	}
	if req.CustomerNumber == "" {
		return fmt.Errorf("CustomerNumber is required")
	}
	if len(req.CustomerNumber) > 11 {
		return fmt.Errorf("CustomerNumber must be max 11 characters")
	}
	if req.RequestID == "" {
		return fmt.Errorf("RequestID is required")
	}
	if len(req.RequestID) > 255 {
		return fmt.Errorf("RequestID must be max 255 characters")
	}
	if req.ChannelType == "" {
		return fmt.Errorf("ChannelType is required")
	}
	if len(req.ChannelType) > 50 {
		return fmt.Errorf("ChannelType must be max 50 characters")
	}
	return nil
}

// ValidatePaymentRequest validates a payment request
func (v *RequestValidator) ValidatePaymentRequest(req PaymentRequest) error {
	if err := v.ValidateInquiryRequest(InquiryRequest{
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		RequestID:      req.RequestID,
		ChannelType:    req.ChannelType,
	}); err != nil {
		return err
	}

	if req.CustomerName == "" {
		return fmt.Errorf("CustomerName is required")
	}
	if len(req.CustomerName) > 30 {
		return fmt.Errorf("CustomerName must be max 30 characters")
	}
	if req.CurrencyCode == "" {
		return fmt.Errorf("CurrencyCode is required")
	}
	if len(req.CurrencyCode) != 3 {
		return fmt.Errorf("CurrencyCode must be exactly 3 characters")
	}
	if req.PaidAmount == "" {
		return fmt.Errorf("PaidAmount is required")
	}
	if req.TotalAmount == "" {
		return fmt.Errorf("TotalAmount is required")
	}
	if req.Reference == "" {
		return fmt.Errorf("Reference is required")
	}
	if len(req.Reference) > 255 {
		return fmt.Errorf("Reference must be max 255 characters")
	}
	return nil
}
