package email

import (
	"context"
	"regexp"

	"github.com/mailgun/mailgun-go/v4"
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email/webhook"
)

type EmailProvider interface {
	Send(ctx context.Context, from, subject, text string, to []string, params ...MailgunParameters) (*Response, error)
	Webhook(ctx context.Context, payload mailgun.WebhookPayload) (*webhook.WebhookResponse, error)
	IsValid(email string) bool
}

type Response struct {
	ID      string `json:"id"`
	Message string `json:"message"`
}

func (r *Response) ToID() string {
	if r.ID == "" {
		return ""
	}

	re := regexp.MustCompile(`<([^>]+)>`)
	match := re.FindStringSubmatch(r.ID)
	if len(match) > 1 {
		return match[1]
	}

	return ""
}
