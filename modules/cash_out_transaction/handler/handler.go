package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/streadway/amqp"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/entity"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/model"
	"repo.nusatek.id/nusatek/payment/utils/response"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

type CashOutHandler struct {
	cashOutUseCase usecase.CashOutUseCase
}

func NewHandler(cashoutUsecase usecase.CashOutUseCase) *CashOutHandler {
	return &CashOutHandler{cashoutUsecase}
}

// List CashOut
func (ch *CashOutHandler) List(c *fiber.Ctx) error {
	type request struct {
		Page             int    `query:"page" validate:"required,min=1"`
		ItemPerPage      int    `query:"limit" validate:"required,min=1"`
		Key              string `query:"key"`
		Value            string `query:"value"`
		FromDate         string `query:"from_date"`
		ToDate           string `query:"to_date"`
		DateType         string `query:"date_type"`
		Status           string `query:"status"`
		PartnerId        string `query:"partner_id"`
		FeePaymentStatus string `query:"fee_payment_status"`
	}

	r := new(request)
	if err := c.QueryParser(r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(r); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	param := map[string]interface{}{}
	if r.Value != "" {
		param["value"] = r.Value
	}

	if r.Key != "" {
		param["key"] = r.Key
	}

	if r.Status != "" {
		param["status"] = r.Status
	}

	if r.PartnerId != "" {
		param["partner_id"] = r.PartnerId
	}

	if r.FeePaymentStatus != "" {
		param["fee_payment_status"] = r.FeePaymentStatus
	}

	if r.FromDate != "" || r.ToDate != "" {
		param["date_type"] = r.DateType
		param["from"] = r.FromDate
		param["to"] = r.ToDate + " 23:59:59.000"
	}

	orderParam := map[string]interface{}{
		"start": r.Page,
		"limit": r.ItemPerPage,
	}

	list, totalItem, err := ch.cashOutUseCase.List(c.UserContext(), param, orderParam)
	if err != nil {
		return err
	}

	datas := []entity.CashoutDetailRes{}
	for _, v := range list {
		datas = append(datas, entity.FromDomainCashoutDetail(v))
	}

	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, datas)
}

func (ch *CashOutHandler) ListAPI(c *fiber.Ctx) error {
	auth := c.Request().Header.Peek("Authorization")

	type request struct {
		Page             int    `query:"page" validate:"required,min=1"`
		ItemPerPage      int    `query:"limit" validate:"required,min=1"`
		FromDate         string `query:"from"`
		ToDate           string `query:"to"`
		Status           string `query:"status"`
		PartnerId        string `query:"partner_id"`
		FeePaymentStatus string `query:"fee_payment_status"`
	}

	r := new(request)
	if err := c.QueryParser(r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(r); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	param := map[string]interface{}{}
	if r.Status != "" {
		param["status"] = r.Status
	}

	if r.PartnerId != "" {
		param["partner_id"] = r.PartnerId
	}

	if r.FromDate != "" || r.ToDate != "" {
		var pag utils.Pagination
		pag.FromDate = r.FromDate
		pag.ToDate = r.ToDate
		if err := utils.ValidateStruct(pag); err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return errors.ErrorHandle(c, err)
		}

		param["from"] = r.FromDate
		param["to"] = r.ToDate + " 23:59:59.000"
	}

	orderParam := map[string]interface{}{
		"start": r.Page,
		"limit": r.ItemPerPage,
	}

	list, totalItem, err := ch.cashOutUseCase.ListAPI(c.UserContext(), param, orderParam, string(auth))
	if err != nil {
		return err
	}
	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

func (ch *CashOutHandler) GetDetailByID(c *fiber.Ctx) error {
	id, _ := c.ParamsInt("id")

	data, err := ch.cashOutUseCase.GetDetailByID(c.UserContext(), id)
	if err != nil {
		return err
	}
	return response.HandleSuccess(c, entity.FromDomainCashoutDetail(data))
}

// RequestDisbursement ...
func (ch *CashOutHandler) RequestDisbursement(c *fiber.Ctx) (err error) {
	id, _ := c.ParamsInt("id")
	var request entity.UpdateRequest
	if err := c.BodyParser(&request); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(request); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	user := c.Locals("token").(model.JwtToken)
	err = ch.cashOutUseCase.RequestDisbursement(c.UserContext(), request, id, user.UserId)
	if err != nil {
		return err
	}

	return response.HandleSuccess(c, request)
}

// Scheduler request disbursement
func (ch *CashOutHandler) DisbursementProcess() {
	ctx := context.TODO()
	go ch.cashOutUseCase.DisbursementProcess(ctx)
}

func (h *CashOutHandler) GenerateQRGoogleAuthorization(c *fiber.Ctx) (err error) {
	user := c.Locals("token").(model.JwtToken)
	resp, err := h.cashOutUseCase.GenerateQRGoogleAuthorization(c.UserContext(), user.UserId)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *CashOutHandler) ApproveCashOutTransactionStatus(c *fiber.Ctx) (err error) {
	var req entity.OTP

	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	user := c.Locals("token").(model.JwtToken)
	err = h.cashOutUseCase.BulkApproveCashOutTransactionStatus(c.UserContext(), req, user.UserId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, nil)
}

func (h *CashOutHandler) RejectedCashOutTransactionStatus(c *fiber.Ctx) (err error) {
	var req entity.OTP
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	user := c.Locals("token").(model.JwtToken)
	resp, err := h.cashOutUseCase.UpdateCashOutTransactionStatus(c.UserContext(), req, user.UserId, constants.CashOutStatusRejected)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashOutHandler) UpdateCashOutTransactionStatusFeePaymentStatus(c *fiber.Ctx) (err error) {
	var req entity.FeePaymentStatusRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	user := c.Locals("token").(model.JwtToken)
	err = h.cashOutUseCase.UpdateCashOutTransactionStatusFeePaymentStatus(c.UserContext(), req, user.UserId)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

// ListHistory CashOut
func (ch *CashOutHandler) ListHistory(c *fiber.Ctx) error {
	type request struct {
		Page                 int `query:"page" validate:"required,min=1"`
		ItemPerPage          int `query:"limit" validate:"required,min=1"`
		CashOutTransactionID int `query:"id" validate:"required"`
	}
	r := new(request)
	if err := c.QueryParser(r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(r); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	param := map[string]interface{}{
		"cash_out_transaction_id": r.CashOutTransactionID,
	}

	orderParam := map[string]interface{}{
		"start": (r.Page - 1) * r.ItemPerPage,
		"limit": r.ItemPerPage,
	}

	list, totalItem, _ := ch.cashOutUseCase.ListHistory(c.UserContext(), param, orderParam)
	paginate := utils.Pagination{
		Limit: r.ItemPerPage,
		Page:  r.Page,
	}
	return response.HandleSuccessWithPagination(c, float64(totalItem), paginate, list)
}

func (h *CashOutHandler) CallbackXenditDisburse(c *fiber.Ctx) (err error) {
	var req interface{}
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	signature := c.Request().Header.Peek("x-callback-token")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	err = h.cashOutUseCase.PushCallbackProviderCashOutTranscation(c.UserContext(), string(signature), req, constants.ProviderXendit)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *CashOutHandler) ListenCallbackXfers(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenCallbackXfers", logger.String("body", string(c.Body)))

	var req xfers.DisbursementResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, "Unmarshal error", logger.Err(err))
		return
	}
	reqByte, _ := json.Marshal(req)

	go h.cashOutUseCase.CallbackCashOutTransactionProces(ctx, req.Data.Attributes.ReferenceId, req.Data.ID, constants.ProviderXfers, reqByte)

	return
}

func (h *CashOutHandler) ListenCallbackXendit(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenCallbackXendit", logger.String("body", string(c.Body)))

	var xenditReq xendit.DisbursementResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &xenditReq)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "[Callback Process]"+err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	reqByte, _ := json.Marshal(xenditReq)
	go h.cashOutUseCase.CallbackCashOutTransactionProces(ctx, xenditReq.ExternalID, xenditReq.ID, constants.ProviderXendit, reqByte)

	return
}

func (h *CashOutHandler) CheckStatusCashOutAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	id := c.Params("id")

	resp, err := h.cashOutUseCase.CheckStatusCashOutAPI(c.Context(), id, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}

func (h *CashOutHandler) GetListItemCashInBatchNumber(c *fiber.Ctx) (err error) {
	id, err := c.ParamsInt("cashOutId")
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, "invalid cashout id")
		return
	}

	dateType := c.Query("date_type")

	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return
	}
	paginate.SetMaxLimit(100)

	req := domain.GetCashoutItemParam{
		CashoutId: id,
		Paginate:  paginate,
		DateType:  dateType,
		WithTotal: true,
	}

	req.FromDate, _ = timepkg.StringDateToTime(c.Query("from_date"))
	req.ToDate, _ = timepkg.StringDateToTime(c.Query("to_date"))
	resp, total, err := h.cashOutUseCase.GetListItemCashInBatchNumber(c.UserContext(), req)
	if err != nil {
		return
	}

	return response.HandleSuccessWithPagination(c, float64(total), paginate, resp)
}

func (h *CashOutHandler) GetAllOutstandingItemCashIn(c *fiber.Ctx) (err error) {
	partnerId, err := strconv.Atoi(c.Query("partner_id"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid_partner_id")
		return errors.ErrorHandle(c, err)
	}
	productId, err := strconv.Atoi(c.Query("company_product_id"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid_company_product_id")
		return errors.ErrorHandle(c, err)
	}

	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return
	}
	paginate.SetMaxLimit(100)
	req := domain.CashInTransactionItemsOutstandingParam{
		PartnerID:        partnerId,
		CompanyProductId: productId,
		Search:           c.Query("search"),
		Paginate:         paginate,
		WithTotal:        true,
	}
	req.SettlementDateStart, _ = timepkg.StringDateToTime(c.Query("settlement_date_start"))
	req.SettlementDateEnd, _ = timepkg.StringDateToTime(c.Query("settlement_date_end"))

	res, total, err := h.cashOutUseCase.GetAllOutstandingItemCashIn(c.UserContext(), req)
	if err != nil {
		return
	}

	resp := []entity.CashInTransactionItemsOutstandingRes{}
	for _, v := range res {
		d := entity.CashInTransactionItemsOutstandingRes{
			InvoiceNumber:       v.InvoiceNumber,
			PaymentChannelName:  v.PaymentChannelName,
			PaymentProviderName: v.PaymentProviderName,
			ID:                  v.ID,
			CashInTransactionID: v.CashInTransactionID,
			CustomerName:        v.CustomerName,
			TotalAmount:         v.TotalAmount,
			PartnerId:           v.PartnerId,
			PartnerName:         v.PartnerName,
			ItemName:            v.ItemName,
			Amount:              v.Amount,
			Status:              v.Status,
			IsCashout:           v.IsCashout,
			SlaDate:             v.SlaDate,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
		}

		d.SettlementDate = timepkg.UnixToTime(&v.PaymentAt)
		resp = append(resp, d)
	}

	return response.HandleSuccessWithPagination(c, float64(total), paginate, resp)
}

func (h *CashOutHandler) AddCashOutItemsByIDs(c *fiber.Ctx) (err error) {
	var req entity.AddCashOutItemsByIDsRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	err = h.cashOutUseCase.AddCashOutItemsByIDs(c.UserContext(), req.CashOutID, req.CashInItemIDs)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, nil)
}

func (h *CashOutHandler) DeleteCashOutItemsByIDs(c *fiber.Ctx) (err error) {
	var req entity.DeleteCashOutItemsByIDsRequest
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	err = h.cashOutUseCase.DeleteCashOutItemsByIDs(c.UserContext(), req.CashOutID, req.CashOutItemIDs)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, nil)
}

func (h *CashOutHandler) Export(c *fiber.Ctx) (err error) {
	ctx := context.TODO()
	req := new(domain.CashoutExportReq)
	req.CashoutID, _ = strconv.Atoi(c.Query("cashout_id"))
	req.StartDatetime, err = timepkg.StringDateToTime(c.Query("start_date"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid start date")
		return errors.ErrorHandle(c, err)
	}
	req.EndDatetime, err = timepkg.StringDateToTime(c.Query("end_date"))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid end date")
		return errors.ErrorHandle(c, err)
	}
	filename, err := h.cashOutUseCase.Export(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}
	defer func(filename string) {
		err := os.Remove(filename)
		if err != nil {
			fmt.Println("error delete file export", err)
		}
	}(filename)

	// download filename
	n := time.Now()
	df := fmt.Sprintf("%s_cashout_report_%d.xlsx", n.Format("2006-01-02"), n.UnixMilli())

	c.Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", df))

	return c.SendFile(filename)
}

func (h *CashOutHandler) UpdateDoneStatus(c *fiber.Ctx) (err error) {
	var manualDone entity.ManualDoneRequest

	err = c.BodyParser(&manualDone)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	err = utils.ValidateStruct(manualDone)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	manualDone.Status = "done"

	cashOutId, err := c.ParamsInt("cashOutId")
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid cash out id")
		return errors.ErrorHandle(c, err)
	}

	err = h.cashOutUseCase.UpdateCashOutTransaction(c.UserContext(), cashOutId, &manualDone)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"message": "success",
	})
}

func (h *CashOutHandler) ResendEmail(c *fiber.Ctx) (err error) {
	cashOutId, err := c.ParamsInt("cashOutId")
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid cash out id")
		return errors.ErrorHandle(c, err)
	}

	err = h.cashOutUseCase.ResendEmail(c.UserContext(), cashOutId)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"message": "success",
	})
}
