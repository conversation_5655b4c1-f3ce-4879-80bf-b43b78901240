package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
)

func GenerateSignatureHmac256(payload string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(payload))

	mac := hex.EncodeToString(h.Sum(nil))
	return mac
}

func EncryptAES(stringToEncrypt string, keyString string) (encryptedString string, err error) {
	bytes := make([]byte, 32)
	copy(bytes, []byte(keyString))
	str := hex.EncodeToString(bytes)
	
	//Since the key is in string, we need to convert decode it to bytes
	key, _ := hex.DecodeString(str)
	plaintext := []byte(stringToEncrypt)

	//Create a new Cipher Block from the key
	block, err := aes.NewCipher(key)
	if err != nil {
		err = fmt.Errorf("error Encrypt: %v", err.Error())
		return
	}

	//Create a new GCM - https://en.wikipedia.org/wiki/Galois/Counter_Mode
	//https://golang.org/pkg/crypto/cipher/#NewGCM
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		err = fmt.Errorf("error chiper %v", err.Error())
		return
	}

	//Create a nonce. Nonce should be from GCM
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		err = fmt.Errorf("error read full %v", err.Error())
		return
	}

	//Encrypt the data using aesGCM.Seal
	//Since we don't want to save the nonce somewhere else in this case, we add it as a prefix to the encrypted data. The first nonce argument in Seal is the prefix.
	ciphertext := aesGCM.Seal(nonce, nonce, plaintext, nil)
	encryptedString = fmt.Sprintf("%x", ciphertext)
	return
}

func DecryptAES(encryptedString string, keyString string) (decryptedString string, err error) {
	bytes := make([]byte, 32)
	copy(bytes, []byte(keyString))
	str := hex.EncodeToString(bytes)
	
	key, _ := hex.DecodeString(str)
	enc, _ := hex.DecodeString(encryptedString)

	//Create a new Cipher Block from the key
	block, err := aes.NewCipher(key)
	if err != nil {
		err = fmt.Errorf("error decypt: %v", err.Error())
		return
	}

	//Create a new GCM
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		err = fmt.Errorf("error decypt: %v", err.Error())
		return
	}

	//Get the nonce size
	nonceSize := aesGCM.NonceSize()

	//Extract the nonce from the encrypted data
	nonce, ciphertext := enc[:nonceSize], enc[nonceSize:]

	//Decrypt the data
	plaintext, err := aesGCM.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		err = fmt.Errorf("error decryp open: %v", err.Error())
		return
	}

	decryptedString = string(plaintext)
	return 
}

func EncryptRSA(data, pubKey string) ([]byte, error) {
	publicKey := []byte(fmt.Sprintf("-----BEGIN PUBLIC KEY-----\n%s\n-----END PUBLIC KEY-----", pubKey))
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return nil, errors.New("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	pub := pubInterface.(*rsa.PublicKey)
	return rsa.EncryptPKCS1v15(rand.Reader, pub, []byte(data))
}

func DecriptRSA(data []byte, privKey string) ([]byte, error) {
	privateKey := []byte(fmt.Sprintf("-----BEGIN RSA PRIVATE KEY-----\n%s\n-----END RSA PRIVATE KEY-----", privKey))
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, errors.New("private key error")
	}
	privInterface, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	extraPrivkey := privInterface.(*rsa.PrivateKey)
	textByte, err := rsa.DecryptPKCS1v15(rand.Reader, extraPrivkey, data)
	if err != nil {
		return nil, err
	}
	return textByte, nil
}

// func EncryptAesCbc(secret, payload string) (resp string, err error) {
// 	lenBag := len(payload)/16
// 	size := (lenBag+1)*16
// 	plaintext := make([]byte, size)
// 	copy(plaintext, []byte(payload))
	
// 	key := make([]byte, 32)
// 	copy(key, []byte(secret))
	
// 	if len(plaintext)%aes.BlockSize != 0 {
//         err = fmt.Errorf("%v", "[EncrypAesCBC] plaintext is not a multiple of the block size")
// 		return
//     }

//     block, err := aes.NewCipher(key)
//     if err != nil {
//         err = fmt.Errorf("%v %v", "[EncrypAesCBC]", err.Error())
// 		return
//     }

//     ciphertext := make([]byte, aes.BlockSize+len(plaintext))
//     iv := ciphertext[:aes.BlockSize]
//     if _, err = io.ReadFull(rand.Reader, iv); err != nil {
//         err = fmt.Errorf("%v %v", "[EncryptAesCBC]", err.Error())
// 		return
//     }

//     cbc := cipher.NewCFBEncrypter(block, iv)
//     cbc.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

// 	resp = base64.URLEncoding.EncodeToString(ciphertext)
//     return
// }

// func DecryptAesCbc(key []byte, payload string) (plaintext string, err error) {
//     ciphertext, _ := base64.URLEncoding.DecodeString(payload)

// 	var block cipher.Block

//     if block, err = aes.NewCipher(key); err != nil {
// 		err = fmt.Errorf("%v %v", "[DecryptAesCBC]", err.Error())
//         return
//     }

//     if len(ciphertext) < aes.BlockSize {
//         err = fmt.Errorf("%v", "ciphertext too short")
//         return
//     }

//     iv := ciphertext[:aes.BlockSize]
//     ciphertext = ciphertext[aes.BlockSize:]

//     cbc := cipher.NewCFBDecrypter(block, iv)
//     cbc.XORKeyStream(ciphertext, ciphertext)

//     plaintext = fmt.Sprintf("%s", ciphertext)

//     return
// }

func EncryptAesCbc(key []byte, text string) (resp string, err error) {
	// key := []byte(keyText)
	plaintext := []byte(text)

	block, err := aes.NewCipher(key)
	if err != nil {
		err = fmt.Errorf("%v", err.Error())
		return
	}

	// The IV needs to be unique, but not secure. Therefore it's common to
	// include it at the beginning of the ciphertext.
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err = io.ReadFull(rand.Reader, iv); err != nil {
		err = fmt.Errorf("%v", err.Error())
		return
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	// convert to base64
	resp = base64.URLEncoding.EncodeToString(ciphertext)
	return 
}

// decrypt from base64 to decrypted string
func DecryptAesCbc(key []byte, cryptoText string) (resp string, err error) {
	ciphertext, _ := base64.URLEncoding.DecodeString(cryptoText)

	block, err := aes.NewCipher(key)
	if err != nil {
		err = fmt.Errorf("%v", err.Error())
		return
	}

	// The IV needs to be unique, but not secure. Therefore it's common to
	// include it at the beginning of the ciphertext.
	if len(ciphertext) < aes.BlockSize {
		err = fmt.Errorf("%v", "ciphertext too short")
		return
	}
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)

	// XORKeyStream can work in-place if the two arguments are the same.
	stream.XORKeyStream(ciphertext, ciphertext)

	resp = fmt.Sprintf("%s", ciphertext)
	return 
}