package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/user_management/usecase"
)

type defaultUserManagement struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.UserManagementRepository {
	return &defaultUserManagement{db}
}

func (r *defaultUserManagement) CreateUserMenu(ctx context.Context, req *domain.Menus) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultUserManagement) UpdateUserMenu(ctx context.Context, req *domain.Menus) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultUserManagement) GetUserMenuById(ctx context.Context, id string) (resp *domain.Menus, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultUserManagement) DeleteUserMenu(ctx context.Context, req *domain.Menus) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("menu_id = ?", req.ID).Delete(&domain.RolePermissions{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultUserManagement) GetListUserMenuAll(ctx context.Context) (resp *[]domain.Menus, err error) {
	err = r.db.WithContext(ctx).Model(&domain.Menus{}).Order("created_at ASC").Find(&resp).Error
	return
}
