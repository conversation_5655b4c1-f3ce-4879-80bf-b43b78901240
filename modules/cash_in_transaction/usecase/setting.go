package usecase

import (
	"github.com/go-redis/redis/v8"

	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapbca"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	channelMapping "repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	company "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	companyMapping "repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	companyProduct "repo.nusatek.id/nusatek/payment/modules/company_product/usecase"
	companyProductMapping "repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/usecase"
	partner "repo.nusatek.id/nusatek/payment/modules/partner/usecase"
	channel "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	paymentProvider "repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	snap "repo.nusatek.id/nusatek/payment/modules/snap/usecase"
	trxReqLog "repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
	user "repo.nusatek.id/nusatek/payment/modules/user_management/usecase"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/s3"
)

type defaultCashinTransaction struct {
	cashinRepo                   CashinTranscationRepo
	companyService               company.CompanyManagementUsecase
	companyProductService        companyProduct.CompanyProductUsecase
	companyProductMappingService companyProductMapping.Usecase
	partnerService               partner.PartnerUseCase
	xfersRest                    xfers.RestXfers
	channelService               channel.PaymentChannelUsecase
	providerService              paymentProvider.PaymentProviderUsecase
	channelMappingRepo           channelMapping.ChannelMappingRepository
	cache                        *redis.Client
	nicePayRest                  nicepay.RestNicePay
	snapNicepay                  snapclient.SNAP
	companyMappingRepo           companyMapping.CompanyMappingRepository
	companyMappingService        companyMapping.CompanyMappingUsecase
	xenditRest                   xendit.RestXendit
	ottocashRest                 ottocash.RestOttocash
	dokuRest                     doku.RestDoku
	cashlezRest                  *cashlez.Wrapper
	snapBcaRest                  *snapbca.Wrapper
	userService                  user.UserManagementUsecase
	snapRepo                     snap.Repository
	cashOutCallbackWhitelist     map[string]struct{}
	trxReqLogService             trxReqLog.TrxRequestLogUseCase
	s3                           *s3.Credential
}

func Setup() *defaultCashinTransaction {
	return &defaultCashinTransaction{}
}

func (s *defaultCashinTransaction) SetCashinRepo(t CashinTranscationRepo) *defaultCashinTransaction {
	s.cashinRepo = t
	return s
}

func (s *defaultCashinTransaction) SetCompanyService(t company.CompanyManagementUsecase) *defaultCashinTransaction {
	s.companyService = t
	return s
}

func (s *defaultCashinTransaction) SetCompanyProductService(t companyProduct.CompanyProductUsecase) *defaultCashinTransaction {
	s.companyProductService = t
	return s
}

func (s *defaultCashinTransaction) SetCompanyProductMappingService(t companyProductMapping.Usecase) *defaultCashinTransaction {
	s.companyProductMappingService = t
	return s
}

func (s *defaultCashinTransaction) SetPartnerService(t partner.PartnerUseCase) *defaultCashinTransaction {
	s.partnerService = t
	return s
}

func (s *defaultCashinTransaction) SetXfersRest(t xfers.RestXfers) *defaultCashinTransaction {
	s.xfersRest = t
	return s
}

func (s *defaultCashinTransaction) SetChannelService(t channel.PaymentChannelUsecase) *defaultCashinTransaction {
	s.channelService = t
	return s
}

func (s *defaultCashinTransaction) SetProviderService(t paymentProvider.PaymentProviderUsecase) *defaultCashinTransaction {
	s.providerService = t
	return s
}

func (s *defaultCashinTransaction) SetChannelMappingRepo(t channelMapping.ChannelMappingRepository) *defaultCashinTransaction {
	s.channelMappingRepo = t
	return s
}

func (s *defaultCashinTransaction) SetRedisClient(t *redis.Client) *defaultCashinTransaction {
	s.cache = t
	return s
}

func (s *defaultCashinTransaction) SetNicePayRest(t nicepay.RestNicePay) *defaultCashinTransaction {
	s.nicePayRest = t
	return s
}

func (s *defaultCashinTransaction) SetCompanyMappingRepo(t companyMapping.CompanyMappingRepository) *defaultCashinTransaction {
	s.companyMappingRepo = t
	return s
}

func (s *defaultCashinTransaction) SetCompanyMappingService(t companyMapping.CompanyMappingUsecase) *defaultCashinTransaction {
	s.companyMappingService = t
	return s
}

func (s *defaultCashinTransaction) SetXenditRest(t xendit.RestXendit) *defaultCashinTransaction {
	s.xenditRest = t
	return s
}

func (s *defaultCashinTransaction) SetOttocashRest(t ottocash.RestOttocash) *defaultCashinTransaction {
	s.ottocashRest = t
	return s
}

func (s *defaultCashinTransaction) SetDokuRest(t doku.RestDoku) *defaultCashinTransaction {
	s.dokuRest = t
	return s
}

func (s *defaultCashinTransaction) SetCashlezRest(t *cashlez.Wrapper) *defaultCashinTransaction {
	s.cashlezRest = t
	return s
}

func (s *defaultCashinTransaction) SetSnapBcaRest(t *snapbca.Wrapper) *defaultCashinTransaction {
	s.snapBcaRest = t
	return s
}

func (s *defaultCashinTransaction) SetUserService(t user.UserManagementUsecase) *defaultCashinTransaction {
	s.userService = t
	return s
}

func (s *defaultCashinTransaction) SetSnapRepo(snapRepo snap.Repository) *defaultCashinTransaction {
	s.snapRepo = snapRepo
	return s
}

func (s *defaultCashinTransaction) SetSnapNicepay(snapNicepay snapclient.SNAP) *defaultCashinTransaction {
	s.snapNicepay = snapNicepay
	return s
}

func (s *defaultCashinTransaction) SetTrxReqLogService(trxReqLogService trxReqLog.TrxRequestLogUseCase) *defaultCashinTransaction {
	s.trxReqLogService = trxReqLogService
	return s
}

func (s *defaultCashinTransaction) SetCashOutCallbackWhitelist() *defaultCashinTransaction {
	whitelists := config.GetStringSlice("company_callback.cash_out.whitelist")
	if len(whitelists) <= 0 {
		return s
	}

	s.cashOutCallbackWhitelist = make(map[string]struct{})
	for _, v := range whitelists {
		s.cashOutCallbackWhitelist[v] = struct{}{}
	}
	return s
}

func (s *defaultCashinTransaction) SetS3(t *s3.Credential) *defaultCashinTransaction {
	s.s3 = t
	return s
}

func (s *defaultCashinTransaction) Validate() CashinTransactionUsecase {
	if s.cashinRepo == nil {
		panic("cash in repo is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	if s.companyProductService == nil {
		panic("company product service is nil")
	}

	if s.partnerService == nil {
		panic("company partner service is nil")
	}

	if s.xfersRest == nil {
		panic("xfers rest is nil")
	}

	if s.channelService == nil {
		panic("channel service is nil")
	}

	if s.providerService == nil {
		panic("provider service is nil")
	}

	if s.channelMappingRepo == nil {
		panic("channel mapping repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.nicePayRest == nil {
		panic("nicepay rest is nil")
	}

	if s.companyMappingRepo == nil {
		panic("company mapping repo is nil")
	}

	if s.companyMappingService == nil {
		panic("company mapping service is nil")
	}

	if s.xenditRest == nil {
		panic("xendit rest is nil")
	}

	if s.ottocashRest == nil {
		panic("ottocash rest is nil")
	}

	if s.dokuRest == nil {
		panic("doku rest is nil")
	}

	return s
}
