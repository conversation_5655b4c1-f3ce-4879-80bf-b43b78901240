package nicepay

const (
	PayMethodCreditCard       string = "01"
	PayMethodVirtualAccount   string = "02"
	PayMethodConvenienceStore string = "03"
	PayMethodClickPay         string = "04"
	PayMethodEWallet          string = "05"
	PayMethodPayloan          string = "06"

	CurrencyIDR string = "IDR"

	DateTimeFormat = "********150405"
	DateFormat     = "********"
	TimeFormat     = "150405"

	ResultCdSuccess = "0000"

	//	Virtual Account status
	PaymentStatusVaPaid    = "0"
	PaymentStatusVaUnpaid  = "3"
	PaymentStatusVaExpired = "4"
)

type Credential struct {
	MerchantID  string `json:"merchant_id"`
	MerchantKey string `json:"merchant_key"`
}

type RegistrationRequest struct {
	TimeStamp       string `json:"timeStamp"`      // required YYYYMMDDHH24MISS
	IMid            string `json:"iMid"`           // required
	PayMethod       string `json:"payMethod"`      // required
	Currency        string `json:"currency"`       // required
	Amt             string `json:"amt"`            // required
	ReferenceNo     string `json:"referenceNo"`    // required
	GoodsNm         string `json:"goodsNm"`        // required
	BillingNm       string `json:"billingNm"`      // required
	BillingPhone    string `json:"billingPhone"`   // required
	BillingEmail    string `json:"billingEmail"`   // required
	BillingAddr     string `json:"billingAddr"`    // required
	BillingCity     string `json:"billingCity"`    // required
	BillingState    string `json:"billingState"`   // required
	BillingPostCd   string `json:"billingPostCd"`  // required
	BillingCountry  string `json:"billingCountry"` // required
	CartData        string `json:"cartData"`       // json string required default "{}"
	InstmntType     string `json:"instmntType"`    //required for CC
	InstmntMon      string `json:"instmntMon"`     // required
	RecurrOpt       string `json:"recurrOpt"`      // required
	BankCd          string `json:"bankCd"`         // required for VA
	VacctValidDt    string `json:"vacctValidDt"`   // required YYYYMMDD
	VacctValidTm    string `json:"vacctValidTm"`   // required HH24MISS
	MerFixAcctID    string `json:"merFixAcctId"`   // required
	MitraCd         string `json:"mitraCd"`        // required for CVS, E-Wallet, Payloan, QRIS
	UserIP          string `json:"userIP"`         // required for CC, E-Wallet, Payloan, QRIS
	DbProcessURL    string `json:"dbProcessUrl"`   // required
	MerchantToken   string `json:"merchantToken"`  // required
	DeliveryNm      string `json:"deliveryNm"`
	DeliveryPhone   string `json:"deliveryPhone"`
	DeliveryAddr    string `json:"deliveryAddr"`
	DeliveryCity    string `json:"deliveryCity"`
	DeliveryState   string `json:"deliveryState"`
	DeliveryPostCd  string `json:"deliveryPostCd"`
	DeliveryCountry string `json:"deliveryCountry"`
	Vat             string `json:"vat"`
	Fee             string `json:"fee"`
	NotaxAmt        string `json:"notaxAmt"`
	Description     string `json:"description"`
	ReqDt           string `json:"reqDt"`
	ReqTm           string `json:"reqTm"`
	ReqDomain       string `json:"reqDomain"`
	ReqServerIP     string `json:"reqServerIP"`
	ReqClientVer    string `json:"reqClientVer"`
	UserSessionID   string `json:"userSessionID"`
	UserAgent       string `json:"userAgent"`
	UserLanguage    string `json:"userLanguage"`
	ShopID          string `json:"shopId"` // required for QRIS
}

type RegistrationResponse struct {
	ResultCd     string      `json:"resultCd"`
	ResultMsg    string      `json:"resultMsg"`
	TXid         string      `json:"tXid"`
	ReferenceNo  string      `json:"referenceNo"`
	PayMethod    string      `json:"payMethod"`
	Amt          string      `json:"amt"`
	TransDt      string      `json:"transDt"`
	TransTm      string      `json:"transTm"`
	Description  interface{} `json:"description"`
	BankCd       string      `json:"bankCd"`
	VacctNo      string      `json:"vacctNo"`
	MitraCd      interface{} `json:"mitraCd"`
	PayNo        interface{} `json:"payNo"`
	Currency     string      `json:"currency"`
	GoodsNm      string      `json:"goodsNm"`
	BillingNm    string      `json:"billingNm"`
	VacctValidDt string      `json:"vacctValidDt"`
	VacctValidTm string      `json:"vacctValidTm"`
	PayValidDt   interface{} `json:"payValidDt"`
	PayValidTm   interface{} `json:"payValidTm"`
	RequestURL   interface{} `json:"requestURL"`
	PaymentExpDt interface{} `json:"paymentExpDt"`
	PaymentExpTm interface{} `json:"paymentExpTm"`
	QrContent    interface{} `json:"qrContent"`
	QrURL        interface{} `json:"qrUrl"`
}

type InquiryRequest struct {
	TimeStamp     string `json:"timeStamp"`
	MerchantToken string `json:"merchantToken"`
	ReferenceNo   string `json:"referenceNo"`
	TXid          string `json:"tXid"`
	Amt           string `json:"amt"`
	IMid          string `json:"iMid"`
}

type InquiryResponse struct {
	TXid           string      `json:"tXid"`
	IMid           string      `json:"iMid"`
	Currency       string      `json:"currency"`
	Amt            string      `json:"amt"`
	InstmntMon     string      `json:"instmntMon"`
	InstmntType    string      `json:"instmntType"`
	ReferenceNo    string      `json:"referenceNo"`
	GoodsNm        string      `json:"goodsNm"`
	PayMethod      string      `json:"payMethod"`
	BillingNm      string      `json:"billingNm"`
	ReqDt          string      `json:"reqDt"`
	ReqTm          string      `json:"reqTm"`
	Status         string      `json:"status"`
	ResultCd       string      `json:"resultCd"`
	ResultMsg      string      `json:"resultMsg"`
	CardNo         interface{} `json:"cardNo"`
	PreauthToken   interface{} `json:"preauthToken"`
	AcquBankCd     interface{} `json:"acquBankCd"`
	IssuBankCd     interface{} `json:"issuBankCd"`
	VacctValidDt   string      `json:"vacctValidDt"`
	VacctValidTm   string      `json:"vacctValidTm"`
	VacctNo        string      `json:"vacctNo"`
	BankCd         string      `json:"bankCd"`
	PayNo          interface{} `json:"payNo"`
	MitraCd        interface{} `json:"mitraCd"`
	ReceiptCode    interface{} `json:"receiptCode"`
	CancelAmt      interface{} `json:"cancelAmt"`
	TransDt        string      `json:"transDt"`
	TransTm        string      `json:"transTm"`
	RecurringToken interface{} `json:"recurringToken"`
	CcTransType    interface{} `json:"ccTransType"`
	PayValidDt     interface{} `json:"payValidDt"`
	PayValidTm     interface{} `json:"payValidTm"`
	MRefNo         interface{} `json:"mRefNo"`
	AcquStatus     interface{} `json:"acquStatus"`
	CardExpYymm    interface{} `json:"cardExpYymm"`
	AcquBankNm     interface{} `json:"acquBankNm"`
	IssuBankNm     interface{} `json:"issuBankNm"`
	DepositDt      interface{} `json:"depositDt"`
	DepositTm      interface{} `json:"depositTm"`
	PaymentExpDt   interface{} `json:"paymentExpDt"`
	PaymentExpTm   interface{} `json:"paymentExpTm"`
	PaymentTrxSn   interface{} `json:"paymentTrxSn"`
	CancelTrxSn    interface{} `json:"cancelTrxSn"`
	UserID         interface{} `json:"userId"`
	ShopID         interface{} `json:"shopId"`
}

type CancelRequest struct {
	TimeStamp      string `json:"timeStamp"`
	TXid           string `json:"tXid"`
	IMid           string `json:"iMid"`
	PayMethod      string `json:"payMethod"`
	CancelType     string `json:"cancelType"`
	CancelMsg      string `json:"cancelMsg"`
	MerchantToken  string `json:"merchantToken"`
	PreauthToken   string `json:"preauthToken"`
	Amt            string `json:"amt"`
	CancelServerIP string `json:"cancelServerIp"`
	CancelUserID   string `json:"cancelUserId"`
	CancelUserIP   string `json:"cancelUserIp"`
	CancelUserInfo string `json:"cancelUserInfo"`
	CancelRetryCnt string `json:"cancelRetryCnt"`
	ReferenceNo    string `json:"referenceNo"`
	Worker         string `json:"worker"`
}

type CancelResponse struct {
	TXid              string `json:"tXid"`
	ReferenceNo       string `json:"referenceNo"`
	ResultCd          string `json:"resultCd"`
	ResultMsg         string `json:"resultMsg"`
	TransDt           string `json:"transDt"`
	TransTm           string `json:"transTm"`
	Amt               string `json:"amt"`
	CancelReferenceNo string `json:"cancelReferenceNo"`
}

type PaymentRequest struct {
	TimeStamp        string `json:"timeStamp"`
	TransactionID    string `json:"tXid"`
	CreditCardNumber string `json:"cardNo"`
	CardExpiry       string `json:"cardExpYymm"`
	CardCvv          string `json:"cardCvv"`
	CardHolderNm     string `json:"cardHolderNm"`
	RecurringToken   string `json:"recurringToken"`
	PreAuthToken     string `json:"preauthToken"`
	MerchantToken    string `json:"merchantToken"`
	CallBackUrl      string `json:"callBackUrl"`
	ClickPayNo       string `json:"clickPayNo"`
	DataField3       string `json:"dataField3"`
	ClickPayToken    string `json:"clickPayToken"`
}

type PaymentResponse struct {
	ResultCd    int64   `json:"resultCd"`
	ResultMsg   string  `json:"resultMsg"`
	TXid        string  `json:"tXid"`
	ReferenceNo string  `json:"referenceNo"`
	PayMethod   float64 `json:"payMethod"`
	Amt         float64 `json:"amt"`
	Currency    string  `json:"currency"`
	GoodsNm     string  `json:"goodsNm"`
	BillingNm   string  `json:"billingNm"`
	TransDt     int64   `json:"transDt"`
	TransTm     int64   `json:"transTm"`
	Description string  `json:"description"`
	MitraCd     string  `json:"mitraCd"`
}

// call back from nicepay
// example:
/*
merchantToken=1300bcbc5f881cf2988cc70f618b0eaa6cb25990e3e6e0e61d9005090bb3c873&
goodsNm=Cust&referenceNo=INV20220813-0009-PRO8-CH31&
transTm=203702&tXid=IONPAYTEST02********2036413955&
amt=11000&
vacctNo=111111102036413955&
instmntType=1&
billingNm=Cust&
matchCl=1&
vacctValidDt=********&
payMethod=02&
bankCd=CENA&
currency=IDR&
instmntMon=null&
vacctValidTm=213643&
transDt=********&
status=0
*/
type NotificationRequest struct {
	TXid                            string  `json:"tXid"`
	MerchantToken                   string  `json:"merchantToken"`
	ReferenceNo                     string  `json:"referenceNo"`
	PayMethod                       int     `json:"payMethod"`
	Amt                             float64 `json:"amt"`
	TransDt                         string  `json:"transDt"`
	TransTm                         string  `json:"transTm"`
	Currency                        string  `json:"currency"`
	GoodsNm                         string  `json:"goodsNm"`
	BillingNm                       string  `json:"billingNm"`
	MatchCl                         int     `json:"matchCl"`
	Status                          int     `json:"status"`
	NotificationAdditionalVARequest         // additional param for VA
}

type NotificationAdditionalVARequest struct {
	BankCd       string `json:"bankCd"`
	VacctNo      string `json:"vacctNo"`
	VacctValidDt string `json:"vacctValidDt"`
	VacctValidTm string `json:"vacctValidTm"`
	DepositDt    string `json:"depositDt"`
	DepositTm    string `json:"depositTm"`
}
