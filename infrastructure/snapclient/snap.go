// client for integration with SNAP API
package snapclient

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"

	"repo.nusatek.id/nusatek/payment/utils/snaputil"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

type SNAP interface {
	// virtual account
	TransferVaCreateVA(ctx context.Context, cred iCredential, hreq HTrxReq, req *TransferVaCreateVaReq) (resp TransferVaCreateVaResp, err error)
	TransferVaStatus(ctx context.Context, cred iCredential, hreq HTrxReq, req *TransferVaStatusReq) (resp TransferVaStatusResp, err error)
}

var _ SNAP = (*Wrapper)(nil)

const (
	HeaderAuthorization = "Authorization"
	HeaderTimestamp     = "X-TIMESTAMP"
	HeaderClientKey     = "X-CLIENT-KEY"
	HeaderSignature     = "X-SIGNATURE"
	HeaderChannelId     = "CHANNEL-ID"
	HeaderPartnerId     = "X-PARTNER-ID"
	HeaderExternalId    = "X-EXTERNAL-ID"
)

type Wrapper struct {
	BaseUrl string
	RPaths  RegisterPaths
	Client  *resty.Client
	now     func() time.Time

	clientTokens      map[string]clientToken
	clientTokensMutex *sync.Mutex
}

type clientToken struct {
	accessToken string
	expiredAt   time.Time // SNAP BI defined expired time is 15 minutes
}

type Opt func(*Wrapper)

func WithRegisterPaths(rp RegisterPaths) Opt {
	return func(w *Wrapper) {
		w.RPaths = rp
	}
}

func WithBaseUrl(baseUrl string) Opt {
	return func(w *Wrapper) {
		w.BaseUrl = baseUrl
	}
}

func WithDebug() Opt {
	return func(w *Wrapper) {
		w.Client.SetDebug(true)
	}
}

func NewWrapper(opts ...Opt) *Wrapper {
	w := new(Wrapper)
	w.now = time.Now
	w.RPaths = DefaultPaths()
	w.Client = resty.New()
	w.clientTokens = make(map[string]clientToken)
	w.clientTokensMutex = new(sync.Mutex)
	for _, opt := range opts {
		opt(w)
	}

	return w
}

func (w *Wrapper) Time() time.Time {
	return w.now().In(timepkg.TimeJakartaLoc)
}

type DoReq struct {
	Header  map[string]string
	Method  string
	Path    string
	ReqBody interface{}
}

type BaseResp struct {
	ResponseCode    string `json:"responseCode"`
	ResponseMessage string `json:"responseMessage"`
}

type ibaseResp interface {
	getBaseResp() BaseResp
}

func (b *BaseResp) getBaseResp() BaseResp {

	return *b
}

// implement error interface
func (b BaseResp) Error() string {

	return b.ResponseMessage
}

// general function for http request
func (w *Wrapper) do(ctx context.Context, doreq DoReq, result ibaseResp) (req *resty.Request, res *resty.Response, err error) {
	fullURL := w.BaseUrl + doreq.Path

	// default header
	if doreq.Header == nil {
		doreq.Header = make(map[string]string)
	}
	doreq.Header["Content-Type"] = "application/json"

	// prepare http client
	req = w.Client.R().
		SetHeaders(doreq.Header)
	if doreq.ReqBody != nil {
		req.SetBody(doreq.ReqBody)
	}

	var rr *resty.Response
	logger.Info(ctx, "request", logger.Any("fullURL", fullURL), logger.Any("header", req.Header), logger.Any("requestBody", doreq.ReqBody))
	switch doreq.Method {
	case http.MethodGet:
		rr, err = req.Get(fullURL)
	case http.MethodPost:
		rr, err = req.Post(fullURL)
	case http.MethodPut:
		rr, err = req.Put(fullURL)
	case http.MethodPatch:
		rr, err = req.Patch(fullURL)
	case http.MethodDelete:
		rr, err = req.Delete(fullURL)
	}
	res = rr
	if err != nil {
		logger.Error(ctx, "request error", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.Any("header", rr.Header()), logger.Int("statusCode", rr.StatusCode()), logger.Any("responseBody", string(rr.Body())))

	err = json.Unmarshal(rr.Body(), &result)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.Err(err))
		return
	}

	if rr.StatusCode() < 200 || rr.StatusCode() > 299 {
		err = result.getBaseResp()
		logger.Error(ctx, "invalid status", logger.Err(err))
		return
	}

	return req, res, nil
}

func (w *Wrapper) accessTokenHeaders(ctx context.Context, icred iCredential) (map[string]string, error) {
	headers := make(map[string]string)
	xtimestamp := w.Time().Format(time.RFC3339)
	cred := icred.GetCred()
	signature, err := snaputil.GenerateAsymetricSignature(cred.PrivateKey(), cred.ClientID, xtimestamp)
	if err != nil {
		logger.Error(ctx, "GenerateAsymetricSignature error", logger.Err(err))
		return nil, err
	}
	headers["Content-Type"] = "application/json"
	headers[HeaderTimestamp] = xtimestamp
	headers[HeaderClientKey] = cred.ClientID
	headers[HeaderSignature] = signature

	return headers, nil
}

func (w *Wrapper) transactionHeaders(ctx context.Context, accessToken string, clientSecret string, req DoReq) (map[string]string, error) {
	if req.Header == nil {
		req.Header = make(map[string]string)
	}
	reqURL, _ := url.Parse(req.Path)
	tm := w.Time()
	xtimestamp := tm.Format(time.RFC3339)
	path := reqURL.Path + reqURL.Query().Encode()
	reqBodyB, _ := json.Marshal(req.ReqBody)
	signature, err := snaputil.GenerateSymetricSignature(accessToken, req.Method, path, string(reqBodyB), xtimestamp, clientSecret)
	if err != nil {
		logger.Error(ctx, "GenerateSymetricSignature error", logger.Err(err))
		return nil, err
	}
	rand.Seed(tm.UnixNano())
	externalId := fmt.Sprintf("%06d", rand.Intn(1000000))
	req.Header["Content-Type"] = "application/json"
	req.Header[HeaderTimestamp] = xtimestamp
	req.Header[HeaderSignature] = signature
	req.Header[HeaderAuthorization] = "Bearer " + accessToken
	req.Header[HeaderExternalId] = externalId

	return req.Header, nil
}

type Money struct {
	Value    string `json:"value"`
	Currency string `json:"currency"`
}

const (
	CurrencyIDR = "IDR"
)

func NewMoney(currency string, amount float64) Money {
	return Money{
		Currency: "IDR",
		Value:    fmt.Sprintf("%.2f", amount),
	}
}

// header transaction request
type HTrxReq struct {
	XPartnerId string
	ChannelId  string
}

type AdditionalInfo struct {
	Data map[string]interface{} `json:"additionalInfo"`
}

func (ai AdditionalInfo) Get(key string) interface{} {
	if ai.Data == nil {
		ai.Data = make(map[string]interface{})
	}
	return ai.Data[key]
}
