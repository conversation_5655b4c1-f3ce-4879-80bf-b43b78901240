package usecase

import (
	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/nusatek/payment/utils/s3"
)

type defaultFile struct {
	fileRepo FileRepository
	cache    *redis.Client
	s3       *s3.Credential
}

func Setup() *defaultFile {
	return &defaultFile{}
}

func (s *defaultFile) SetFileRepo(t FileRepository) *defaultFile {
	s.fileRepo = t
	return s
}

func (s *defaultFile) SetRedisClient(t *redis.Client) *defaultFile {
	s.cache = t
	return s
}

func (s *defaultFile) SetS3(t *s3.Credential) *defaultFile {
	s.s3 = t
	return s
}

func (s *defaultFile) Validate() FileUsecase {
	if s.fileRepo == nil {
		panic("payment channel repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.s3 == nil {
		panic("s3 is nil")
	}

	return s
}
