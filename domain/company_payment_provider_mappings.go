package domain

import (
	"time"

	"gorm.io/gorm"
)

type CompanyPaymentProviderMappings struct {
	ID                int64          `gorm:"primaryKey" json:"id"`
	CompanyID         int64          `json:"company_id"`
	PaymentProviderID int64          `json:"payment_provider_id"`
	ProviderSecrets   string         `json:"provider_secrets"`
	Status            bool           `json:"status"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
