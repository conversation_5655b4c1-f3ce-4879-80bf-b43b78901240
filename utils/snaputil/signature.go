package snaputil

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"repo.nusatek.id/nusatek/payment/utils/signature"
)

// client key = client id
func GenerateAsymetricSignature(privPEM, clientKey, timestampStr string) (sign string, err error) {
	privKey, err := signature.ParseRsaPrivateKeyFromPemStr(privPEM)
	if err != nil {
		log.Println(fmt.Sprintf("error ParseRsaPrivateKeyFromPemStr %v", err))
		return
	}

	stringToSign := clientKey + "|" + timestampStr
	signB, err := signature.SignSHA256WithRSA(privKey, []byte(stringToSign))
	if err != nil {
		log.Println(fmt.Sprintf("error SignSHA256WithRSA %v", err))
		return
	}
	sign = string(signB)
	return
}

func VerifyAsymetricSignature(pubPEM, clientKey, timestampStr, signatureMsg string) (err error) {
	pubKey, err := signature.ParseRsaPublicKeyFromPemStr(pubPEM)
	if err != nil {
		log.Println(fmt.Sprintf("error ParseRsaPublicKeyFromPemStr %v", err))
		return
	}

	stringToSign := clientKey + "|" + timestampStr

	//Signature Type PKCS
	err = signature.VerifyPKCS(pubKey, stringToSign, signatureMsg)
	if err != nil {
		log.Println(fmt.Sprintf("error generate token %v", err))
		return
	}

	return
}

func GenerateSymetricSignature(accessToken, httpMethod, path, reqBody, xtimestampStr, clientSecret string) (sign string, err error) {
	var body string = ""
	dst := &bytes.Buffer{}
	json.Compact(dst, []byte(reqBody))
	body = dst.String()

	// encode body
	h := sha256.New()
	h.Write([]byte(string(body)))
	hash := h.Sum(nil)
	encodedBody := strings.ToLower(hex.EncodeToString(hash))

	msg := httpMethod + ":" + path + ":" + accessToken + ":" + encodedBody + ":" + xtimestampStr

	mac := hmac.New(sha512.New, []byte(clientSecret))
	_, err = mac.Write([]byte(msg))
	if err != nil {
		log.Println(fmt.Sprintf("error hmac %v", err))
		return
	}

	expectedMAC := mac.Sum(nil)
	sign = base64.StdEncoding.EncodeToString(expectedMAC)
	return
}
