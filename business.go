package ottopay

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

// BusinessIntegration provides integration points for business logic
type BusinessIntegration struct {
	vaHelper       *VANumberHelper
	formatHelper   *FormatHelper
	requestHelper  *RequestIDHelper
	securityHelper *SecurityHelper
}

// NewBusinessIntegration creates a new business integration instance
func NewBusinessIntegration() *BusinessIntegration {
	return &BusinessIntegration{
		vaHelper:       NewVANumberHelper(),
		formatHelper:   NewFormatHelper(),
		requestHelper:  NewRequestIDHelper(),
		securityHelper: NewSecurityHelper(),
	}
}

// VANumberGenerator provides Virtual Account number generation
type VANumberGenerator struct {
	config Config
}

// NewVANumberGenerator creates a new VA number generator
func NewVANumberGenerator(config Config) *VANumberGenerator {
	return &VANumberGenerator{config: config}
}

// GenerateVANumber generates a Virtual Account number based on channel and customer info
func (vg *VANumberGenerator) GenerateVANumber(channelType, customerNumber string) (string, error) {
	// Get prefix for the channel
	prefix, exists := vg.config.GetBankPrefix(channelType)
	if !exists {
		return "", fmt.Errorf("unsupported channel type: %s", channelType)
	}

	// Validate customer number
	if customerNumber == "" {
		return "", fmt.Errorf("customer number is required")
	}

	// Clean customer number (remove non-numeric characters)
	cleanCustomerNumber := ""
	for _, char := range customerNumber {
		if char >= '0' && char <= '9' {
			cleanCustomerNumber += string(char)
		}
	}

	if cleanCustomerNumber == "" {
		return "", fmt.Errorf("customer number must contain numeric characters")
	}

	// Ensure customer number is max 11 digits
	if len(cleanCustomerNumber) > 11 {
		cleanCustomerNumber = cleanCustomerNumber[len(cleanCustomerNumber)-11:]
	}

	// Generate VA number
	vaNumber := prefix + cleanCustomerNumber
	return vaNumber, nil
}

// ValidateVANumber validates a Virtual Account number
func (vg *VANumberGenerator) ValidateVANumber(vaNumber, channelType string) error {
	// Get expected prefix
	expectedPrefix, exists := vg.config.GetBankPrefix(channelType)
	if !exists {
		return fmt.Errorf("unsupported channel type: %s", channelType)
	}

	// Check if VA number starts with expected prefix
	if !strings.HasPrefix(vaNumber, expectedPrefix) {
		return fmt.Errorf("VA number does not match channel prefix")
	}

	// Validate format
	if len(vaNumber) < len(expectedPrefix)+1 || len(vaNumber) > len(expectedPrefix)+11 {
		return fmt.Errorf("invalid VA number length")
	}

	// Check if all characters are numeric
	for _, char := range vaNumber {
		if char < '0' || char > '9' {
			return fmt.Errorf("VA number must be numeric")
		}
	}

	return nil
}

// ExtractCustomerNumber extracts customer number from VA number
func (vg *VANumberGenerator) ExtractCustomerNumber(vaNumber, channelType string) (string, error) {
	prefix, exists := vg.config.GetBankPrefix(channelType)
	if !exists {
		return "", fmt.Errorf("unsupported channel type: %s", channelType)
	}

	if !strings.HasPrefix(vaNumber, prefix) {
		return "", fmt.Errorf("VA number does not match channel prefix")
	}

	customerNumber := vaNumber[len(prefix):]
	if customerNumber == "" {
		return "", fmt.Errorf("no customer number found in VA")
	}

	return customerNumber, nil
}

// DatabaseIntegration defines interfaces for database operations
type DatabaseIntegration interface {
	// User/Authentication operations
	ValidateUserCredentials(ctx context.Context, username, password string) (*UserInfo, error)
	GetUserByID(ctx context.Context, userID string) (*UserInfo, error)

	// Virtual Account operations
	CreateVirtualAccount(ctx context.Context, va *VirtualAccount) error
	GetVirtualAccount(ctx context.Context, vaNumber string) (*VirtualAccount, error)
	UpdateVirtualAccountStatus(ctx context.Context, vaNumber, status string) error

	// Transaction operations
	CreateInquiryLog(ctx context.Context, log *InquiryLog) error
	CreatePaymentLog(ctx context.Context, log *PaymentLog) error
	GetTransactionByRequestID(ctx context.Context, requestID string) (*Transaction, error)
	UpdateTransactionStatus(ctx context.Context, requestID, status string) error

	// Company/Provider operations
	GetCompanyByCode(ctx context.Context, companyCode string) (*Company, error)
	GetProviderConfig(ctx context.Context, providerID string) (*ProviderConfig, error)
}

// Data models for database integration
type UserInfo struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	IsActive bool   `json:"is_active"`
	Company  string `json:"company"`
}

type VirtualAccount struct {
	ID             string    `json:"id"`
	VANumber       string    `json:"va_number"`
	CompanyCode    string    `json:"company_code"`
	CustomerNumber string    `json:"customer_number"`
	ChannelType    string    `json:"channel_type"`
	CustomerName   string    `json:"customer_name"`
	Amount         float64   `json:"amount"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	ExpiresAt      time.Time `json:"expires_at"`
}

type InquiryLog struct {
	ID             string    `json:"id"`
	RequestID      string    `json:"request_id"`
	CompanyCode    string    `json:"company_code"`
	CustomerNumber string    `json:"customer_number"`
	ChannelType    string    `json:"channel_type"`
	Status         string    `json:"status"`
	Response       string    `json:"response"`
	CreatedAt      time.Time `json:"created_at"`
	IPAddress      string    `json:"ip_address"`
	UserAgent      string    `json:"user_agent"`
}

type PaymentLog struct {
	ID             string    `json:"id"`
	RequestID      string    `json:"request_id"`
	CompanyCode    string    `json:"company_code"`
	CustomerNumber string    `json:"customer_number"`
	ChannelType    string    `json:"channel_type"`
	PaidAmount     float64   `json:"paid_amount"`
	TotalAmount    float64   `json:"total_amount"`
	Status         string    `json:"status"`
	Response       string    `json:"response"`
	CreatedAt      time.Time `json:"created_at"`
	IPAddress      string    `json:"ip_address"`
	Reference      string    `json:"reference"`
}

type Transaction struct {
	ID             string    `json:"id"`
	RequestID      string    `json:"request_id"`
	VANumber       string    `json:"va_number"`
	CompanyCode    string    `json:"company_code"`
	CustomerNumber string    `json:"customer_number"`
	Amount         float64   `json:"amount"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type Company struct {
	ID          string `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	IsActive    bool   `json:"is_active"`
	CallbackURL string `json:"callback_url"`
}

type ProviderConfig struct {
	ID           string            `json:"id"`
	ProviderName string            `json:"provider_name"`
	IsActive     bool              `json:"is_active"`
	Config       map[string]string `json:"config"`
}

// DefaultBusinessHandler provides a default implementation of BusinessLogicHandler
type DefaultBusinessHandler struct {
	db        DatabaseIntegration
	vaGen     *VANumberGenerator
	validator *Validator

	// retry/timeout config
	retryMaxAttempts       int
	retryInitialBackoff    time.Duration
	retryMaxBackoff        time.Duration
	retryBackoffMultiplier float64
	requestTimeout         time.Duration
}

// NewDefaultBusinessHandler creates a new default business handler
func NewDefaultBusinessHandler(db DatabaseIntegration, config Config) *DefaultBusinessHandler {
	return &DefaultBusinessHandler{
		db:                     db,
		vaGen:                  NewVANumberGenerator(config),
		validator:              NewValidator(),
		retryMaxAttempts:       max(1, config.RetryMaxAttempts),
		retryInitialBackoff:    config.RetryInitialBackoff,
		retryMaxBackoff:        config.RetryMaxBackoff,
		retryBackoffMultiplier: config.RetryBackoffMultiplier,
		requestTimeout:         config.RequestTimeout,
	}
}

// ValidateCredentials validates username/password for token generation
func (h *DefaultBusinessHandler) ValidateCredentials(ctx context.Context, username, password string) (UserData, error) {
	userInfo := &UserInfo{}
	err := h.doWithRetry(ctx, "ValidateUserCredentials", func(ctx context.Context) error {
		u, err := h.db.ValidateUserCredentials(ctx, username, password)
		if err != nil {
			return err
		}
		userInfo = u
		return nil
	})
	if err != nil {
		return UserData{}, fmt.Errorf("credential validation failed: %w", err)
	}

	if !userInfo.IsActive {
		return UserData{}, fmt.Errorf("user account is inactive")
	}

	return UserData{
		ID:       userInfo.ID,
		Username: userInfo.Username,
		IsValid:  true,
	}, nil
}

// HandleInquiry processes inquiry requests and returns customer/bill information
func (h *DefaultBusinessHandler) HandleInquiry(ctx context.Context, req InquiryRequest) (InquiryData, error) {
	// Validate request
	if err := h.validator.ValidateInquiryRequest(req); err != nil {
		return InquiryData{IsValid: false, ErrorMessage: err.Error()}, nil
	}

	// Generate VA number for lookup
	vaNumber, err := h.vaGen.GenerateVANumber(req.ChannelType, req.CustomerNumber)
	if err != nil {
		return InquiryData{IsValid: false, ErrorMessage: "Invalid VA number format"}, nil
	}

	// Get virtual account from database with retry/timeout
	var va *VirtualAccount
	err = h.doWithRetry(ctx, "GetVirtualAccount", func(ctx context.Context) error {
		var err error
		va, err = h.db.GetVirtualAccount(ctx, vaNumber)
		return err
	})
	if err != nil {
		// Log inquiry attempt
		h.logInquiry(ctx, req, "FAILED", "VA not found")
		return InquiryData{IsValid: false, ErrorMessage: "Virtual Account not found"}, nil
	}

	// Check if VA is active and not expired
	if va.Status != "ACTIVE" {
		h.logInquiry(ctx, req, "FAILED", "VA inactive")
		return InquiryData{IsValid: false, ErrorMessage: "Virtual Account is not active"}, nil
	}

	if time.Now().After(va.ExpiresAt) {
		h.logInquiry(ctx, req, "FAILED", "VA expired")
		return InquiryData{IsValid: false, ErrorMessage: "Virtual Account has expired"}, nil
	}

	// Log successful inquiry
	h.logInquiry(ctx, req, "SUCCESS", "Inquiry successful")

	return InquiryData{
		CustomerName:   va.CustomerName,
		TotalAmount:    va.Amount,
		CurrencyCode:   CurrencyIDR,
		SubCompany:     DefaultSubCompany,
		AdditionalData: "",
		IsValid:        true,
	}, nil
}

// HandlePayment processes payment requests and returns payment status
func (h *DefaultBusinessHandler) HandlePayment(ctx context.Context, req PaymentRequest) (PaymentData, error) {
	// Validate request
	if err := h.validator.ValidatePaymentRequest(req); err != nil {
		return PaymentData{IsSuccess: false, ErrorMessage: err.Error()}, nil
	}

	// Parse amounts
	paidAmount, err := strconv.ParseFloat(req.PaidAmount, 64)
	if err != nil {
		return PaymentData{IsSuccess: false, ErrorMessage: "Invalid paid amount"}, nil
	}

	totalAmount, err := strconv.ParseFloat(req.TotalAmount, 64)
	if err != nil {
		return PaymentData{IsSuccess: false, ErrorMessage: "Invalid total amount"}, nil
	}

	// Generate VA number for lookup
	vaNumber, err := h.vaGen.GenerateVANumber(req.ChannelType, req.CustomerNumber)
	if err != nil {
		return PaymentData{IsSuccess: false, ErrorMessage: "Invalid VA number format"}, nil
	}

	// Get virtual account from database with retry/timeout
	var va *VirtualAccount
	err = h.doWithRetry(ctx, "GetVirtualAccount", func(ctx context.Context) error {
		var err error
		va, err = h.db.GetVirtualAccount(ctx, vaNumber)
		return err
	})
	if err != nil {
		h.logPayment(ctx, req, "FAILED", "VA not found")
		return PaymentData{IsSuccess: false, ErrorMessage: "Virtual Account not found"}, nil
	}

	// Validate payment amount
	if paidAmount != va.Amount {
		h.logPayment(ctx, req, "FAILED", "Amount mismatch")
		return PaymentData{IsSuccess: false, ErrorMessage: "Payment amount does not match bill amount"}, nil
	}

	// Check for duplicate payment with retry/timeout
	var existingTx *Transaction
	_ = h.doWithRetry(ctx, "GetTransactionByRequestID", func(ctx context.Context) error {
		var err error
		existingTx, err = h.db.GetTransactionByRequestID(ctx, req.RequestID)
		return err
	})
	if existingTx != nil && existingTx.Status == "PAID" {
		h.logPayment(ctx, req, "FAILED", "Duplicate payment")
		return PaymentData{IsSuccess: false, ErrorMessage: "Payment already processed"}, nil
	}

	// Process payment (update VA status, create transaction record) with retry/timeout
	if err := h.doWithRetry(ctx, "UpdateVirtualAccountStatus", func(ctx context.Context) error {
		return h.db.UpdateVirtualAccountStatus(ctx, vaNumber, "PAID")
	}); err != nil {
		h.logPayment(ctx, req, "FAILED", "Database update failed")
		return PaymentData{IsSuccess: false, ErrorMessage: "Payment processing failed"}, nil
	}

	// Log successful payment
	h.logPayment(ctx, req, "SUCCESS", "Payment successful")

	return PaymentData{
		IsSuccess:      true,
		CustomerName:   va.CustomerName,
		TotalAmount:    totalAmount,
		PaidAmount:     paidAmount,
		CurrencyCode:   req.CurrencyCode,
		AdditionalData: "",
	}, nil
}

// Helper methods for logging
func (h *DefaultBusinessHandler) logInquiry(ctx context.Context, req InquiryRequest, status, message string) {
	log := &InquiryLog{
		RequestID:      req.RequestID,
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		ChannelType:    req.ChannelType,
		Status:         status,
		Response:       message,
		CreatedAt:      time.Now(),
		IPAddress:      "", // Extract from context if available
	}
	_ = h.doWithRetry(ctx, "CreateInquiryLog", func(ctx context.Context) error {
		return h.db.CreateInquiryLog(ctx, log)
	})
}

// doWithRetry wraps an operation with context timeout and retry using exponential backoff.
func (h *DefaultBusinessHandler) doWithRetry(parentCtx context.Context, opName string, fn func(ctx context.Context) error) error {
	attempts := h.retryMaxAttempts
	if attempts < 1 {
		attempts = 1
	}
	backoff := h.retryInitialBackoff
	if backoff <= 0 {
		backoff = 100 * time.Millisecond
	}
	maxBackoff := h.retryMaxBackoff
	if maxBackoff <= 0 {
		maxBackoff = 2 * time.Second
	}
	mult := h.retryBackoffMultiplier
	if mult < 1.0 {
		mult = 2.0
	}

	var lastErr error
	for i := 1; i <= attempts; i++ {
		// derive per-attempt timeout
		ctx, cancel := context.WithTimeout(parentCtx, h.requestTimeout)
		err := fn(ctx)
		cancel()

		if err == nil {
			return nil
		}

		// if parent context cancelled or deadline exceeded, stop early
		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			return err
		}

		lastErr = err
		if i == attempts {
			break
		}

		// sleep with capped exponential backoff, also respect parent context
		sleep := time.Duration(math.Min(float64(backoff), float64(maxBackoff)))
		timer := time.NewTimer(sleep)
		select {
		case <-parentCtx.Done():
			timer.Stop()
			return parentCtx.Err()
		case <-timer.C:
		}

		// increase backoff
		next := time.Duration(float64(backoff) * mult)
		if next > maxBackoff {
			next = maxBackoff
		}
		backoff = next
	}
	return lastErr
}

// max helper
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func (h *DefaultBusinessHandler) logPayment(ctx context.Context, req PaymentRequest, status, message string) {
	paidAmount, _ := strconv.ParseFloat(req.PaidAmount, 64)
	totalAmount, _ := strconv.ParseFloat(req.TotalAmount, 64)

	log := &PaymentLog{
		RequestID:      req.RequestID,
		CompanyCode:    req.CompanyCode,
		CustomerNumber: req.CustomerNumber,
		ChannelType:    req.ChannelType,
		PaidAmount:     paidAmount,
		TotalAmount:    totalAmount,
		Status:         status,
		Response:       message,
		Reference:      req.Reference,
		CreatedAt:      time.Now(),
		IPAddress:      "", // Extract from context if available
	}
	_ = h.doWithRetry(ctx, "CreatePaymentLog", func(ctx context.Context) error {
		return h.db.CreatePaymentLog(ctx, log)
	})
}
