package webhook

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"golang.org/x/time/rate"
)

// SimpleWebhookService manages webhook subscriptions and deliveries without storage dependency
type SimpleWebhookService struct {
	endpoints    map[string]*WebhookEndpoint
	httpClient   HTTPClient
	rateLimiters map[string]RateLimiter
	mu           sync.RWMutex
}

// SimpleOption defines a function type for configuring SimpleWebhookService
type SimpleOption func(*SimpleWebhookService)

// WithSimpleHTTPClient sets a custom HTTP client
func WithSimpleHTTPClient(client HTTPClient) SimpleOption {
	return func(s *SimpleWebhookService) {
		s.httpClient = client
	}
}

// WithSimpleDefaultHTTPClient sets a default HTTP client with configuration
func WithSimpleDefaultHTTPClient(config HTTPClientConfig) SimpleOption {
	return func(s *SimpleWebhookService) {
		s.httpClient = &http.Client{
			Timeout: config.Timeout,
			Transport: &http.Transport{
				MaxIdleConns:      config.MaxIdleConns,
				IdleConnTimeout:   config.IdleConnTimeout,
				DisableKeepAlives: config.DisableKeepAlives,
			},
		}
	}
}

// NewSimpleWebhookService creates a new SimpleWebhookService with options
func NewSimpleWebhookService(opts ...SimpleOption) *SimpleWebhookService {
	service := &SimpleWebhookService{
		endpoints:    make(map[string]*WebhookEndpoint),
		rateLimiters: make(map[string]RateLimiter),
	}

	for _, opt := range opts {
		opt(service)
	}

	if service.httpClient == nil {
		service.httpClient = &http.Client{
			Timeout: 30 * time.Second,
		}
	}

	return service
}

// RegisterEndpoint registers a new webhook endpoint
func (s *SimpleWebhookService) RegisterEndpoint(endpoint *WebhookEndpoint) error {
	if endpoint.ID == "" || endpoint.URL == "" {
		return errors.New("endpoint ID and URL are required")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.endpoints[endpoint.ID] = endpoint

	if endpoint.RateLimit.RequestsPerSecond > 0 {
		s.rateLimiters[endpoint.ID] = rate.NewLimiter(
			rate.Limit(endpoint.RateLimit.RequestsPerSecond),
			endpoint.RateLimit.Burst,
		)
	}

	return nil
}

// SimpleDeliveryConfig holds optional configuration for DeliverWebhook
type SimpleDeliveryConfig struct {
	Headers map[string]string
	Auth    Authentication
}

// SimpleDeliveryOption defines a function type for configuring DeliverWebhook
type SimpleDeliveryOption func(*SimpleDeliveryConfig)

// WithSimpleHeaders sets custom headers for the request
func WithSimpleHeaders(headers map[string]string) SimpleDeliveryOption {
	return func(c *SimpleDeliveryConfig) {
		c.Headers = headers
	}
}

// WithSimpleAuth sets authentication for the request
func WithSimpleAuth(auth Authentication) SimpleDeliveryOption {
	return func(c *SimpleDeliveryConfig) {
		c.Auth = auth
	}
}

// DeliverWebhook sends a payload to a webhook endpoint
func (s *SimpleWebhookService) DeliverWebhook(ctx context.Context, endpointIDOrURL string, payload []byte, opts ...SimpleDeliveryOption) (*WebhookDeliveryResponse, error) {
	startTime := time.Now()

	config := &SimpleDeliveryConfig{
		Headers: make(map[string]string),
	}

	for _, opt := range opts {
		opt(config)
	}

	endpoint, err := s.getEndpoint(endpointIDOrURL, config.Headers, config.Auth)
	if err != nil {
		return nil, err
	}

	if err := s.applyRateLimit(endpoint.ID); err != nil {
		return nil, err
	}

	deliveryID := uuid.New().String()
	resp, err := s.sendRequest(ctx, endpoint, payload, deliveryID)
	if err != nil {
		return nil, fmt.Errorf("failed to send webhook: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return &WebhookDeliveryResponse{
		ID:          deliveryID,
		StatusCode:  resp.StatusCode,
		Duration:    time.Since(startTime),
		Body:        body,
		Response:    resp,
		Headers:     resp.Header,
		EndpointID:  endpoint.ID,
		EndpointURL: endpoint.URL,
	}, nil
}

// getEndpoint retrieves or creates an endpoint
func (s *SimpleWebhookService) getEndpoint(endpointIDOrURL string, headers map[string]string, auth Authentication) (*WebhookEndpoint, error) {
	if strings.HasPrefix(endpointIDOrURL, "http://") || strings.HasPrefix(endpointIDOrURL, "https://") {
		return &WebhookEndpoint{
			ID:  uuid.New().String(),
			URL: endpointIDOrURL,
			RateLimit: RateLimit{
				RequestsPerSecond: 10,
				Burst:             5,
			},
			Headers:        headers,
			Authentication: auth,
		}, nil
	}

	s.mu.RLock()
	endpoint, exists := s.endpoints[endpointIDOrURL]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("endpoint not found: %s", endpointIDOrURL)
	}

	return endpoint, nil
}

// applyRateLimit applies rate limiting for the endpoint
func (s *SimpleWebhookService) applyRateLimit(endpointID string) error {
	s.mu.RLock()
	limiter, exists := s.rateLimiters[endpointID]
	s.mu.RUnlock()

	if !exists {
		return nil
	}

	if !limiter.Allow() {
		return errors.New("rate limit exceeded")
	}

	return nil
}

// sendRequest sends the HTTP request
func (s *SimpleWebhookService) sendRequest(ctx context.Context, endpoint *WebhookEndpoint, payload []byte, deliveryID string) (*http.Response, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint.URL, bytes.NewReader(payload))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Webhook-Delivery", deliveryID)

	for k, v := range endpoint.Headers {
		req.Header.Set(k, v)
	}

	if endpoint.Authentication != nil {
		if err := endpoint.Authentication.Apply(req); err != nil {
			return nil, fmt.Errorf("failed to apply authentication: %w", err)
		}
	}

	if endpoint.SignatureConfig.Secret != "" {
		verifier := NewSignatureVerifier(endpoint.SignatureConfig)
		timestamp := time.Now().Unix()

		signature, digest := verifier.GenerateSignature(payload, timestamp, "application/json")
		h := verifier.BuildHeaders(timestamp, signature, digest)

		for k, v := range h {
			req.Header.Set(k, v)
		}
	}

	return s.httpClient.Do(req)
}

func (s *SimpleWebhookService) GetEndpoint(endpointID string) (*WebhookEndpoint, error) {
	return s.getEndpoint(endpointID, nil, nil)
}

// DeleteEndpoint deletes an endpoint
func (s *SimpleWebhookService) DeleteEndpoint(endpointID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	delete(s.endpoints, endpointID)
	delete(s.rateLimiters, endpointID)

	return nil
}

// Close clears all endpoints and rate limiters
func (s *SimpleWebhookService) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.endpoints = make(map[string]*WebhookEndpoint)
	s.rateLimiters = make(map[string]RateLimiter)

	return nil
}
