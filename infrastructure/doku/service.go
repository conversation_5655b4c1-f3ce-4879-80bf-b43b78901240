package doku

import (
	"context"
)

type RestDoku interface {
	Config() Config

	VAPaymentCode(ctx context.Context, credential Credential, bankChannel string, req VAPaymentCodeReq) (rawResp []byte, resp VAPaymentCodeRes, err error)
	OrderStatus(ctx context.Context, credential Credential, invoiceNumber string) (rawResp []byte, resp OrderStatusRes, err error)

	VerifyCallbackSignature(ctx context.Context, comp SignatureComponent, signature string) error
}
