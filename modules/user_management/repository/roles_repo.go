package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
)

func (r *defaultUserManagement) CreateRole(ctx context.Context, req *domain.Roles, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultUserManagement) UpdateRole(ctx context.Context, req *domain.Roles) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultUserManagement) GetRoleById(ctx context.Context, id string) (resp *domain.Roles, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultUserManagement) DeleteRole(ctx context.Context, req *domain.Roles) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.Where("role_id = ?", req.ID).Delete(&domain.Users{}).Error
	if err != nil {
		return
	}
	err = tx.Where("role_id = ?", req.ID).Delete(&domain.RolePermissions{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultUserManagement) GetListRole(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error) {
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Model(domain.Roles{}).
		Order(paginate.GetOrderBy("")).Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultUserManagement) GetListRoleAll(ctx context.Context) (resp *[]domain.Roles, err error) {
	err = r.db.WithContext(ctx).Model(domain.Roles{}).Order("created_at DESC").Find(&resp).Error
	return
}

func (r *defaultUserManagement) GetListRoleAllStatusTrue(ctx context.Context) (resp *[]domain.Roles, err error) {
	err = r.db.WithContext(ctx).Model(domain.Roles{}).Where("status = true").Order("created_at DESC").Find(&resp).Error
	return
}

func (r *defaultUserManagement) GetListRoleAllStatusFalse(ctx context.Context) (resp *[]domain.Roles, err error) {
	err = r.db.WithContext(ctx).Model(domain.Roles{}).Where("status = false").Order("created_at DESC").Find(&resp).Error
	return
}

func (r *defaultUserManagement) SearchRole(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error) {
	query := func(db *gorm.DB) *gorm.DB {
		condition := db
		if paginate.Value != "" {
			searchValue := "%" + paginate.Value + "%"
			switch paginate.Key {
			case "role":
				condition.Where("\"name\" ILIKE ?", searchValue)
			case "role_code":
				condition.Where("code ILIKE ?", searchValue)
			case "description":
				condition.Where("description ILIKE ?", searchValue)
			case "status":
				condition.Where("status = ?", paginate.Value)
			default:
				condition.Where("CONCAT(\"name\", code) ILIKE ?", searchValue)
			}
		}
		return condition
	}
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Model(domain.Roles{}).Scopes(query).
		Order("created_at DESC").Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Model(domain.Roles{}).Scopes(query).Count(&totalData).Error
	return
}
