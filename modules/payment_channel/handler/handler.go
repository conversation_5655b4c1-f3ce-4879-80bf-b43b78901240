package handler

import (
	"net/http"
	"net/url"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

type PaymentChannelHandler struct {
	paymentChannelService usecase.PaymentChannelUsecase
}

func NewHandler(companyProductService usecase.PaymentChannelUsecase) *PaymentChannelHandler {
	return &PaymentChannelHandler{companyProductService}
}

func (h *PaymentChannelHandler) CreatePaymentChannel(c *fiber.Ctx) (err error) {
	var req domain.PaymentChannels
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.paymentChannelService.CreatePaymentChannel(c.UserContext(), &req)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *PaymentChannelHandler) UpdatePaymentChannel(c *fiber.Ctx) (err error) {
	var req domain.PaymentChannels
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.paymentChannelService.UpdatePaymentChannel(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}
func (h *PaymentChannelHandler) UpdatePaymentChannelStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id := c.Params("id")
	req := new(request)

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = h.paymentChannelService.UpdatePaymentChannelStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}

func (h *PaymentChannelHandler) DeletePaymentChannel(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.paymentChannelService.DeletePaymentChannel(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *PaymentChannelHandler) SearchPaymentChannel(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return
	}

	paginate.Value, _ = url.QueryUnescape(paginate.Value)
	resp, totalData, err := h.paymentChannelService.SearchPaymentChannel(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *PaymentChannelHandler) GetPaymentChannelListActive(c *fiber.Ctx) (err error) {
	resp, _, err := h.paymentChannelService.GetPaymentChannelListActive(c.UserContext())
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PaymentChannelHandler) GetPaymentChannelById(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.paymentChannelService.GetPaymentChannelById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PaymentChannelHandler) GetPaymentChannelListAll(c *fiber.Ctx) (err error) {
	status := c.Query("status")
	resp, _, err := h.paymentChannelService.GetPaymentChannelListAll(c.UserContext(), status)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PaymentChannelHandler) GetListPaymentChannelType(c *fiber.Ctx) (err error) {
	resp, err := h.paymentChannelService.GetListPaymentChannelType(c.UserContext())
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}
