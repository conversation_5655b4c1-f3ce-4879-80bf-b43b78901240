package domain

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Companies struct {
	ID              int            `json:"id"`
	ParentCompanyID *int           `json:"parent_company_id"`
	Code            string         `json:"code" validate:"min=4,alphanum"`
	Name            string         `json:"name" validate:"required"`
	Email           string         `json:"email" validate:"required,email"`
	<PERSON><PERSON>           string         `json:"alias"`
	Secrets         string         `json:"secrets"`
	Webhooks        datatypes.JSON `json:"webhooks"`
	Status          bool           `json:"status"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type CompanyWebhook struct {
	EventType string `json:"event_type"`
	URL       string `json:"url"`
}

type WebhookData struct {
	URL string `json:"url"`
}
