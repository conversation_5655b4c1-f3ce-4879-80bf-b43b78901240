package domain

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

type PaymentChannelTypes struct {
	ID          int            `gorm:"primaryKey" json:"id"`
	PaymentType string         `json:"payment_type"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type PaymentChannelTypeConfigTemplate struct {
	Name string `json:"name"`
	Type string `json:"type"` // enum: string, boolean, integer
}

type PaymentChannelTypeConfigTemplates []PaymentChannelTypeConfigTemplate

func (a PaymentChannelTypeConfigTemplates) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *PaymentChannelTypeConfigTemplates) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		// return errors.New("type assertion to []byte failed")
		return nil
	}

	return json.Unmarshal(b, &a)
}
