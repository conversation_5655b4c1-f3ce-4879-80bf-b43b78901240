package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

// Definisikan struct untuk PgReference
type PgReference struct {
	ID   string `json:"id"`   // to provider xendit
	TXID string `json:"tXid"` // to provider nicepay
}

func (s *defaultCashinTransaction) CancelCashInTransaction(ctx context.Context, req *entity.CashInCancelationRequest, auth string) (err error) {
	ctx = logger.SetCtxData(ctx, map[string]interface{}{
		"provider_transcation_id": req.ProviderTransactionID,
		"invoice_number":          req.InvoiceNumber,
		"customer_name":           req.CustomerName,
		"total":                   req.Total,
	})

	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	cashIn, err := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, req.InvoiceNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.CompanyID != company.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid company")
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.Total != req.Total {
		err = errors.SetErrorMessage(http.StatusBadRequest, fmt.Sprintf("total %v is different from cash in total %v", req.Total, cashIn.Total))
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.Status != constants.CashInStatusPending {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("status %s is invalid for canceling", cashIn.Status))
		logger.Error(ctx, err.Error())
		return
	}

	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, strconv.Itoa(cashIn.CompanyID), strconv.Itoa(cashIn.PaymentProviderID))
	if err != nil {
		logger.Error(ctx, "payment provider not found in company", logger.Err(err))
		return
	}

	provider, err := s.providerService.GetPaymentProviderById(ctx, strconv.Itoa(cashIn.PaymentProviderID))
	if err != nil {
		return
	}

	switch true {
	case strings.Contains(strings.ToLower(provider.Name), constants.ProviderXendit):
		return s.cancelXenditAPI(ctx, cashIn, req, providerMapping.ProviderSecrets)
	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "Pembatalan transaksi saat ini belum dapat dilakukan. Silakan lanjutkan pembayaran atau tunggu hingga transaksi berakhir")
		logger.Error(ctx, err.Error())
		return
	}
}

func (s *defaultCashinTransaction) cancelXenditAPI(ctx context.Context, cashIn *domain.CashInTransactions, req *entity.CashInCancelationRequest, secret string) (err error) {
	tx := s.cashinRepo.BeginTrans()
	defer tx.Rollback()

	cashInHistory, err := s.cashinRepo.GetCashinTransactionHistoryByPaymentStatus(ctx, cashIn.ID, constants.PaymentDraft)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("cash in transaction history %s not found", req.ProviderTransactionID))
		logger.Error(ctx, err.Error())
		return
	}

	var pgRef PgReference
	if err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &pgRef); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "failed to parse PgReference Information")
		logger.Error(ctx, err.Error())
		return
	}

	if req.ProviderTransactionID != pgRef.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("invalid provider_transcation_id %s", req.ProviderTransactionID))
		logger.Error(ctx, err.Error())
		return
	}

	channel, err := s.channelService.GetPaymentChannelById(ctx, strconv.Itoa(cashIn.PaymentChannelID))
	if err != nil {
		return
	}
	channelType, err := s.channelService.GetPaymentChannelTypeById(ctx, strconv.Itoa(channel.PaymentChannelTypeId))
	if err != nil {
		return
	}

	logger.Info(ctx, channelType.PaymentType)

	// Log jenis pembayaran yang dibatalkan
	switch true {
	case channelType.PaymentType == constants.TrxEwallet:
		logger.Info(ctx, "cancel ewallet")
	case channelType.PaymentType == constants.TrxRetail:
		logger.Info(ctx, "cancel retail")
		if _, err = s.cancelXenditRetail(ctx, secret, cashIn, req); err != nil {
			return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		}
	case channelType.PaymentType == constants.TrxVirtualAccount:
		logger.Info(ctx, "cancel virtual account")
		if _, err = s.cancelXenditVA(ctx, secret, cashIn, req); err != nil {
			return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		}
	default:
		return errors.SetErrorMessage(http.StatusBadRequest, "payment channel type not found")
	}

	// Buat riwayat transaksi
	history := &domain.CashInTransactionHistories{
		Description:            fmt.Sprintf("Cancel Payment By it %s", req.CustomerName),
		Status:                 constants.CashInStatusFailed,
		PaymentStatus:          constants.PaymentFailed,
		ChannelId:              cashIn.PaymentChannelID,
		ProviderId:             cashIn.PaymentProviderID,
		CashInTransactionID:    cashIn.ID,
		InvoiceNumber:          cashIn.InvoiceNumber,
		PgReferenceInformation: "{}",
	}

	if err = s.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	// Update status item cashin
	cashInItems, err := s.cashinRepo.GetCashinItems(ctx, cashIn.ID, false)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	for _, item := range *cashInItems {
		item.Status = constants.CashInItemStatusFailed
		if err = s.cashinRepo.UpdateStatusCashinTransactionItemsByCashinTranscationId(ctx, cashIn.ID, item.Status); err != nil {
			return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		}
	}

	// Update status cashin
	cashIn.Status = constants.CashInStatusFailed
	cashIn.PaymentStatus = constants.PaymentFailed
	cashIn.UpdatedAt = time.Now()

	if err = s.cashinRepo.UpdateCashinTransaction(ctx, tx, cashIn); err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	tx.Commit()
	return
}

func (s *defaultCashinTransaction) cancelXenditVA(ctx context.Context, secret string, cashIn *domain.CashInTransactions, req *entity.CashInCancelationRequest) (entity.CashinTranscationResponse, error) {
	credential, err := xendit.GetXenditCredential(secret)
	if err != nil {
		logger.Error(ctx, err.Error())
		return entity.CashinTranscationResponse{}, errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	// update to backdate to expired it
	_, err = s.xenditRest.UpdatePaymentVA(ctx, xendit.UpdateVARequest{
		ID:             req.ProviderTransactionID,
		ExternalId:     cashIn.InvoiceNumber,
		ExpirationDate: time.Now().AddDate(0, 0, -1).Format(time.RFC3339),
		ExpectedAmount: cashIn.Total,
	}, credential)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return entity.CashinTranscationResponse{}, err
	}
	return entity.CashinTranscationResponse{}, nil
}

func (s *defaultCashinTransaction) cancelXenditRetail(ctx context.Context, secret string, cashIn *domain.CashInTransactions, req *entity.CashInCancelationRequest) (entity.CashinTranscationResponse, error) {
	credential, err := xendit.GetXenditCredential(secret)
	if err != nil {
		logger.Error(ctx, err.Error())
		return entity.CashinTranscationResponse{}, errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	// update to backdate to expired it
	_, err = s.xenditRest.UpdateFixedPaymentCode(ctx, xendit.UpdateFixedPaymentCodeRequest{
		ID:             req.ProviderTransactionID,
		Name:           cashIn.InvoiceNumber,
		ExpirationDate: time.Now().AddDate(0, 0, -1).Format(time.RFC3339),
		ExpectedAmount: cashIn.Total,
	}, credential)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return entity.CashinTranscationResponse{}, err
	}
	return entity.CashinTranscationResponse{}, nil
}

func (s *defaultCashinTransaction) SwitchCashinPaymentChannel(ctx context.Context, req *entity.CashInChangeChannelRequest, auth string) (resp entity.CashinTranscationResponse, err error) {
	// tx := s.cashinRepo.BeginTrans()
	// defer tx.Rollback()

	ctx = logger.SetCtxData(ctx, map[string]interface{}{
		"provider_transcation_id": req.ProviderTransactionID,
		"last_invoice_number":     req.LastInvoiceNumber,
		"new_invoice_number":      req.NewInvoiceNumber,
	})

	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	cashIn, err := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, req.LastInvoiceNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("last cash in transaction %s not found", req.LastInvoiceNumber))
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.CompanyID != company.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid company")
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.Total != req.Total {
		err = errors.SetErrorMessage(http.StatusBadRequest, fmt.Sprintf("total %v is different from cash in total %v", req.Total, cashIn.Total))
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.Status != constants.CashInStatusPending {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("status %s is invalid for canceling", cashIn.Status))
		logger.Error(ctx, err.Error())
		return
	}

	channelPayment, err := s.channelMappingRepo.GetChannelMappingByCode(ctx, req.PaymentMethodCode)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("payment method %s not found", req.PaymentMethodCode))
		logger.Error(ctx, err.Error())
		return
	}

	if channelPayment.Capability != constants.CashInCapability {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment method does not support cash in transactions")
		logger.Error(ctx, err.Error())
		return
	}

	channel, err := s.channelService.GetPaymentChannelById(ctx, strconv.Itoa(int(channelPayment.PaymentChannelID)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("payment channel %d not found", channelPayment.PaymentChannelID))
		logger.Error(ctx, err.Error())
		return
	}

	product, err := s.companyProductService.GetCompanyProductByCodeAndCompanyId(ctx, req.CompanyProductCode, strconv.Itoa(company.ID))
	if err != nil {
		return
	}

	if !product.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, fmt.Sprintf("product %s is not active", req.CompanyProductCode))
		logger.Error(ctx, err.Error())
		return
	}

	// switch from e-wallet to other payment channel not supported yet
	if cashIn.PaymentChannelType == constants.TrxEwallet && channel.PaymentChannelTypeName != constants.TrxEwallet {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Perubahan dari e-wallet belum tersedia. Silakan selesaikan transaksi terlebih dahulu")
		logger.Error(ctx, err.Error())
		return
	}

	// switch from virtual account to other e-wallet not supported yet
	if cashIn.PaymentChannelType != constants.TrxVirtualAccount && channel.PaymentChannelTypeName == constants.TrxEwallet {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Perubahan ke e-wallet belum tersedia. Silakan selesaikan transaksi terlebih dahulu")
		logger.Error(ctx, err.Error())
		return
	}

	// get last use provider
	lastProvider, err := s.providerService.GetPaymentProviderById(ctx, strconv.Itoa(cashIn.PaymentProviderID))
	if err != nil {
		return
	}
	// get switch new  provider
	newProvider, err := s.providerService.GetPaymentProviderById(ctx, strconv.Itoa(int(channelPayment.PaymentProviderID)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("payment provider %d not found", channelPayment.PaymentProviderID))
		logger.Error(ctx, err.Error())
		return
	}

	if lastProvider.Name != newProvider.Name {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Pembatalan transaksi saat ini belum dapat dilakukan. Silakan lanjutkan pembayaran atau tunggu hingga transaksi berakhir")
		logger.Error(ctx, err.Error())
		return
	}

	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, strconv.Itoa(company.ID), strconv.Itoa(cashIn.PaymentProviderID))
	if err != nil {
		logger.Error(ctx, "payment provider not found in company", logger.Err(err))
		return
	}

	switch true {
	case strings.Contains(strings.ToLower(newProvider.Name), constants.ProviderXendit):
		return s.switchXenditAPI(ctx, req, cashIn, providerMapping.ProviderSecrets, auth)
	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "Pembatalan transaksi saat ini belum dapat dilakukan. Silakan lanjutkan pembayaran atau tunggu hingga transaksi berakhir")
		logger.Error(ctx, err.Error())
		return
	}
}

func (s *defaultCashinTransaction) switchXenditAPI(ctx context.Context, req *entity.CashInChangeChannelRequest, cashIn *domain.CashInTransactions, secret string, auth string) (resp entity.CashinTranscationResponse, err error) {
	tx := s.cashinRepo.BeginTrans()
	defer tx.Rollback()

	//buid request cancel payment
	reqCancel := entity.CashInCancelationRequest{
		ProviderTransactionID: req.ProviderTransactionID,
		InvoiceNumber:         req.LastInvoiceNumber,
		Total:                 cashIn.Total,
		CustomerName:          req.CustomerName,
	}

	cashInHistory, err := s.cashinRepo.GetCashinTransactionHistoryByPaymentStatus(ctx, cashIn.ID, constants.PaymentDraft)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("cash in transaction history %s not found", req.ProviderTransactionID))
		logger.Error(ctx, err.Error())
		return
	}

	var pgRef PgReference
	if err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &pgRef); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "failed to parse PgReference Information")
		logger.Error(ctx, err.Error())
		return
	}

	if req.ProviderTransactionID != pgRef.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("invalid provider_transcation_id %s", req.ProviderTransactionID))
		logger.Error(ctx, err.Error())
		return
	}

	err = s.cancelXenditAPI(ctx, cashIn, &reqCancel, secret)
	if err != nil {
		tx.Rollback() // Rollback jika ada error
		return
	}

	items := []entity.TransactionItems{}
	cashInItems, err := s.cashinRepo.GetCashinItems(ctx, cashIn.ID, false)
	if err != nil {
		return
	}

	for _, item := range *cashInItems {
		partner, errRes := s.partnerService.GetPartnerById(ctx, strconv.Itoa(item.PartnerId))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
			logger.Error(ctx, err.Error())
			return
		}
		items = append(items, entity.TransactionItems{
			RefInvoiceNumber: item.RefInvoiceNumber,
			PartnerCode:      partner.Code,
			ItemName:         item.ItemName,
			Amount:           item.Amount,
			Note:             item.Note,
		})
	}

	// build new payment
	newRequestPayment := entity.CashinRequest{
		CompanyProductCode: req.CompanyProductCode,
		InvoiceNumber:      req.NewInvoiceNumber,
		Total:              cashIn.Total,
		CustomerEmail:      req.CustomerEmail,
		CustomerName:       req.CustomerName,
		CustomerPhone:      req.CustomerPhone,
		MobileNumber:       req.MobileNumber,
		Description:        req.Description,
		SuccessRedirectUrl: req.SuccessRedirectUrl,
		FailureRedirectUrl: req.FailureRedirectUrl,
		PaymentMethodCode:  req.PaymentMethodCode,
		Discount:           cashIn.Discount,
		Voucher:            cashIn.Voucher,
		Items:              items,
		AdditionalInfo:     req.AdditionalInfo,
	}

	// activate new payment channel
	resp, err = s.CreateCashinTranscation(ctx, newRequestPayment, auth)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		tx.Rollback() // Rollback jika ada error
		return resp, err
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}
