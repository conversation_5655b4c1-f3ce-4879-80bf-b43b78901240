package usecase

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/user_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

type UserManagementRepository interface {
	BeginTrans() *gorm.DB
	CreateUserMenu(ctx context.Context, req *domain.Menus) (err error)
	UpdateUserMenu(ctx context.Context, req *domain.Menus) (err error)
	GetUserMenuById(ctx context.Context, id string) (resp *domain.Menus, err error)
	DeleteUserMenu(ctx context.Context, req *domain.Menus) (err error)
	CreateRolePermission(ctx context.Context, req *domain.RolePermissions, tx *gorm.DB) (err error)
	UpdateRolePermission(ctx context.Context, req *domain.RolePermissions, tx *gorm.DB) (err error)
	GetRolePermissionById(ctx context.Context, id string) (resp *domain.RolePermissions, err error)
	DeleteRolePermission(ctx context.Context, req *domain.RolePermissions) (err error)
	CreateUser(ctx context.Context, req *domain.Users, tx *gorm.DB) (err error)
	UpdateUser(ctx context.Context, req *domain.Users, tx *gorm.DB) (err error)
	GetUserById(ctx context.Context, id int) (resp *domain.Users, err error)
	DeleteUser(ctx context.Context, req *domain.Users) (err error)
	SearchUserList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.UserList, totalData int64, err error)
	CreateUserCompany(ctx context.Context, req *domain.UserCompanies, tx *gorm.DB) (err error)
	UpdateUserCompany(ctx context.Context, req *domain.UserCompanies, tx *gorm.DB) (err error)
	GetUserCompanyById(ctx context.Context, id string) (resp *domain.UserCompanies, err error)
	DeleteUserCompanyByUserId(ctx context.Context, id int, tx *gorm.DB) (err error)
	DeleteUserCompany(ctx context.Context, req *domain.UserCompanies) (err error)
	CreateRole(ctx context.Context, req *domain.Roles, tx *gorm.DB) (err error)
	UpdateRole(ctx context.Context, req *domain.Roles) (err error)
	GetRoleById(ctx context.Context, id string) (resp *domain.Roles, err error)
	DeleteRole(ctx context.Context, req *domain.Roles) (err error)
	GetUserByEmail(ctx context.Context, email string) (resp *domain.Users, err error)
	GetRolePermissionByRoleId(ctx context.Context, id string) (resp *[]domain.RolePermissions, err error)
	GetListUserMenuAll(ctx context.Context) (resp *[]domain.Menus, err error)
	GetRolePermissionByMenuId(ctx context.Context, id string) (resp *domain.RolePermissions, err error)
	GetUserCompanyByUserId(ctx context.Context, id string) (resp *[]domain.UserCompanies, err error)
	GetListRole(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error)
	GetListRoleAll(ctx context.Context) (resp *[]domain.Roles, err error)
	GetListRoleAllStatusTrue(ctx context.Context) (resp *[]domain.Roles, err error)
	GetListRoleAllStatusFalse(ctx context.Context) (resp *[]domain.Roles, err error)
	SearchRole(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error)
}

type UserManagementUsecase interface {
	CreateUser(ctx context.Context, req *entity.UsersRequest) (err error)
	UpdateUser(ctx context.Context, req *entity.UsersRequest) (err error)
	DeleteUser(ctx context.Context, id int) (err error)
	GetUserByIdShow(ctx context.Context, id string) (resp *entity.UsersResponse, err error)
	GetUserById(ctx context.Context, id string) (resp *domain.Users, err error)
	UserLogin(ctx context.Context, req string) (resp model.JwtTokenShow, err error)
	GetListMenuMappingUserPermissionByRoleId(ctx context.Context, roleId string) (resp []entity.MenuMappingPermission, err error)
	UpdateListMenuMappingUserPermissionByRoleId(ctx context.Context, req []entity.MenuMappingPermission, roleId string) (err error)
	CreateRolePermission(ctx context.Context, req *domain.RolePermissions) (err error)
	CreateUserMenu(ctx context.Context, req *domain.Menus) (err error)
	UpdateUserMenu(ctx context.Context, req *domain.Menus, id string) (err error)
	GetUserMenuById(ctx context.Context, id string) (resp *domain.Menus, err error)
	DeleteUserMenuById(ctx context.Context, id string) (err error)
	CreateRole(ctx context.Context, req *domain.Roles) (err error)
	UpdateRole(ctx context.Context, req *domain.Roles, id string) (err error)
	GetRoleById(ctx context.Context, id string) (resp *domain.Roles, err error)
	DeleteRole(ctx context.Context, id string) (err error)
	SearchRoles(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error)
	GetListAllRole(ctx context.Context, status string) (resp *[]domain.Roles, err error)
	SearchUserList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.UserList, totalData int64, err error)
	UpdateStatus(ctx context.Context, id int, status bool) (err error)
	UpdateStatusRole(ctx context.Context, id int, status bool) (err error)
}
