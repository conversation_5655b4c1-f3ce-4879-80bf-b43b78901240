package domain

import (
	"time"

	"gorm.io/gorm"
)

type CashInTransactionHistories struct {
	ID                     int            `gorm:"primaryKey" json:"id"`
	CashInTransactionID    int            `json:"cashin_transcation_id"`
	Description            string         `json:"description"`
	PaymentStatus          string         `json:"payment_status"`
	Status                 string         `json:"status"`
	VirtualAccount         string         `json:"virtual_account"`
	InvoiceNumber          string         `json:"invoice_number"`
	ProviderId             int            `json:"provider_id"`
	ChannelId              int            `json:"channel_id"`
	PgReferenceInformation string         `json:"pg_reference_information"`
	CreatedAt              time.Time      `json:"created_at"`
	UpdatedAt              time.Time      `json:"updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
