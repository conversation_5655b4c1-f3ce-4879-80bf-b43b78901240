package emailclient

import (
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

type wrapper struct {
	client email.EmailProvider
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Setup() IEmailClient {
	var (
		domain        = config.GetString("mailgun.domain")
		apikey        = config.GetString("mailgun.api_key")
		webhookApiKey = config.GetString("mailgun.webhook")
	)

	mg := email.NewMailgunEmailProvider(domain, apikey, webhookApiKey)

	w.client = mg

	return w
}
