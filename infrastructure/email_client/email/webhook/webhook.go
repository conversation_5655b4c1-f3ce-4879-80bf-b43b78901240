package webhook

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/mailgun/mailgun-go/v4"
	"github.com/mailgun/mailgun-go/v4/events"
)

type EmailDeliveryStatus string

const (
	EmailDeliveryStatusDelivered EmailDeliveryStatus = "sent"
	EmailDeliveryStatusAccepted  EmailDeliveryStatus = "pending"
	EmailDeliveryStatusOpened    EmailDeliveryStatus = "opened"
	EmailDeliveryStatusClicked   EmailDeliveryStatus = "clicked"
	EmailDeliveryStatusFailed    EmailDeliveryStatus = "failed"
	EmailDeliveryStatusRejected  EmailDeliveryStatus = "rejected"
)

type WebhookResponse struct {
	Email      string
	Signature  string
	TaskID     string
	Status     EmailDeliveryStatus
	ErrorMsg   string
	Parameters []byte
	event      mailgun.Event
}

func (w *WebhookResponse) isStatusWithErrorMessage(keywords ...string) bool {
	if w.ErrorMsg != "" && (w.Status == EmailDeliveryStatusRejected || w.Status == EmailDeliveryStatusFailed) {
		lowerErrMsg := strings.ToLower(w.ErrorMsg)
		for _, keyword := range keywords {
			if strings.Contains(lowerErrMsg, keyword) {
				return true
			}
		}
	}
	return false
}

func (w *WebhookResponse) IsEmailBounce() bool {
	return w.isStatusWithErrorMessage("bounce")
}

func (w *WebhookResponse) IsEmailBlocked() bool {
	return w.isStatusWithErrorMessage("espblock")
}

func NewWebhook(e mailgun.Event) (*WebhookResponse, error) {
	webhook := &WebhookResponse{event: e}
	if err := webhook.SetStatus(); err != nil {
		return nil, err
	}
	return webhook, nil
}

func (w *WebhookResponse) parseUserVariables(p interface{}) ([]byte, error) {
	userVars, ok := p.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("user variables should be a map of string keys to interface{} values")
	}

	paramVal, exists := userVars["parameters"]
	if !exists {
		return nil, fmt.Errorf("parameters for email not found")
	}

	paramStr, ok := paramVal.(string)
	if !ok {
		return nil, fmt.Errorf("parameters field is not a string")
	}

	return json.RawMessage(paramStr), nil
}

func (w *WebhookResponse) setCommonFields(headers *events.MessageHeaders, recipient string, userVariables interface{}) error {
	var err error
	w.TaskID = headers.MessageID
	w.Email = recipient
	w.Parameters, err = w.parseUserVariables(userVariables)
	return err
}

func (w *WebhookResponse) SetStatus() error {
	var err error

	switch event := w.event.(type) {
	case *events.Accepted:
		w.Status = EmailDeliveryStatusAccepted
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	case *events.Delivered:
		w.Status = EmailDeliveryStatusDelivered
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	case *events.Opened:
		w.Status = EmailDeliveryStatusOpened
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	case *events.Clicked:
		w.Status = EmailDeliveryStatusClicked
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	case *events.Failed:
		w.Status = EmailDeliveryStatusFailed
		w.ErrorMsg = fmt.Sprintf("reason: %v, message: %v", event.Reason, event.DeliveryStatus.Message)
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	case *events.Rejected:
		w.Status = EmailDeliveryStatusRejected
		w.ErrorMsg = fmt.Sprintf("reason: %v, message: %v: flags: %v", event.Reject.Reason, event.Reject.Description, event.Flags)
		err = w.setCommonFields(&event.Message.Headers, event.Message.Headers.To, event.UserVariables)
	case *events.Complained:
		w.Status = EmailDeliveryStatusRejected
		w.ErrorMsg = fmt.Sprintf("reason: %v", event)
		err = w.setCommonFields(&event.Message.Headers, event.Recipient, event.UserVariables)
	}

	return err
}
