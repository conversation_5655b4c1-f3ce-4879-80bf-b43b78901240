package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
)

type customerFVARepo struct {
	DB *gorm.DB
}

func NewCustomerFVARepository(db *gorm.DB) usecase.CustomerFVARepository {
	return &customerFVARepo{db}
}

func (r customerFVARepo) Create(ctx context.Context, req *domain.FixedVACustomer) (err error) {
	gormData := entity.CustomerFVAGORM{}
	gormData.FromDomain(req)
	gormData.CreatedAt = time.Now()
	gormData.UpdatedAt = time.Now()
	err = r.DB.Debug().Create(&gormData).Error
	if err != nil {
		return
	}
	res := gormData.ToDomain()
	req.ID = res.ID
	return
}

func (r customerFVARepo) Update(ctx context.Context, tx *gorm.DB, req *domain.FixedVACustomer) error {
	gormData := entity.CustomerFVAGORM{}
	if tx == nil {
		tx = r.DB
	}
	return tx.Debug().Model(&gormData).Where("id = ?", req.ID).Updates(map[string]interface{}{
		"name":                 req.Name,
		"va_number":            req.VANumber,
		"last_pg_reference_id": req.LastPgReferenceId,
		"expired_at":           req.ExpiredAt,
		"updated_at":           time.Now(),
	}).Error
}

func (r customerFVARepo) GetOne(ctx context.Context, mappingId int64, phone string) (res domain.FixedVACustomer, err error) {
	gormData := entity.CustomerFVAGORM{}

	err = r.DB.Debug().Where("company_payment_provider_channel_mapping_id = ?", mappingId).
		Where("phone = ?", phone).Take(&gormData).Error
	res = gormData.ToDomain()
	return
}

func (r customerFVARepo) GetOneByVA(ctx context.Context, mappingId int64, vaNumber string) (res domain.FixedVACustomer, err error) {
	gormData := entity.CustomerFVAGORM{}

	err = r.DB.Debug().Where("company_payment_provider_channel_mapping_id = ?", mappingId).
		Where("va_number = ?", vaNumber).Take(&gormData).Error
	res = gormData.ToDomain()
	return
}
