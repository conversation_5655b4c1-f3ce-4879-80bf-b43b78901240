package doku

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"net/http"
	"net/url"

	"github.com/go-resty/resty/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

const (
	HEADER_CLIENT_ID         = "Client-Id"
	HEADER_REQUEST_ID        = "Request-Id"
	HEADER_REQUEST_TIMESTAMP = "Request-Timestamp"
	HEADER_REQUEST_TARGET    = "Request-Target"
	HEADER_DIGEST            = "Digest"
	SYMBOL_COLON             = ":"
	HEADER_SIGNATURE         = "Signature"

	HEADER_RESPONSE_TIMESTAMP = "Response-Timestamp"
)

type wrapper struct {
	Endpoint  string
	isSandbox bool
	// client    rest.RestClient
	client        *resty.Client
	config        Config
	vaBankApiPath map[string]string
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Config() Config {
	return w.config
}

func (w *wrapper) Setup() RestDoku {
	var (
		EndpointSandbox = config.GetString("doku.sandbox")
		Endpoint        = config.GetString("doku.host")
	)
	vaNotifURL, err := url.Parse(config.GetString(CONFIG_CALLBACK_URL_VA))
	if err != nil {
		panic("invalid callback va url")
	}

	w.vaBankApiPath = config.GetStringMapString(CONFIG_VA_CHANNEL_MAPPING_API_PATH)
	if w.vaBankApiPath == nil {
		panic("doku channel va api path is empty")
	}

	cfg := Config{
		VirtualAccountNotificationURL: *vaNotifURL,
	}
	w.config = cfg

	w.isSandbox = config.GetBool("doku.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	r := resty.New()
	r = r.SetTimeout(config.GetDuration("doku.timeout")).SetDebug(config.GetBool("doku.debug"))

	if config.GetBool("doku.skiptls") {
		r = r.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}
	if config.GetString("doku.proxy") != "" {
		r = r.SetProxy(config.GetString("doku.proxy"))
	} else {
		r = r.RemoveProxy()
	}

	w.client = r

	return w
}

// result must be pointer
func (w *wrapper) httpReqWrapper(ctx context.Context, hrw HttpReqWrapper, result interface{}) (hrsw HttpResWrapper, err error) {
	// pre request
	fullURL := w.Endpoint + hrw.Path

	// prepare http client
	r := w.client.R().
		SetHeaders(w.setHeaders(ctx, hrw.Credential, hrw.Method, hrw.Path, hrw.ReqBody))

	if hrw.ReqBody != nil {
		r.SetBody(hrw.ReqBody)
	}
	var rr *resty.Response
	logger.Info(ctx, "request", logger.Any("fullURL", fullURL), logger.Any("header", r.Header), logger.Any("requestBody", string(hrw.ReqBody)))
	switch hrw.Method {
	case http.MethodGet:
		rr, err = r.Get(fullURL)
	case http.MethodPost:
		rr, err = r.Post(fullURL)
	case http.MethodPut:
		rr, err = r.Put(fullURL)
	case http.MethodPatch:
		rr, err = r.Patch(fullURL)
	case http.MethodDelete:
		rr, err = r.Delete(fullURL)
	}
	if err != nil {
		return
	}
	logger.Info(ctx, "response", logger.Any("header", rr.Header()), logger.Int("statusCode", rr.StatusCode()), logger.Any("responseBody", string(rr.Body())))
	// post request
	hrsw.Header = make(map[string]string)
	for k := range rr.RawResponse.Header {
		hrsw.Header[k] = rr.RawResponse.Header.Get(k)
	}
	hrsw.RespBody = rr.Body()
	hrsw.StatusCode = rr.StatusCode()

	if hrsw.StatusCode < 200 || hrsw.StatusCode > 299 {
		var errResp ErrorResp
		_ = json.Unmarshal(rr.Body(), &errResp)
		err = errResp.Error
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	// unmarshal result
	_ = json.Unmarshal(rr.Body(), &result)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.Err(err), logger.String("responseBody", string(rr.Body())))
		return
	}

	sc := SignatureComponent{
		Credential:        hrw.Credential,
		Method:            hrw.Method,
		RequestTarget:     hrw.Path,
		RequestID:         rr.RawResponse.Header.Get(HEADER_REQUEST_ID),
		ResponseTimestamp: rr.RawResponse.Header.Get(HEADER_RESPONSE_TIMESTAMP),
		ResponseBody:      rr.Body(),
	}
	err = w.verifyResponseSignature(ctx, sc, rr.RawResponse.Header.Get(HEADER_SIGNATURE))
	if err != nil {
		logger.Error(ctx, "verifyResponseSignature", logger.Any("header", rr.RawResponse.Header), logger.Err(err))
		err = errors.SetErrorMessage(http.StatusForbidden, err.Error())
		return
	}
	return
}
