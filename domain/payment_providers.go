package domain

import (
	"time"

	"gorm.io/gorm"
)

type PaymentProviders struct {
	ID              int64          `gorm:"primaryKey" json:"id"`
	Code            string         `json:"code" validate:"min=4,alphanum"`
	Name            string         `json:"name"`
	Status          bool           `json:"status"`
	SecretTemplates string         `json:"secret_templates"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
