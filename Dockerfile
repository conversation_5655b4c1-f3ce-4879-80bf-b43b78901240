ARG BASE_IMAGE=golang:alpine
#ARG CONTAINER=busybox:musl
ARG CONTAINER=scratch

FROM ${BASE_IMAGE}

ARG APP_NAME=payment-service

RUN apk add --update git tzdata ca-certificates dumb-init && wget -O- -nv https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s
COPY netrc /root/.netrc
WORKDIR /src

COPY go.* ./
RUN go mod download -x

COPY . .
RUN golangci-lint run || exit 0
RUN CGO_ENABLED=0 go build -ldflags="-s -w" -o ${APP_NAME}
RUN addgroup -S groot && adduser -S groot -G groot # 100:101


FROM ${CONTAINER}
ARG APP_NAME=payment-service

WORKDIR /app

COPY --from=0 /src/${APP_NAME} .
COPY --from=0 /usr/bin/dumb-init /bin/dumb-init
COPY --from=0 /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=0 /etc/ssl/certs /etc/ssl/certs
COPY --from=0 /etc/passwd /etc/passwd
COPY config-for-deployment.json config.json

EXPOSE 6081

USER groot

#HEALTHCHECK --timeout=5s --start-period=10s CMD wget -nv --tries=1 --spider http://localhost:6081/v1/ || exit 1
ENTRYPOINT ["/bin/dumb-init", "--"]
CMD ["./payment-service"]
