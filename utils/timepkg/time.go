package timepkg

import "time"

const (
	TimeJakartaLocFormat = "Asia/Jakarta"
	TimeDateFormat       = "2006-01-02"
	TimeDateExportFormat = "02/01/2006 15:04:05"
)

var (
	WIB               = time.FixedZone("UTC+7", 7*60*60)
	TimeJakartaLoc, _ = time.LoadLocation("Asia/Jakarta")
)

func DateNow() time.Time {
	now := time.Now().In(WIB)
	dateNow := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, WIB)
	return dateNow
}

func UnixToTime(unixInt *int) *time.Time {
	if unixInt == nil || *unixInt <= 0 {
		return nil
	}

	t := time.Unix(int64(*unixInt), 0)
	return &t
}

func StringDateToTime(timeStr string) (resp time.Time, err error) {
	tm, err := time.Parse(TimeDateFormat, timeStr)
	if err != nil {
		return
	}

	loc, _ := time.LoadLocation(TimeJakartaLocFormat)
	resp = time.Date(tm.Year(), tm.Month(), tm.Day(), 0, 0, 0, 0, loc)
	return
}
