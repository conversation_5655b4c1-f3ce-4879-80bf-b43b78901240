package snapclient

type NicepayCred struct {
	Credential
	ChannelId string `json:"channel_id"`
}

type NicepaySnapNotifRequest struct {
	TXid                            string `form:"tXid"`
	MerchantToken                   string `form:"merchantToken"`
	ReferenceNo                     string `form:"referenceNo"`
	PayMethod                       string `form:"payMethod"`
	Amt                             string `form:"amt"`
	TransDt                         string `form:"transDt"`
	TransTm                         string `form:"transTm"`
	Currency                        string `form:"currency"`
	GoodsNm                         string `form:"goodsNm"`
	BillingNm                       string `form:"billingNm"`
	MatchCl                         string `form:"matchCl"`
	Status                          string `form:"status"`
	NicepayNotifAdditionalVARequest        // additional param for VA
}

type NicepayNotifAdditionalVARequest struct {
	BankCd       string `json:"bankCd"`
	VacctNo      string `json:"vacctNo"`
	VacctValidDt string `json:"vacctValidDt"`
	VacctValidTm string `json:"vacctValidTm"`
	DepositDt    string `json:"depositDt"`
	DepositTm    string `json:"depositTm"`
}

const (
	NicepayPayMethodCreditCard       string = "01"
	NicepayPayMethodVirtualAccount   string = "02"
	NicepayPayMethodConvenienceStore string = "03"
	NicepayPayMethodClickPay         string = "04"
	NicepayPayMethodEWallet          string = "05"
	NicepayPayMethodPayloan          string = "06"

	NicepayTranscStatusSuccess   = "00"
	NicepayTranscStatusInitiated = "01"
	NicepayTranscStatusPaying    = "02"
	NicepayTranscStatusPending   = "03"
	NicepayTranscStatusRefunded  = "04"
	NicepayTranscStatusCancelled = "05"
	NicepayTranscStatusFailed    = "06"
	NicepayTranscStatusNotFound  = "07"
)

type NicepayVaHistoryInfo struct {
	VacctNo          string `json:"vacctNo"`
	TotalAmount      Money  `json:"totalAmount"`
	HeaderExternalId string `json:"headerExternalId"`
	TrxID            string `json:"trxId"`
	TXidVA           string `json:"tXidVa"`
}
