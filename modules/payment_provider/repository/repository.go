package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/entity"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

type defaultPaymentProvider struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.PaymentProviderRepository {
	return &defaultPaymentProvider{db}
}

func (r *defaultPaymentProvider) CreatePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultPaymentProvider) UpdatePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderById(ctx context.Context, id string) (resp *domain.PaymentProviders, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultPaymentProvider) DeletePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("payment_provider_id = ?", req.ID).Delete(&domain.PaymentProviderChannelMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("payment_provider_id = ?", req.ID).Delete(&domain.CompanyPaymentProviderChannelMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("payment_provider_id = ?", req.ID).Delete(&domain.CompanyPaymentProviderMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderListAll(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentProviders{}).Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderListAllActive(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentProviders{}).Where("status = true").Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderListAllNotActive(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentProviders{}).Where("status = false").Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).
		Model(domain.PaymentProviders{}).Order(paginate.GetOrderBy("")).Count(&totalData).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Model(domain.PaymentProviders{}).Count(&totalData).Error
	return
}

func (r *defaultPaymentProvider) SearchPaymentProvider(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	// whereQuery := "\"name\" @@ to_tsquery(?)"
	whereQuery := `name ILIKE ? OR code ILIKE ?`
	searchValue := "%" + paginate.Value + "%"
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Model(domain.PaymentProviders{}).
		Where(whereQuery, searchValue, searchValue).Order(paginate.GetOrderBy("")).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Model(domain.PaymentProviders{}).Where(whereQuery, searchValue, searchValue).Count(&totalData).Error
	return
}

func (r *defaultPaymentProvider) SearchPaymentProviderWithChannelMapping(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	joinQuery := "JOIN payment_provider_channel_mappings m ON p.id = m.payment_provider_id JOIN payment_channels c ON m.payment_channel_id = c.id"
	whereQuery := "CONCAT(p.\"name\", ' ', c.\"name\") @@ to_tsquery(?) AND m.deleted_at IS NULL"

	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("payment_providers p").
		Select("p.id, p.\"name\", p.code, p.status, p.secret_templates").Joins(joinQuery).Where(whereQuery, paginate.Value).
		Order(paginate.GetOrderBy("p")).Group("p.id").Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Table("payment_providers p").Select("COUNT(DISTINCT(p.id))").Joins(joinQuery).
		Where(whereQuery, paginate.Value).Take(&totalData).Error
	return
}

func (r *defaultPaymentProvider) GetPaymentProviderListFilterCapability(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	joinQuery := "JOIN payment_provider_channel_mappings m ON p.id = m.payment_provider_id JOIN payment_channels c ON m.payment_channel_id = c.id"
	whereQuery := "m.capability = ? AND m.deleted_at IS NULL"
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("payment_providers p").
		Select("p.id, p.\"name\", p.code, p.status, p.secret_templates").Joins(joinQuery).Where(whereQuery, paginate.Key).
		Order(paginate.GetOrderBy("p")).Group("p.id").Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Table("payment_providers p").Select("COUNT(DISTINCT(p.id))").
		Joins(joinQuery).Where(whereQuery, paginate.Key).Take(&totalData).Error
	return
}

func (r *defaultPaymentProvider) SearchPaymentProviderFilterCabability(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error) {
	joinQuery := "JOIN payment_provider_channel_mappings m ON p.id = m.payment_provider_id JOIN payment_channels c ON m.payment_channel_id = c.id"
	whereQuery := "CONCAT(p.\"name\", ' ', c.\"name\") @@ to_tsquery(?) AND m.deleted_at IS NULL"

	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("payment_providers p").
		Select("p.id, p.\"name\", p.code, p.status, p.secret_templates").Joins(joinQuery).Where(whereQuery+" AND m.capability = ?", paginate.Value, paginate.Key).
		Order(paginate.GetOrderBy("p")).Group("p.id").Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Table("payment_providers p").Select("COUNT(DISTINCT(p.id))").Joins(joinQuery).
		Where(whereQuery+" AND m.capability = ?", paginate.Value, paginate.Key).Take(&totalData).Error
	return
}

func (r *defaultPaymentProvider) GetAvailableProviders(ctx context.Context) (res []string, err error) {
	return config.GetStringSlice("available_providers"), nil
}
