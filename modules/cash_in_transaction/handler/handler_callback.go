package handler

import (
	"encoding/json"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

func (h *CashinHandler) CallbackXfers(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	var req xfers.PaymentResponse
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	signature := c.Request().Header.Peek("Xfers-Signature")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}

	switch true {
	case req.Data.PaymentDataResp.PaymentMethodResp.ID != "":
		err = h.cashinUsecase.PushCallbackXfersCashin(ctx, string(signature), constants.TrxVirtualAccount, req)
		if err != nil {
			return
		}
		return response.HandleSuccess(c, req)

	default:
		var req interface{}
		err = c.BodyParser(&req)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			return errors.ErrorHandle(c, err)
		}

		err = h.cashoutUsecase.PushCallbackProviderCashOutTranscation(ctx, string(signature), req, constants.ProviderXfers)
		if err != nil {
			return
		}

		return response.HandleSuccess(c, req)
	}
}

func (h *CashinHandler) CallbackXenditVA(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	var req interface{}
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	signature := c.Request().Header.Peek("x-callback-token")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	err = h.cashinUsecase.PushCallbackXenditCashin(ctx, string(signature), req, constants.TrxVirtualAccount)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}

	reqByte, _ := json.Marshal(req)
	logger.Info(c.UserContext(), "CallbackXenditVA", logger.String("req", string(reqByte)))

	return response.HandleSuccess(c, req)
}

func (h *CashinHandler) CallbackXenditEwallet(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	var req interface{}
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	signature := c.Request().Header.Peek("x-callback-token")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	err = h.cashinUsecase.PushCallbackXenditCashin(ctx, string(signature), req, constants.TrxEwallet)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccess(c, req)
}

func (h *CashinHandler) CallbackXenditInvoice(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	var req interface{}
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	signature := c.Request().Header.Peek("x-callback-token")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	err = h.cashinUsecase.PushCallbackXenditCashin(ctx, string(signature), req, constants.TrxCc)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccess(c, req)
}

func (h *CashinHandler) CallbackXenditFixedPaymentCode(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	var req interface{}
	err = c.BodyParser(&req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	signature := c.Request().Header.Peek("x-callback-token")
	if string(signature) == "" {
		err = errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	err = h.cashinUsecase.PushCallbackXenditCashin(ctx, string(signature), req, constants.TrxRetail)
	if err != nil {
		return errors.ErrorHandle(c, err)
	}
	return response.HandleSuccess(c, req)
}

// CallbackNicePay
func (h *CashinHandler) CallbackNicePay(c *fiber.Ctx) error {
	ctx := logger.MustCorrelationId(c.UserContext())
	// example VA
	// merchantToken=1300bcbc5f881cf2988cc70f618b0eaa6cb25990e3e6e0e61d9005090bb3c873&goodsNm=Cust&referenceNo=INV20220813-0009-PRO8-CH31&transTm=203702&tXid=IONPAYTEST02********2036413955&amt=11000&vacctNo=111111102036413955&instmntType=1&billingNm=Cust&matchCl=1&vacctValidDt=********&payMethod=02&bankCd=CENA&currency=IDR&instmntMon=null&vacctValidTm=213643&transDt=********&status=0
	logger.Info(ctx, "CallbackNicePay", logger.String("body", string(c.Request().Body())))
	r := new(nicepay.NotificationRequest)

	if err := c.BodyParser(r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(r); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	err := h.cashinUsecase.PushCallbackNicePayCashin(c.UserContext(), r)
	if err != nil {
		return err
	}

	return response.HandleSuccess(c, r)
}

// CallbackOttocash
func (h *CashinHandler) CallbackOttocash(c *fiber.Ctx) error {
	ctx := logger.MustCorrelationId(c.UserContext())

	var r interface{}
	if err := c.BodyParser(&r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	// if err := utils.ValidateStruct(r); err != nil {
	// 	return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	// }

	signature := c.Request().Header.Peek("Signature")
	if string(signature) == "" {
		err := errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	timestamp := c.Request().Header.Peek("Timestamp")
	if string(timestamp) == "" {
		err := errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}

	err := h.cashinUsecase.PushCallbackOttocashCashin(ctx, string(signature), string(timestamp), r)
	if err != nil {
		return err
	}

	return response.HandleSuccess(c, r)
}

func (h *CashinHandler) CallbackDokuVA(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	reqByte := c.Request().Body()
	// reqHeader := &c.Request().Header
	reqHeader := c.Request().Header.Header()

	logger.Info(c.UserContext(), "CallbackDokuVA", logger.String("header", string(reqHeader)), logger.String("body", string(reqByte)))

	signature := c.Request().Header.Peek(doku.HEADER_SIGNATURE)
	if string(signature) == "" {
		err := errors.SetErrorMessage(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized))
		return errors.ErrorHandle(c, err)
	}
	// convert hedaer from fast http (fiber) to native go http.Header
	header := http.Header{}
	header.Add(doku.HEADER_CLIENT_ID, string(c.Request().Header.Peek(doku.HEADER_CLIENT_ID)))
	header.Add(doku.HEADER_REQUEST_ID, string(c.Request().Header.Peek(doku.HEADER_REQUEST_ID)))
	header.Add(doku.HEADER_REQUEST_TIMESTAMP, string(c.Request().Header.Peek(doku.HEADER_REQUEST_TIMESTAMP)))
	header.Add(doku.HEADER_SIGNATURE, string(c.Request().Header.Peek(doku.HEADER_SIGNATURE)))

	err = h.cashinUsecase.PushCallbackDokuCashin(ctx, header, reqByte, constants.TrxVirtualAccount)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, "ok")
}

func (h *CashinHandler) CallbackCashlezOfflinePg(c *fiber.Ctx) (err error) {
	ctx := logger.MustCorrelationId(c.UserContext())

	reqByte := c.Request().Body()
	reqHeader := c.Request().Header.Header()

	logger.Info(c.UserContext(), "CallbackCashlezOfflinePg", logger.String("header", string(reqHeader)), logger.String("body", string(reqByte)))

	header := http.Header{}
	header.Add("Authorization", string(c.Request().Header.Peek("Authorization")))
	err = h.cashinUsecase.PushCallbackCashlez(ctx, header, reqByte, constants.TrxOfflinePG)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, "ok")
}

func (h *CashinHandler) CallbackSnapNicePay(c *fiber.Ctx) error {
	ctx := logger.MustCorrelationId(c.UserContext())
	// example VA
	// merchantToken=1300bcbc5f881cf2988cc70f618b0eaa6cb25990e3e6e0e61d9005090bb3c873&goodsNm=Cust&referenceNo=INV20220813-0009-PRO8-CH31&transTm=203702&tXid=IONPAYTEST02********2036413955&amt=11000&vacctNo=111111102036413955&instmntType=1&billingNm=Cust&matchCl=1&vacctValidDt=********&payMethod=02&bankCd=CENA&currency=IDR&instmntMon=null&vacctValidTm=213643&transDt=********&status=0
	logger.Info(c.UserContext(), "CallbackSnapNicePay", logger.String("body", string(c.Request().Body())))
	r := new(snapclient.NicepaySnapNotifRequest)

	if err := c.BodyParser(r); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return errors.ErrorHandle(c, err)
	}
	if err := utils.ValidateStruct(r); err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return errors.ErrorHandle(c, err)
	}

	err := h.cashinUsecase.PushCallbackSnapNicePayCashin(ctx, r)
	if err != nil {
		return err
	}

	return response.HandleSuccess(c, r)
}
