package str

import (
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
)

// Contains ...
func Contains(slices []string, comparizon string) bool {
	for _, a := range slices {
		if a == comparizon {
			return true
		}
	}

	return false
}

func PhoneNormalization(data string) (res string) {
	res = data
	if len(data) < 2 {
		return
	}
	if data[0:2] == "62" {
		res = strings.Replace(data, "62", "0", 1)
	}

	return res
}

func ToFloat64(data string) float64 {
	res, _ := strconv.ParseFloat(data, 64)
	return res
}

func SplitToInts(str, sep string) (res []int) {
	if len(str) <= 0 {
		return
	}
	intsS := strings.Split(str, sep)
	for _, v := range intsS {
		i, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		res = append(res, i)
	}
	return
}

func SplitToInt64s(str, sep string) (res []int64) {
	if len(str) <= 0 {
		return
	}
	intsS := strings.Split(str, sep)
	for _, v := range intsS {
		i, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			continue
		}
		res = append(res, i)
	}
	return
}

type Base64str string

func (bs Base64str) Decode() string {
	dec, err := base64.StdEncoding.DecodeString(string(bs))
	if err != nil {
		fmt.Println("Base64str error:", err)
	}
	return string(dec)
}
