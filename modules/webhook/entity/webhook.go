package entity

import (
	"encoding/json"
	"log"
	"time"

	"gorm.io/datatypes"
)

type WebhookType string

const (
	CashInStatusWebhook  WebhookType = "cashin.status"
	CashOutStatusWebhook WebhookType = "cashout.status"
)

type WebhookURLs struct {
	CashInStatus  *WebhookURL `json:"cashin.status"`
	CashOutStatus *WebhookURL `json:"cashout.status"`
}

type WebhookURL struct {
	URL string `json:"url"`
}

func GetWebhookURLs(jsonData datatypes.JSON) (*WebhookURLs, error) {
	var webhookURLs WebhookURLs
	err := json.Unmarshal(jsonData, &webhookURLs)
	if err != nil {
		log.Printf("Error unmarshalling webhook URLs: %v", err)
		return nil, err
	}

	return &webhookURLs, nil
}

type WebhookRequest struct {
	CompanyId int64       `json:"company_id"`
	AuthKey   string      `json:"-"`
	EventType WebhookType `json:"type" validate:"required"`
	URL       string      `json:"url" validate:"required,url"`
}

type WebhookVerifyResponse struct {
	VerifyPayload
}

type Header struct {
	Authorization string `json:"authorization"`
	Signature     string `json:"signature"`
	ContentType   string `json:"content-type"`
	Timestamp     string `json:"timestamp"`
	RequestId     string `json:"request_id"`
}

type Response struct {
	ContentType string      `json:"content_type"`
	StatusCode  int         `json:"status_code"`
	Time        float64     `json:"time"`
	Payload     interface{} `json:"payload"`
}

type WebhookEventPayload interface {
	TypeName() string
}

type VerifyPayload struct {
	URL      string              `json:"url"`
	CashOut  *CashOutPayload     `json:"-"`
	CashIn   *CashInPayload      `json:"-"`
	Payload  WebhookEventPayload `json:"payload"`
	Headers  Header              `json:"headers"`
	Response Response            `json:"response"`
}

func BuildWebhookPayload(eventType WebhookType, url string) *VerifyPayload {
	switch eventType {
	case CashOutStatusWebhook:
		cashOut := defaultCashOutPayload(url)
		cashOut.Payload = cashOut.CashOut
		return cashOut
	case CashInStatusWebhook:
		cashIn := defaultCashInPayload(url)
		cashIn.Payload = cashIn.CashIn
		return cashIn
	default:
		return nil
	}
}

func (v VerifyPayload) IsCashOut() bool {
	return v.CashOut != nil
}

func (v VerifyPayload) IsCashIn() bool {
	return v.CashIn != nil
}

func (v VerifyPayload) ToPayload() []byte {
	if v.IsCashOut() {
		payload, err := json.Marshal(v.CashOut)
		if err != nil {
			log.Println("Error marshalling payload:", err)
			return nil
		}
		return payload
	}
	if v.IsCashIn() {
		payload, err := json.Marshal(v.CashIn)
		if err != nil {
			log.Println("Error marshalling payload:", err)
			return nil
		}
		return payload
	}
	return nil
}

type CashOut struct {
	InvoiceNumber string `json:"invoice_number"`
	ProductName   string `json:"product_name"`
	Status        string `json:"status"`
	PaymentStatus string `json:"payment_status"`
}
type CashoutItems struct {
	CashIn           CashOut `json:"cash_in"`
	RefInvoiceNumber string  `json:"ref_invoice_number"`
	ItemName         string  `json:"item_name"`
	PartnerName      string  `json:"partner_name"`
	PartnerCode      string  `json:"partner_code"`
	Status           string  `json:"status"`
}
type CashOutPayload struct {
	BatchNumber  string         `json:"batch_number"`
	CashoutItems []CashoutItems `json:"cashout_items"`
	PaymentAt    string         `json:"payment_at"`
	Status       string         `json:"status"`
}

func (c CashOutPayload) TypeName() string {
	return "cash_out"
}

type CashInPayload struct {
	ID                    int       `json:"id"`
	ProviderTranscationID string    `json:"provider_transcation_id"`
	InvoiceNumber         string    `json:"invoice_number"`
	Provider              string    `json:"provider"`
	Type                  string    `json:"type"`
	ProductCode           string    `json:"product_code"`
	CompanyCode           string    `json:"company_code"`
	Fees                  int       `json:"fees"`
	Total                 int       `json:"total"`
	AdminFee              int       `json:"admin_fee"`
	Discount              int       `json:"discount"`
	Voucher               int       `json:"voucher"`
	PgDeliveryFee         int       `json:"pg_delivery_fee"`
	PaymentStatus         string    `json:"payment_status"`
	Status                string    `json:"status"`
	ProductFee            int       `json:"product_fee"`
	ExpiredTime           time.Time `json:"expired_time"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

func (c CashInPayload) TypeName() string {
	return "cash_in"
}

func defaultCashOutPayload(url string) *VerifyPayload {
	payload := `{
      "batch_number":"CO/BNSSIS/2024/10/6743",
      "cashout_items":[
         {
            "cash_in":{
               "invoice_number":"TRANS/SCH-0736/26/10/2024/01853",
               "product_name":"TRANS/SCH-0736/26/10/2024/01853",
               "status":"done",
               "payment_status":"settled"
            },
            "ref_invoice_number":"TRANS/SCH-0736/26/10/2024/01853",
            "item_name":"TRANS/SCH-0736/26/10/2024/01853",
            "partner_name":"SMA Yos Sudarso Metro",
            "partner_code":"MAYSM",
            "status":"done"
         },
         {
            "cash_in":{
               "invoice_number":"TRANS/SCH-0736/27/10/2024/01854",
               "product_name":"TRANS/SCH-0736/27/10/2024/01854",
               "status":"done",
               "payment_status":"settled"
            },
            "ref_invoice_number":"TRANS/SCH-0736/27/10/2024/01854",
            "item_name":"TRANS/SCH-0736/27/10/2024/01854",
            "partner_name":"SMA Yos Sudarso Metro",
            "partner_code":"MAYSM",
            "status":"done"
         },
         {
            "cash_in":{
               "invoice_number":"TRANS/SCH-0736/27/10/2024/01855",
               "product_name":"TRANS/SCH-0736/27/10/2024/01855",
               "status":"done",
               "payment_status":"settled"
            },
            "ref_invoice_number":"TRANS/SCH-0736/27/10/2024/01855",
            "item_name":"TRANS/SCH-0736/27/10/2024/01855",
            "partner_name":"SMA Yos Sudarso Metro",
            "partner_code":"MAYSM",
            "status":"done"
         }
      ],
      "payment_at":"2024-10-29 00:42:18",
      "status":"done"
   }`

	var jsonData []byte = []byte(payload)

	var VerifyPayload VerifyPayload
	err := json.Unmarshal(jsonData, &VerifyPayload.CashOut)
	if err != nil {
		log.Println(err)
	}
	VerifyPayload.URL = url

	return &VerifyPayload
}

func defaultCashInPayload(url string) *VerifyPayload {
	payload := `{
            "id": 137,
            "provider_transcation_id": "INV564456696-PRO6-CH13",
            "invoice_number": "MO/VP/EMBRL/I/2025/101466",
            "provider": "Xendit",
            "type": "ewallet",
            "product_code": "TEMBRL",
            "company_code": "NAA2022",
            "fees": 69000,
            "total": 500000,
            "admin_fee": 2775,
            "discount": 0,
            "voucher": 0,
            "pg_delivery_fee": 0,
            "payment_status": "draft",
            "status": "pending",
            "product_fee": 0,
            "expired_time": "2025-01-07T15:26:24+07:00",
            "created_at": "2025-01-07T15:26:24+07:00",
            "updated_at": "2025-01-07T15:26:24+07:00"
        }`

	var jsonData []byte = []byte(payload)

	var VerifyPayload VerifyPayload
	err := json.Unmarshal(jsonData, &VerifyPayload.CashIn)
	if err != nil {
		log.Println(err)
	}
	VerifyPayload.URL = url

	return &VerifyPayload
}
