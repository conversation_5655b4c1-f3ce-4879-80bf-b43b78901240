package entity

import (
	"github.com/lib/pq"
	"repo.nusatek.id/nusatek/payment/domain"
)

type PartnerShow struct {
	Tags               *[]domain.Tags `json:"tags" gorm:"-"`
	PaymentChannelName string         `json:"payment_channel_name"`
	PartnerCode        string         `json:"partner_code"`
	CompanyPartner     string         `json:"company_partner"`
	Email              string         `json:"email"`
	Emails             pq.StringArray `gorm:"type:[]text" json:"emails"`
	IsSendEmail        bool           `json:"is_send_email"`
	Partner            string         `json:"partner"`
	BankAccountNumber  string         `json:"bank_account_number"`
	CompanyCode        string         `json:"company_code"`
	Phone              string         `json:"phone"`
	AccountReceivable  string         `json:"account_receivable"`
	FeeFixValue        float64        `json:"fee_fix_value"`
	PaymentChannelID   int            `json:"payment_channel_id"`
	ID                 int            `json:"id"`
	SLA                int            `json:"sla"`
	FeePercentage      float64        `json:"fee_percentage"`
	FeeBehind          bool           `json:"fee_behind"`
	Status             bool           `json:"status"`
}

type PartnersResponseAPI struct {
	Code               string   `json:"code" validate:"min=4,alphanum"`
	PaymentChannelName string   `json:"payment_channel_name"`
	Name               string   `json:"name"`
	Phone              string   `json:"phone"`
	ArAccount          string   `json:"ar_account"`
	ApAccount          string   `json:"ap_account"`
	Email              string   `json:"email" validate:"required,email"`
	Emails             []string `json:"emails"`
	IsSendEmail        bool     `json:"is_send_email"`
	FeePercentage      float64  `json:"fee_percentage"`
	Sla                int      `json:"sla"`
	FeeFixValue        float64  `json:"fee_fix_value"`
	ID                 int      `gorm:"primaryKey" json:"id"`
	PaymentChannelId   int      `json:"payment_channel_id"`
	CompanyID          int      `json:"company_id"`
	Status             bool     `json:"status"`
	FeeBehind          bool     `json:"fee_behind"`
}
