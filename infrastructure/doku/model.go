package doku

import (
	"crypto/sha256"
	"encoding/base64"
	"net/url"
	"strings"
	"time"
)

// general config
type Config struct {
	VirtualAccountNotificationURL url.URL
}

// credential
type Credential struct {
	ClientID  string `json:"client_id"`
	SecretKey string `json:"secret_key"`
}

// signature component
type SignatureComponent struct {
	Credential        Credential
	Method            string
	RequestID         string
	RequestTimestamp  string
	ResponseTimestamp string
	RequestTarget     string
	RequestBody       []byte
	ResponseBody      []byte
}

func (sc SignatureComponent) DigestReqBody() string {
	if sc.RequestBody == nil {
		return ""
	}
	// fmt.Println("sc.RequestBody\n", string(sc.RequestBody))
	hasher := sha256.New()
	hasher.Write(sc.RequestBody)

	return (base64.StdEncoding.EncodeToString(hasher.Sum(nil)))
}

func (sc SignatureComponent) DigestResBody() string {
	if sc.ResponseBody == nil {
		return ""
	}
	// fmt.Println("sc.ResponseBody\n", string(sc.ResponseBody))

	hasher := sha256.New()
	hasher.Write(sc.ResponseBody)

	return (base64.StdEncoding.EncodeToString(hasher.Sum(nil)))
}

// http req wrapper
type HttpReqWrapper struct {
	Credential Credential
	Header     map[string]string
	Method     string
	Path       string
	ReqBody    []byte
}

type HttpResWrapper struct {
	Header     map[string]string
	RespBody   []byte
	StatusCode int
}

// error
type Error struct {
	Code    string `json:"code"`
	Type    string `json:"type"`
	Message string `json:"message"`
}
type ErrorResp struct {
	Error Error `json:"error"`
}

// implement error interface
func (e Error) Error() string {
	var sb strings.Builder
	if len(e.Code) > 0 {
		sb.WriteString(e.Code + ":")
	}
	if len(e.Type) > 0 {
		sb.WriteString(e.Type + ":")
	}
	if len(e.Message) > 0 {
		sb.WriteString(e.Message)
	}

	return sb.String()
}

// virtual account
type VAPaymentCodeOrderReq struct {
	InvoiceNumber string  `json:"invoice_number"`
	Amount        float64 `json:"amount"`
}

type VAPaymentCodeInfoReq struct {
	BillingType             string `json:"billing_type"`
	MerchantUniqueReference string `json:"merchant_unique_reference,omitempty"` //required for BNI
	ExpiredTime             int    `json:"expired_time"`
	ReusableStatus          bool   `json:"reusable_status"`
	Info1                   string `json:"info1,omitempty"`
	Info2                   string `json:"info2,omitempty"`
	Info3                   string `json:"info3,omitempty"`
}

type VAPaymentCodeCustomerReq struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type VAPaymentCodeReq struct {
	Order              VAPaymentCodeOrderReq    `json:"order"`
	VirtualAccountInfo VAPaymentCodeInfoReq     `json:"virtual_account_info"`
	Customer           VAPaymentCodeCustomerReq `json:"customer"`
}

type VAPaymentCodeOrderRes struct {
	InvoiceNumber string `json:"invoice_number"`
}

type VAPaymentCodeInfoRes struct {
	VirtualAccountNumber string    `json:"virtual_account_number"`
	HowToPayPage         string    `json:"how_to_pay_page"`
	HowToPayAPI          string    `json:"how_to_pay_api"`
	CreatedDate          string    `json:"created_date"`
	ExpiredDate          string    `json:"expired_date"`
	CreatedDateUtc       time.Time `json:"created_date_utc"`
	ExpiredDateUtc       time.Time `json:"expired_date_utc"`
}

type VAPaymentCodeRes struct {
	Order              VAPaymentCodeOrderRes `json:"order"`
	VirtualAccountInfo VAPaymentCodeInfoRes  `json:"virtual_account_info"`
}

// status
type OrderStatusRes struct {
	Service struct {
		ID string `json:"id"`
	} `json:"service"`
	Acquirer struct {
		ID string `json:"id"`
	} `json:"acquirer"`
	Channel struct {
		ID string `json:"id"`
	} `json:"channel"`
	Transaction struct {
		Status            string    `json:"status"`
		Date              time.Time `json:"date"`
		OriginalRequestID string    `json:"original_request_id"`
	} `json:"transaction"`
	Order struct {
		InvoiceNumber string `json:"invoice_number"`
		Amount        int    `json:"amount"`
	} `json:"order"`
	VirtualAccountInfo struct {
		VirtualAccountNumber string `json:"virtual_account_number"`
	} `json:"virtual_account_info"`
	VirtualAccountPayment struct {
		Identifer []struct {
			Name  string `json:"name"`
			Value string `json:"value"`
		} `json:"identifer"`
	} `json:"virtual_account_payment"`
}

// callback
type VACallback struct {
	Service struct {
		ID string `json:"id"`
	} `json:"service"`
	Acquirer struct {
		ID string `json:"id"`
	} `json:"acquirer"`
	Channel struct {
		ID string `json:"id"`
	} `json:"channel"`
	Transaction struct {
		Status            string    `json:"status"`
		Date              time.Time `json:"date"`
		OriginalRequestID string    `json:"original_request_id"`
	} `json:"transaction"`
	Order struct {
		InvoiceNumber string `json:"invoice_number"`
		Amount        int    `json:"amount"`
	} `json:"order"`
	VirtualAccountInfo struct {
		VirtualAccountNumber string `json:"virtual_account_number"`
	} `json:"virtual_account_info"`
	VirtualAccountPayment struct {
		Identifer []struct {
			Name  string `json:"name"`
			Value string `json:"value"`
		} `json:"identifer"`
	} `json:"virtual_account_payment"`
}
