package xendit

import (
	"encoding/json"
	"fmt"
	"testing"

	"repo.nusatek.id/nusatek/payment/utils"
)

func TestDecryptCredential(t *testing.T) {
	secret := `2f0ca509503340bba426dddc7b680e9d4f3a0250fe1e12be0b462651c07599d7ef9a45210face1f42d4ad2ee5169b4c5732df46bbe968fbd9192c10427742ad904e597ad16350ed0812b7a1475bd1eb9735883c3e52273d42edf61db8eaa237ce9b02fcd4643e09c983bfe1ef22d389e53362d57e39d3cc41c9f35e1a59f77b5868012a8dac56e09ab8bd9baa03c0877fd74b9e65b6ee57b8ecf8c7d71d2dd23b6585b5ec1a01dbb208e9632f3758ae0ea5f63f0e37c67d74929c1b5ab24876c04c3d1e40212ce4bbe4ef705f2`
	dec, err := utils.DecryptAES(secret, "24Hts87532KlstrdsNgrys973Op34GhnEwL")
	if err != nil {
		return
	}
	t.Log("dec", dec)
	var cred Credential
	err = json.Unmarshal([]byte(dec), &cred)
	if err != nil {
		err = fmt.Errorf("unmarshan credential error: %v", err.Error())
		if err != nil {
			return
		}
		return
	}

	fmt.Println(cred)
}

func TestEncryptCredential(t *testing.T) {
	secret := `{}`
	encpStr, err := utils.EncryptAES(secret, "24Hts87532KlstrdsNgrys973Op34GhnEwL")
	if err != nil {
		return
	}

	fmt.Println(encpStr)
}
