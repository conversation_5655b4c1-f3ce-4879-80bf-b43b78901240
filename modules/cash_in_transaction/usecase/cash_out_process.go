package usecase

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"time"

	"gorm.io/gorm"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

// Service is the public interface to perform cashout transactions.
type Service interface {
	CreateTransaction(ctx context.Context, tx *gorm.DB, params CreateParams) (*domain.CashOutTransactions, error)
	UpdateTransaction(ctx context.Context, tx *gorm.DB, params UpdateParams) (*domain.CashOutTransactions, error)
}

// Dependencies required by the service (injected).
type Dependencies struct {
	PartnerService        PartnerService
	CompanyService        CompanyService
	ChannelService        ChannelService
	CompanyProductService CompanyProductService
	CashinRepo            CashinRepository
	Logger                Logger
}

// PartnerService abstracts partner data retrieval.
type PartnerService interface {
	SelectByIds(ctx context.Context, ids []int) ([]domain.Partners, error)
}

// CompanyService abstracts company data retrieval.
type CompanyService interface {
	SelectByIds(ctx context.Context, ids []int) ([]domain.Companies, error)
}

// ChannelService abstracts payment channel retrieval.
type ChannelService interface {
	SelectByIds(ctx context.Context, ids []int) ([]domain.PaymentChannels, error)
}

// CompanyProductService abstracts company product retrieval.
type CompanyProductService interface {
	SelectByIds(ctx context.Context, ids []int) ([]domain.CompanyProducts, error)
}

// CashinRepository abstracts repository operations used by cashout creation/update.
type CashinRepository interface {
	SelectCashOutByPartnerIds(ctx context.Context, partnerIDs []int, status string) ([]domain.CashOutTransactions, error)
	CreateCashOutTransaction(ctx context.Context, txObj *domain.CashOutTransactions, tx *gorm.DB) error
	CreateBulkCashOutTransactionDetail(ctx context.Context, details *[]domain.CashOutTransactionDetails, tx *gorm.DB) error
	GetCashInByCashoutID(ctx context.Context, cashoutID int) ([]domain.CashInTransactionItemsCashout, error)
	UpdatePartialCashoutTransaction(ctx context.Context, tx *gorm.DB, upd *domain.CashOutTransactions) error
	GetTotalCompanyProductFees(ctx context.Context, cashInIDs []int64) (float64, error)
}

// Logger is an abstraction for logging.
type Logger interface {
	Error(ctx context.Context, msg string, kv ...interface{})
}

type service struct {
	deps Dependencies
}

func New(deps Dependencies) Service {
	return &service{deps: deps}
}

// CreateParams contains inputs to create a new cashout transaction.
// The items are expected to belong to the same company, partner, and product grouping
// when you call CreateTransaction for a specific group.
type CreateParams struct {
	Company        domain.Companies
	PaymentChannel domain.PaymentChannels
	Partner        domain.Partners
	CompanyProduct domain.CompanyProducts
	Items          []domain.CashInTransactionItemsCashout
	Sequence       int // for building batch number
}

// UpdateParams contains inputs to update an existing cashout transaction by appending items.
type UpdateParams struct {
	ExistingCashout domain.CashOutTransactions
	CompanyProduct  domain.CompanyProducts
	Items           []domain.CashInTransactionItemsCashout
}

// CreateTransaction builds and persists a new cashout transaction and its details.
func (s *service) CreateTransaction(ctx context.Context, tx *gorm.DB, p CreateParams) (*domain.CashOutTransactions, error) {
	var totalAmount float64
	var totalCashIn int
	var cashInIDs []int64
	cashInMap := make(map[int]bool)

	for _, v := range p.Items {
		totalAmount += v.Amount
		if _, exist := cashInMap[v.CashInTransactionID]; exist {
			continue
		}
		cashInMap[v.CashInTransactionID] = true
		cashInIDs = append(cashInIDs, int64(v.CashInTransactionID))
		totalCashIn++
	}

	fees, err := s.deps.CashinRepo.GetTotalCompanyProductFees(ctx, cashInIDs)
	if err != nil {
		return nil, errors.SetError(http.StatusInternalServerError, err.Error())
	}

	now := time.Now()
	cashOutTransaction := &domain.CashOutTransactions{
		CompanyID:              p.Company.ID,
		PartnerID:              p.Partner.ID,
		PartnerName:            p.Partner.Name,
		PartnerAccountName:     p.Partner.ArAccount,
		PartnerAccountNumber:   p.Partner.ApAccount,
		PartnerBankName:        p.PaymentChannel.Name,
		PaymentChannelID:       p.PaymentChannel.ID,
		CompanyProductID:       &p.CompanyProduct.ID,
		BatchNumber:            buildCashoutNumber(now, p.Company.Code, p.Partner.Code, p.CompanyProduct.Code, p.Sequence),
		Total:                  totalAmount,
		PgReferenceInformation: "{}",
		Status:                 constants.CashOutStatusReady,
		AdminFee:               fees,
		CashOutTransactionHistories: &domain.CashOutTransactionHistories{
			Description: "sync from cash-in transaction",
			Status:      constants.CashOutStatusReady,
		},
	}

	if err := s.deps.CashinRepo.CreateCashOutTransaction(ctx, cashOutTransaction, tx); err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	details := make([]domain.CashOutTransactionDetails, 0, len(p.Items))
	for _, v := range p.Items {
		details = append(details, domain.CashOutTransactionDetails{
			CashOutTransactionID:    cashOutTransaction.ID,
			CashInTransactionItemID: v.ID,
		})
	}
	if err := s.deps.CashinRepo.CreateBulkCashOutTransactionDetail(ctx, &details, tx); err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	return cashOutTransaction, nil
}

// UpdateTransaction appends new items into an existing cashout transaction and updates totals.
func (s *service) UpdateTransaction(ctx context.Context, tx *gorm.DB, p UpdateParams) (*domain.CashOutTransactions, error) {
	currentItems, err := s.deps.CashinRepo.GetCashInByCashoutID(ctx, p.ExistingCashout.ID)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	var (
		totalItemAmount float64
		totalCashIn     int
		cashInIDs       []int64
	)
	cashInMap := make(map[int]bool)

	for _, v := range currentItems {
		totalItemAmount += v.Amount
		if _, exist := cashInMap[v.CashInTransactionID]; !exist {
			cashInMap[v.CashInTransactionID] = true
			cashInIDs = append(cashInIDs, int64(v.CashInTransactionID))
			totalCashIn++
		}
	}

	var newAmount float64
	for _, v := range p.Items {
		newAmount += v.Amount
		if _, exist := cashInMap[v.CashInTransactionID]; !exist {
			cashInMap[v.CashInTransactionID] = true
			cashInIDs = append(cashInIDs, int64(v.CashInTransactionID))
			totalCashIn++
		}
	}

	fees, err := s.deps.CashinRepo.GetTotalCompanyProductFees(ctx, cashInIDs)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	update := &domain.CashOutTransactions{
		ID:       p.ExistingCashout.ID,
		Total:    totalItemAmount + newAmount,
		AdminFee: fees,
	}
	if err := s.deps.CashinRepo.UpdatePartialCashoutTransaction(ctx, tx, update); err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	details := make([]domain.CashOutTransactionDetails, 0, len(p.Items))
	for _, v := range p.Items {
		details = append(details, domain.CashOutTransactionDetails{
			CashOutTransactionID:    update.ID,
			CashInTransactionItemID: v.ID,
		})
	}
	if err := s.deps.CashinRepo.CreateBulkCashOutTransactionDetail(ctx, &details, tx); err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		s.deps.Logger.Error(ctx, err.Error())
		return nil, err
	}

	return update, nil
}

// LoadCashInItems fetches the required datasets for a set of partnerIDs and productIDs.
// This is kept exported if you want to use it from orchestrations, else make it private.
func (s *service) LoadCashInItems(ctx context.Context, partnerIDs, productIDs []int) (ItemCashInData, error) {
	var rd ItemCashInData
	rd.PartnersByID = make(map[int]domain.Partners)
	rd.CompaniesByID = make(map[int]domain.Companies)
	rd.PaymentChannelsByID = make(map[int]domain.PaymentChannels)
	rd.ProductsByID = make(map[int]domain.CompanyProducts)
	rd.CashOutByKey = make(map[partnerProduct]domain.CashOutTransactions)

	partners, err := s.deps.PartnerService.SelectByIds(ctx, partnerIDs)
	if err != nil {
		return rd, errors.SetError(http.StatusInternalServerError, err.Error())
	}
	companyIDSet := make(map[int]struct{}, len(partners))
	paymentChannelIDSet := make(map[int]struct{}, len(partners))
	for _, p := range partners {
		rd.PartnersByID[p.ID] = p
		companyIDSet[p.CompanyID] = struct{}{}
		paymentChannelIDSet[p.PaymentChannelId] = struct{}{}
	}
	companyIDs := setToSlice(companyIDSet)
	paymentChannelIDs := setToSlice(paymentChannelIDSet)

	var (
		wg     sync.WaitGroup
		retErr atomic.Value

		companiesRes []domain.Companies
		channelsRes  []domain.PaymentChannels
		productsRes  []domain.CompanyProducts
		cashoutsRes  []domain.CashOutTransactions
	)

	sem := make(chan struct{}, 3)
	withSem := func(fn func()) {
		sem <- struct{}{}
		defer func() { <-sem }()
		fn()
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		withSem(func() {
			res, e := s.deps.CompanyService.SelectByIds(ctx, companyIDs)
			if e != nil {
				retErr.Store(errors.SetError(http.StatusInternalServerError, e.Error()))
				return
			}
			companiesRes = res
		})
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		withSem(func() {
			res, e := s.deps.ChannelService.SelectByIds(ctx, paymentChannelIDs)
			if e != nil {
				retErr.Store(errors.SetError(http.StatusInternalServerError, e.Error()))
				return
			}
			channelsRes = res
		})
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		withSem(func() {
			res, e := s.deps.CompanyProductService.SelectByIds(ctx, productIDs)
			if e != nil {
				retErr.Store(errors.SetError(http.StatusInternalServerError, e.Error()))
				return
			}
			productsRes = res
		})
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		withSem(func() {
			res, e := s.deps.CashinRepo.SelectCashOutByPartnerIds(ctx, partnerIDs, constants.CashOutStatusReady)
			if e != nil {
				retErr.Store(errors.SetError(http.StatusInternalServerError, e.Error()))
				return
			}
			cashoutsRes = res
		})
	}()

	wg.Wait()
	if v := retErr.Load(); v != nil {
		return rd, v.(error)
	}

	for _, c := range companiesRes {
		rd.CompaniesByID[c.ID] = c
	}
	for _, ch := range channelsRes {
		rd.PaymentChannelsByID[ch.ID] = ch
	}
	for _, pr := range productsRes {
		rd.ProductsByID[pr.ID] = pr
	}
	for _, co := range cashoutsRes {
		if co.CompanyProductID == nil {
			continue
		}
		key := partnerProduct{PartnerId: co.PartnerID, ProductId: *co.CompanyProductID}
		// keep the largest ID per key (latest)
		if existing, ok := rd.CashOutByKey[key]; ok && existing.ID > co.ID {
			continue
		}
		rd.CashOutByKey[key] = co
	}

	return rd, nil
}

type partnerProduct struct {
	PartnerId int
	ProductId int
}

type groupedItems struct {
	ItemByKey map[partnerProduct]domain.CashInTransactionItemsCashout

	ItemIDs    []int
	CashInIDs  []int
	PartnerIDs []int
	ProductIDs []int
}

type ItemCashInData struct {
	PartnersByID        map[int]domain.Partners
	CompaniesByID       map[int]domain.Companies
	PaymentChannelsByID map[int]domain.PaymentChannels
	ProductsByID        map[int]domain.CompanyProducts
	CashOutByKey        map[partnerProduct]domain.CashOutTransactions
}

func CashInItemsForCashOut(items []domain.CashInTransactionItemsCashout) groupedItems {
	itemByKey := make(map[partnerProduct]domain.CashInTransactionItemsCashout)
	itemIDs := make([]int, 0, len(items))
	cashInIDs := make([]int, 0, len(items))
	partnerIDs := make([]int, 0, len(items))
	productIDs := make([]int, 0, len(items))

	for _, item := range items {
		key := partnerProduct{
			PartnerId: item.PartnerId,
			ProductId: item.CashInTransactionCompanyProductID,
		}
		if _, exists := itemByKey[key]; !exists {
			itemByKey[key] = item
			itemIDs = append(itemIDs, item.ID)
			cashInIDs = append(cashInIDs, item.CashInTransactionID)
			partnerIDs = append(partnerIDs, item.PartnerId)
			productIDs = append(productIDs, item.CashInTransactionCompanyProductID)
		}
	}

	return groupedItems{
		ItemByKey:  itemByKey,
		ItemIDs:    itemIDs,
		CashInIDs:  cashInIDs,
		PartnerIDs: partnerIDs,
		ProductIDs: productIDs,
	}
}

func buildCashoutNumber(now time.Time, companyCode, partnerCode, productCode string, nextSeq int) string {
	// Example: CO/<COMPANY>/<YYYY>/<MM>/<SEQ>
	return fmt.Sprintf("CO/%s/%d/%d/%d", companyCode, now.Year(), now.Month(), nextSeq)
}

func setToSlice(m map[int]struct{}) []int {
	out := make([]int, 0, len(m))
	for k := range m {
		out = append(out, k)
	}
	return out
}
