package repository

import (
	"context"
	"log"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/modules/healty/usecase"
)

type healtyRepository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.HealtyRepository {
	return &healtyRepository{db}
}

func (ps *healtyRepository) Ping(ctx context.Context) (err error) {
	sqlDB, err := ps.db.DB()
	if err != nil {
		log.Fatalf("failed select database, err: %s", err.Error())
		return
	}

	err = sqlDB.PingContext(ctx)
	if err != nil {
		log.Fatalf("failed checking connection to database, err: %s", err.<PERSON>rror())
		return
	}

	return
}
