package usecase

import (
	"context"
	"fmt"
	"net/http"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	companyUC "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

type usecase struct {
	r              Repository
	companyService companyUC.CompanyManagementUsecase
}

func NewUsecase(r Repository, companyService companyUC.CompanyManagementUsecase) Usecase {
	return &usecase{
		r:              r,
		companyService: companyService,
	}
}

func (uc usecase) validateCreateBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (validatedData []domain.CompanyProductProviderChannelMapping, err error) {
	companyChannelIDs := []int{}
	for _, v := range data {
		companyChannelIDs = append(companyChannelIDs, v.CompanyPaymentProviderChannelMappingID)
	}
	companyProviderChannelIDMapping := make(map[int]domain.CompanyPaymentProviderChannelMappings)

	// check company_id company_product and company_payment_provider_channel_mapping_id
	companyProduct, err := uc.r.GetCompanyProduct(ctx, companyProductID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	// check existing company_product_id and company_payment_provider_channel_mapping_id
	companyProviderChannelMappings, err := uc.r.GetCompanyProviderChannelMappings(ctx, companyChannelIDs)
	if err != nil && err != gorm.ErrRecordNotFound {
		return
	}
	for _, v := range companyProviderChannelMappings {
		companyProviderChannelIDMapping[int(v.ID)] = v
	}

	// check existing data
	existDatas, err := uc.r.GetExistingMaps(ctx, []int{companyProductID}, companyChannelIDs)
	if err != nil && err != gorm.ErrRecordNotFound {
		return
	}

	for _, v := range data {
		channel := companyProviderChannelIDMapping[v.CompanyPaymentProviderChannelMappingID]
		// compare company id
		if companyProduct.CompanyID != int(channel.CompanyID) {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "company not match")
			logger.Error(ctx, err.Error())
			continue
		}
		if channel.Capability != constants.CashInCapability {
			logger.Error(ctx, "invalid capability")
			continue
		}
		// check existing data
		if existDatas[companyProductID][v.CompanyPaymentProviderChannelMappingID] {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "data exist")
			logger.Error(ctx, err.Error())
			continue
		}
		validatedData = append(validatedData, v)
	}

	return
}

func (uc usecase) CreateBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	data, err = uc.validateCreateBulk(ctx, companyProductID, data)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	res, err = uc.r.CreateBulkWithProductID(ctx, nil, companyProductID, data)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (uc usecase) GetAllWithDetail(ctx context.Context, companyProductID int) (res []domain.CompanyProductProviderChannelMapping, err error) {
	res, err = uc.r.GetAllWithDetailByProductID(ctx, companyProductID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (uc usecase) GetOneByProductIDAndChannelID(ctx context.Context, companyProductID, channelID int) (res domain.CompanyProductProviderChannelMapping, err error) {
	res, err = uc.r.GetOneByProductIDAndChannelID(ctx, companyProductID, channelID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

// update if data exist (by company_product_id and company_payment_provider_channel_mapping_id)
// insert if not exist (by company_product_id and company_payment_provider_channel_mapping_id)
// delete if not send in request(by request was sent)
func (uc usecase) PatchDataBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	// check company products
	companyProduct, err := uc.r.GetCompanyProduct(ctx, companyProductID)
	if err != nil {
		return
	}

	// get all record
	allExists, err := uc.r.GetAllByProductID(ctx, companyProduct.ID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	allExistMaps := make(map[int]domain.CompanyProductProviderChannelMapping) //map[company_payment_provider_channel_mapping_id]domain.CompanyProductProviderChannelMapping
	existInReqMaps := make(map[int]bool)                                      //map[company_payment_provider_channel_mapping_id]isExist
	for _, exist := range allExists {
		allExistMaps[exist.CompanyPaymentProviderChannelMappingID] = exist

		existInReqMaps[exist.CompanyPaymentProviderChannelMappingID] = false
	}

	updateData := []domain.CompanyProductProviderChannelMapping{}
	insertData := []domain.CompanyProductProviderChannelMapping{}
	companyChannelIDs := []int{}
	for _, v := range data {
		exist, isExist := allExistMaps[v.CompanyPaymentProviderChannelMappingID]
		if isExist { // append to updated data
			updateData = append(updateData, domain.CompanyProductProviderChannelMapping{
				ID:            exist.ID,
				FeeFixValue:   v.FeeFixValue,
				FeePercentage: v.FeePercentage,
				Status:        v.Status,
			})
			// flag isExist in request body
			existInReqMaps[v.CompanyPaymentProviderChannelMappingID] = true
		} else {
			companyChannelIDs = append(companyChannelIDs, v.CompanyPaymentProviderChannelMappingID)
			insertData = append(insertData, v)
		}
	}
	// validate inserted data
	insertDataValidated := []domain.CompanyProductProviderChannelMapping{}
	// check existing company_product_id and company_payment_provider_channel_mapping_id
	var companyProviderChannelMappings []domain.CompanyPaymentProviderChannelMappings
	if len(companyChannelIDs) > 0 {
		companyProviderChannelMappings, err = uc.r.GetCompanyProviderChannelMappings(ctx, companyChannelIDs)
		if err != nil && err != gorm.ErrRecordNotFound {
			return
		}
	}

	companyProviderChannelIDMapping := make(map[int]domain.CompanyPaymentProviderChannelMappings)
	for _, v := range companyProviderChannelMappings {
		companyProviderChannelIDMapping[int(v.ID)] = v
	}
	for _, v := range insertData {
		channel, exist := companyProviderChannelIDMapping[v.CompanyPaymentProviderChannelMappingID]
		if exist {
			if companyProduct.CompanyID != int(channel.CompanyID) {
				logger.Error(ctx, fmt.Sprintf("%v invalid company id %v", v.CompanyPaymentProviderChannelMappingID, channel.CompanyID))
				continue
			}
			if channel.Capability != constants.CashInCapability {
				logger.Error(ctx, "invalid capability")
				continue
			}
			insertDataValidated = append(insertDataValidated, v)
		}
	}
	// append deleted data
	deletedIDs := []int{}
	for id, v := range existInReqMaps {
		if !v {
			data := allExistMaps[id]
			deletedIDs = append(deletedIDs, data.ID)
		}
	}

	tx := uc.r.BeginTrans()
	defer tx.Rollback()
	// delete data bulk
	if len(deletedIDs) > 0 {
		err = uc.r.DeleteBulk(ctx, tx, deletedIDs)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "error delete bulk")
			logger.Error(ctx, err.Error())
		}
	}
	// insert data bulk
	if len(insertDataValidated) > 0 {
		_, err = uc.r.CreateBulkWithProductID(ctx, tx, companyProductID, insertDataValidated)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "error delete bulk")
			logger.Error(ctx, err.Error())
		}
	}
	// update data bulk
	if len(updateData) > 0 {
		_, err = uc.r.UpdateBulk(ctx, tx, updateData)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "error delete bulk")
			logger.Error(ctx, err.Error())
		}
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error commit tx")
		logger.Error(ctx, err.Error())
	}

	res = append(res, insertDataValidated...)
	res = append(res, updateData...)
	return
}

func (uc usecase) UpdateBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	// check company products
	companyProduct, err := uc.r.GetCompanyProduct(ctx, companyProductID)
	if err != nil {
		return
	}

	ids := []int{}
	for _, v := range data {
		ids = append(ids, v.ID)
	}
	exists, err := uc.r.GetAllByIDs(ctx, ids)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	for _, exist := range exists {
		if exist.CompanyProductID != companyProduct.ID {
			err = errors.SetErrorMessage(http.StatusBadRequest, "invalid product")
			logger.Error(ctx, err.Error())
			return
		}
	}

	res, err = uc.r.UpdateBulk(ctx, nil, data)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (uc usecase) DeleteBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error) {
	// check company products
	companyProduct, err := uc.r.GetCompanyProduct(ctx, companyProductID)
	if err != nil {
		return
	}

	ids := []int{}
	for _, v := range data {
		ids = append(ids, v.ID)
	}
	exists, err := uc.r.GetAllByIDs(ctx, ids)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	for _, exist := range exists {
		if exist.CompanyProductID != companyProduct.ID {
			err = errors.SetErrorMessage(http.StatusBadRequest, "invalid product")
			logger.Error(ctx, err.Error())
			return
		}
	}

	err = uc.r.DeleteBulk(ctx, nil, ids)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

// usecase function for OPEN API
// TO DO: maybe we could move OpenAPI usecase to new particular usecase for handling open api
func (uc usecase) GetOneWithDetailOpenAPI(ctx context.Context, auth, productCode, channelCode string) (res domain.CompanyProductProviderChannelMapping, err error) {
	company, err := uc.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	res, err = uc.r.GetOneWithDetailByCompany(ctx, company.ID, productCode, channelCode)
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, "data not found")
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return
}

func (uc usecase) CalculateOpenAPI(ctx context.Context, auth, productCode, channelCode string, amount float64) (res domain.CompanyProductProviderChannelMappingCalculate, err error) {
	data, err := uc.GetOneWithDetailOpenAPI(ctx, auth, productCode, channelCode)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	res.CompanyProductProviderChannelMapping = data
	res.CalculatedAmount = amount + data.FeeFixValue + ((data.FeePercentage / 100) * amount)
	res.AdminFee = res.CalculatedAmount - amount

	return
}
