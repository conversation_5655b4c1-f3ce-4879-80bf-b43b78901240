package cashlez

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/go-redis/redis/v8"
	"github.com/go-resty/resty/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

type Wrapper struct {
	Endpoint  string
	isSandbox bool
	client    *resty.Client
	username  string
	password  string
	cache     *redis.Client
}

func NewWrapper() *Wrapper {
	return &Wrapper{}
}

func (w *Wrapper) Setup() *Wrapper {
	var (
		EndpointSandbox = config.GetString("cashlez_backoffice.sandbox")
		Endpoint        = config.GetString("cashlez_backoffice.host")
	)

	w.username = config.GetString("cashlez_backoffice.username")
	w.password = config.GetString("cashlez_backoffice.password")

	w.isSandbox = config.GetBool("cashlez_backoffice.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	r := resty.New()
	r = r.SetTimeout(config.GetDuration("cashlez_backoffice.timeout")).SetDebug(config.GetBool("cashlez_backoffice.debug"))

	if config.GetBool("cashlez_backoffice.skiptls") {
		r = r.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	}
	if config.GetString("cashlez_backoffice.proxy") != "" {
		r = r.SetProxy(config.GetString("cashlez_backoffice.proxy"))
	} else {
		r = r.RemoveProxy()
	}

	w.client = r

	return w
}

func (w *Wrapper) WithRedis(rc *redis.Client) *Wrapper {
	w.cache = rc
	return w
}

// do http req
// result must be pointer of struct and implement ibaseResp
func (w *Wrapper) do(ctx context.Context, req DoReq, result ibaseResp) (err error) {
	fullURL := w.Endpoint + req.Path

	// default header
	if req.Header == nil {
		req.Header = make(map[string]string)
	}
	req.Header["Content-Type"] = "application/json"

	// prepare http client
	r := w.client.R().
		SetHeaders(req.Header)
	if req.ReqBody != nil {
		r.SetBody(req.ReqBody)
	}

	var rr *resty.Response
	logger.Info(ctx, "request", logger.Any("fullURL", fullURL), logger.Any("header", r.Header), logger.Any("requestBody", req.ReqBody))
	switch req.Method {
	case http.MethodGet:
		rr, err = r.Get(fullURL)
	case http.MethodPost:
		rr, err = r.Post(fullURL)
	case http.MethodPut:
		rr, err = r.Put(fullURL)
	case http.MethodPatch:
		rr, err = r.Patch(fullURL)
	case http.MethodDelete:
		rr, err = r.Delete(fullURL)
	}
	if err != nil {
		logger.Error(ctx, "request error", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.Any("header", rr.Header()), logger.Int("statusCode", rr.StatusCode()), logger.Any("responseBody", string(rr.Body())))

	err = json.Unmarshal(rr.Body(), &result)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.Err(err), logger.String("responseBody", string(rr.Body())))
		return
	}

	if rr.StatusCode() < 200 || rr.StatusCode() > 299 {
		err = result.getBaseResp()
		logger.Error(ctx, "invalid status", logger.Err(err))
		return
	}

	return nil
}

func (w *Wrapper) doWithAuth(ctx context.Context, req DoReq, result ibaseResp) (err error) {
	if req.Header == nil {
		req.Header = make(map[string]string)
	}
	token, err := w.getToken(ctx)
	if err != nil {
		logger.Error(ctx, "get token", logger.Err(err))
		err = errors.New("cashlez error get token")
		return
	}
	req.Header["Authorization"] = "Bearer " + token
	return w.do(ctx, req, result)
}
