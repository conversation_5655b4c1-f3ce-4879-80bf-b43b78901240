package entity

import (
	"fmt"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
)

type EmailStatus string

const (
	EmailStatusPending   EmailStatus = "pending"
	EmailStatusSent      EmailStatus = "sent"
	EmailStatusDelivered EmailStatus = "delivered"
	EmailStatusFailed    EmailStatus = "failed"
	EmailStatusOpened    EmailStatus = "opened"
	EmailStatusClicked   EmailStatus = "clicked"
)

func (s EmailStatus) MappedWithMailGunStatus() MailGunStatus {
	switch s {
	case EmailStatusPending:
		return MailGunStatusAccepted
	case EmailStatusSent:
		return MailGunStatusDelivered
	case EmailStatusFailed:
		return MailGunStatusFailed
	case EmailStatusOpened:
		return MailGunStatusOpened
	default:
		return ""
	}
}

type MailGunStatus string

const (
	MailGunStatusAccepted  MailGunStatus = "accepted"
	MailGunStatusDelivered MailGunStatus = "delivered"
	MailGunStatusFailed    MailGunStatus = "failed"
	MailGunStatusOpened    MailGunStatus = "opened"
)

type EmailLog struct {
	Id                int64            `json:"id" gorm:"primaryKey"`
	CashoutEmailLogId *int64           `json:"cashout_email_log_id"`
	ParentId          *int64           `json:"parent_id" gorm:"index"`
	Parent            *EmailLog        `json:"parent,omitempty" gorm:"foreignKey:ParentId"`
	Children          []EmailLog       `json:"children,omitempty" gorm:"foreignKey:ParentId"`
	CompanyId         int64            `json:"company_id"`
	Company           domain.Companies `json:"company" gorm:"foreignKey:CompanyId;references:ID"`
	PartnerId         int64            `json:"partner_id"`
	Partner           domain.Partners  `json:"partner" gorm:"foreignKey:PartnerId;references:ID"`
	BatchNumber       string           `json:"batch_number"`
	Email             string           `json:"email"`
	SendDate          time.Time        `json:"send_date"`
	Status            EmailStatus      `json:"status"`
	ErrorMessage      string           `json:"error_message"`
	CreatedAt         time.Time        `json:"created_at"`
}

func (e *EmailLog) TableName() string {
	return "email_logs"
}

type CashoutEmailLog struct {
	ID          int64     `json:"id"`
	CashoutID   int64     `json:"cashout_id"`
	CompanyID   int64     `json:"company_id"`
	PartnerID   int64     `json:"partner_id"`
	BatchNumber string    `json:"batch_number"`
	Email       string    `json:"email"`
	MessageBody string    `json:"message_body"`
	SendAt      time.Time `json:"send_at"`
	LastStatus  string    `json:"last_status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type EmailLogPaginate struct {
	*utils.Pagination
	Status      string `query:"status" json:"status"`
	Email       string `query:"email" json:"email"`
	BatchNumber string `query:"batch_number" json:"batch_number"`
	Company     int64  `query:"company" json:"company"`
	Partner     int64  `query:"partner" json:"partner"`
	StartDate   string `query:"start_date" json:"start_date"`
	EndDate     string `query:"end_date" json:"end_date"`
}

func (p *EmailLogPaginate) DateFilter() (string, string) {
	parseDate := func(dateStr string) (*time.Time, error) {
		if dateStr == "" {
			return nil, nil
		}
		t, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			return nil, err
		}
		return &t, nil
	}

	start, startErr := parseDate(p.StartDate)
	end, endErr := parseDate(p.EndDate)

	if startErr != nil || endErr != nil {
		return "", ""
	}

	if start == nil && end == nil {
		return "", ""
	}

	now := time.Now()
	oneMonthAgo := now.AddDate(0, -1, 0)

	if start == nil {
		start = &oneMonthAgo
	}
	if end == nil {
		end = &now
	}

	return fmt.Sprintf("%s 00:00:00", start.Format("2006-01-02")), fmt.Sprintf("%s 23:59:59", end.Format("2006-01-02"))
}
