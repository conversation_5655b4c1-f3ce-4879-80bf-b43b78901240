package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
)

type counterRepo struct {
	DB *gorm.DB
}

func NewCounterRepository(db *gorm.DB) usecase.CounterRepository {
	return &counterRepo{db}
}

func (r counterRepo) GetLastCounter(ctx context.Context, mappingId int, types string) (res domain.CompanyPaymentProviderChannelMappingCounter, err error) {
	q := `UPDATE company_payment_provider_channel_mapping_counters 
		SET counter = counter+1 
		WHERE company_payment_provider_channel_mapping_id = ? AND type = ?
		RETURNING id, company_payment_provider_channel_mapping_id, type, counter, created_at, updated_at`

	err = r.DB.Debug().Raw(q, mappingId, types).
		Row().Scan(&res.ID, &res.CompanyPaymentProviderChannelMappingId, &res.Type, &res.Counter, &res.CreatedAt, &res.UpdatedAt)

	return
}

func (r counterRepo) DecreaseCounter(ctx context.Context, mappingId int, types string) (res domain.CompanyPaymentProviderChannelMappingCounter, err error) {
	q := `UPDATE company_payment_provider_channel_mapping_counters 
		SET counter = counter-1 
		WHERE company_payment_provider_channel_mapping_id = ? AND type = ?
		RETURNING id, company_payment_provider_channel_mapping_id, type, counter, created_at, updated_at`

	err = r.DB.Debug().Raw(q, mappingId, types).
		Row().Scan(&res.ID, &res.CompanyPaymentProviderChannelMappingId, &res.Type, &res.Counter, &res.CreatedAt, &res.UpdatedAt)

	return
}

func (r counterRepo) GetOne(ctx context.Context, mappingId int, types string) (res domain.CompanyPaymentProviderChannelMappingCounter, err error) {
	gormData := entity.CounterGORM{}

	err = r.DB.Debug().Where("company_payment_provider_channel_mapping_id = ? AND type = ?", mappingId, types).Take(&gormData).Error
	res = gormData.ToDomain()

	return
}

func (r counterRepo) Create(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter) (err error) {
	gormData := entity.CounterGORM{}
	gormData.FromDomain(req)
	if tx == nil {
		tx = r.DB
	}
	gormData.CreatedAt = time.Now()
	gormData.UpdatedAt = time.Now()
	err = tx.Debug().Create(&gormData).Error
	res := gormData.ToDomain()
	req.ID = res.ID

	return
}

func (r counterRepo) Update(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter) (err error) {
	gormData := entity.CounterGORM{}
	if tx == nil {
		tx = r.DB
	}
	return tx.Debug().Model(&gormData).Where("id = ?", req.ID).Updates(map[string]interface{}{
		"counter":    req.Counter,
		"updated_at": time.Now(),
	}).Error
}

func (r counterRepo) CreateIfNotExist(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter) (res domain.CompanyPaymentProviderChannelMappingCounter, err error) {
	res, err = r.GetOne(ctx, req.CompanyPaymentProviderChannelMappingId, req.Type)
	if err != nil && err != gorm.ErrRecordNotFound {
		return
	}

	if err == gorm.ErrRecordNotFound {
		res = *req
		err = r.Create(ctx, tx, &res)
		if err != nil {
			return
		}
	}
	return
}

func (r counterRepo) CreateOrUpdateCounter(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter, currentCounter int64) (res domain.CompanyPaymentProviderChannelMappingCounter, err error) {
	res, err = r.GetOne(ctx, req.CompanyPaymentProviderChannelMappingId, req.Type)
	if err != nil && err != gorm.ErrRecordNotFound {
		return
	}

	if err == gorm.ErrRecordNotFound {
		err = r.Create(ctx, tx, req)
		if err != nil {
			return
		}
		res = *req
	}

	if res.Counter == currentCounter {
		req.ID = res.ID
		r.Update(ctx, tx, req)
		res = *req
	}

	return
}
