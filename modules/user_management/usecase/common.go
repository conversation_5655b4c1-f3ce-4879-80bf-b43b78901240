package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultUserManagement) getCacheUserMenu(ctx context.Context, menuId string) (resp *domain.Menus, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheUserMenu, menuId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.userRepo.GetUserMenuById(ctx, menuId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "menu not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheUserMenu(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Menus
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultUserManagement) setCacheUserMenu(ctx context.Context, req *domain.Menus) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheUserMenu, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultUserManagement) deleteCacheUserMenu(ctx context.Context, menuId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheUserMenu, menuId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultUserManagement) getCacheUser(ctx context.Context, userId string) (resp *domain.Users, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheUser, userId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		userIDs, _ := strconv.Atoi(userId)
		prod, errRes := s.userRepo.GetUserById(ctx, userIDs)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "user not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheUser(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Users
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultUserManagement) setCacheUser(ctx context.Context, req *domain.Users) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheUser, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultUserManagement) deleteCacheUser(ctx context.Context, userId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheUser, userId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func sortDifferenceMenus(slice1 []int, slice2 []int) []int {
	var res []int
	for i := 0; i < len(slice1); i++ {
		counter := 0
		for k := 0; k < len(slice2); k++ {
			if slice1[i] == slice2[k] {
				counter = counter + 1
			}
		}
		if counter == 0 {
			res = append(res, slice1[i])
		}
	}
	return res
}
