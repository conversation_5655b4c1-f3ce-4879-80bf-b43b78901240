package cashlez

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
)

func (w *Wrapper) encrypt(ctx context.Context, req EncryptReq) (res EncryptResp, err error) {
	doReq := DoReq{
		Method: http.MethodPost,
		Path:   "/api-v2/v2/encrypt",
	}
	doReq.ReqBody = req

	err = w.do(ctx, doReq, &res)
	if err != nil {
		return
	}

	return
}

// expired token = 15 minutes
func (w *Wrapper) login(ctx context.Context, req LoginReq) (res LoginResp, err error) {
	doReq := DoReq{
		Method: http.MethodPost,
		Path:   "/api-v2/v2/guest/login",
	}
	doReq.ReqBody = req

	err = w.do(ctx, doReq, &res)
	if err != nil {
		return
	}

	return
}

func (w *Wrapper) getToken(ctx context.Context) (token string, err error) {
	cacheKey := "cashlez-token"
	err = w.cache.Get(ctx, cacheKey).Scan(&token)
	if err != nil {
		logger.Error(ctx, "error get token", logger.Err(err), logger.String("cacheKey", cacheKey))
		// return //ignore error
	}

	if len(token) <= 0 {
		logger.Info(ctx, "token cashlez empty, trying to get token")
		encryptRes, err := w.encrypt(ctx, EncryptReq{
			Password: w.password,
		})
		if err != nil {
			logger.Error(ctx, "error cashlez encrypt", logger.Err(err), logger.String("cacheKey", cacheKey))
			return token, err
		}

		loginResp, err := w.login(ctx, LoginReq{
			Username: w.username,
			Password: encryptRes.EncryptedPassword,
		})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error cashlez login %v", err))
			return token, err
		}
		token = loginResp.Token
		err = w.cache.Set(ctx, cacheKey, token, time.Minute*10).Err()
		if err != nil {
			logger.Error(ctx, "error cashlez set redis", logger.Err(err), logger.String("cacheKey", cacheKey))
			// return //ignore error
		}

	}

	return token, nil
}

func (w *Wrapper) TransactionAdvanceSearch(ctx context.Context, pageNumber int, req TransactionAdvanceSearchReq) (res TransactionAdvanceSearchResp, err error) {
	doReq := DoReq{
		Method: http.MethodPost,
		Path:   fmt.Sprintf("/api-v2/v2/mcc/transaction/advancedSearch/%d", pageNumber),
	}
	doReq.ReqBody = req

	err = w.doWithAuth(ctx, doReq, &res)
	if err != nil {
		return
	}

	return
}
