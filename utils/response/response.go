package response

import (
	"math"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	common "repo.nusatek.id/moaja/backend/services/entities/common"

	"repo.nusatek.id/nusatek/payment/utils"
)

type responsePagination struct {
	common.Response
	Pagination utils.ItemPages `json:"pagination"`
}

func HandleSuccessWithPagination(c *fiber.Ctx, totalItems float64, paginate utils.Pagination, data interface{}) error {
	var totalPage float64 = 1
	if paginate.Limit != 0 && paginate.Page != 0 {
		res := totalItems / float64(paginate.Limit)
		totalPage = math.Ceil(res)
	}

	resp := responsePagination{
		Response: common.Response{
			Code:    http.StatusOK,
			Message: http.StatusText(http.StatusOK),
			Data:    data,
		},
		Pagination: utils.ItemPages{
			TotalData: int64(totalItems),
			TotalPage: int64(totalPage),
			Page:      int64(paginate.Page),
			Limit:     int64(paginate.Limit),
		},
	}
	return c.Status(fiber.StatusOK).JSON(resp)
}

type commonResp struct {
	common.Response
	CorrelationId string `json:"correlation_id,omitempty"`
}

func HandleSuccess(c *fiber.Ctx, data interface{}) error {
	ctx := c.UserContext()
	resp := commonResp{
		Response: common.Response{
			Code:    http.StatusOK,
			Message: http.StatusText(http.StatusOK),
			Data:    data,
		},
		CorrelationId: logger.GetCorrelationId(ctx),
	}
	return c.Status(fiber.StatusOK).JSON(resp)
}
