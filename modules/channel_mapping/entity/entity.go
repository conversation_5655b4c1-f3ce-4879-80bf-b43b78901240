package channel_mapping

import "repo.nusatek.id/nusatek/payment/domain"

type PaymentChannelMappingList struct {
	ID                         int64                                    `json:"id"`
	PaymentChannelID           int64                                    `json:"payment_channel_id"`
	PaymentChannelName         string                                   `json:"payment_channel_name"`
	Code                       string                                   `json:"code"`
	MaxTransaction             float64                                  `json:"max_transaction"`
	Sla                        int                                      `json:"sla"`
	ProviderChannelCode        string                                   `json:"provider_channel_code"`
	Capability                 int                                      `json:"capability"`
	ChannelTypeConfigTemplates domain.PaymentChannelTypeConfigTemplates `json:"channel_type_config_templates"`
	PaymentInstructions        string                                   `json:"payment_instructions"`
}
