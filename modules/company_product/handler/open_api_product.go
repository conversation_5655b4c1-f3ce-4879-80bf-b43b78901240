package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

func (h *CompanyProductHandler) CreateCompanyProductAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var req domain.CompanyProducts
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.companyProductService.CreateCompanyProductAPI(c.UserContext(), &req, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *CompanyProductHandler) UpdateCompanyProductAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	productId := c.Params("id")

	var req domain.CompanyProducts
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.companyProductService.UpdateCompanyProductAPI(c.UserContext(), productId, string(auth), &req)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *CompanyProductHandler) DeleteCompanyProductAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	id := c.Params("id")

	err = h.companyProductService.DeleteCompanyProductAPI(c.UserContext(), id, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, nil)
}

func (h *CompanyProductHandler) GetListCompanyProductAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}

	resp, totalData, err := h.companyProductService.ListCompanyProductAPI(c.UserContext(), paginate, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *CompanyProductHandler) GetCompanyProductCodeAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	code := c.Params("code")

	resp, err := h.companyProductService.GetCompanyProductCodeAPI(c.UserContext(), string(auth), code)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}
