package usecase

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	entity "repo.nusatek.id/nusatek/payment/modules/channel_mapping/entity"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (s *defaultChannelMapping) CreateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error) {
	err = s.checkPaymentChannelAndPaymentProvider(ctx, req.PaymentChannelID, req.PaymentProviderID)
	if err != nil {
		return
	}

	exist, err := s.channelMappingRepo.GetChannelMappingByProviderChannelIdAndCapability(ctx, strconv.Itoa(int(req.PaymentProviderID)), strconv.Itoa(int(req.PaymentChannelID)), strconv.Itoa(req.Capability))
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}

	if exist.ID != 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate provider ,payment channel and capability record")
		return
	}

	err = s.channelMappingRepo.CreateChannelMapping(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultChannelMapping) UpdateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings, id string) (err error) {
	err = s.checkPaymentChannelAndPaymentProvider(ctx, req.PaymentChannelID, req.PaymentProviderID)
	if err != nil {
		return
	}
	mapping, err := s.channelMappingRepo.GetChannelMappingById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	req.CreatedAt = mapping.CreatedAt
	req.ID = mapping.ID
	err = s.channelMappingRepo.UpdateChannelMapping(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultChannelMapping) DeleteChannelMapping(ctx context.Context, id string) (err error) {
	mapping, err := s.channelMappingRepo.GetChannelMappingById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	exist, err := s.channelMappingRepo.ChekExistingRelationDataCompany(ctx, mapping.PaymentProviderID, mapping.PaymentChannelID, mapping.Capability)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if exist {
		err = errors.SetError(http.StatusForbidden, "there is relation data")
		logger.Error(ctx, err.Error())
		return
	}

	var req domain.PaymentProviderChannelMappings
	req.ID = mapping.ID
	err = s.channelMappingRepo.DeleteChannelMapping(ctx, &req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultChannelMapping) GetListChannelMappingByProviderId(ctx context.Context, params domain.GetPaymentChannelsParams, providerId string) (resp *[]entity.PaymentChannelMappingList, totalData int64, err error) {
	_, err = s.paymentProviderRepo.GetPaymentProviderById(ctx, providerId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "ProviderID not found")
		logger.Error(ctx, err.Error())
		return
	}

	resp, totalData, err = s.channelMappingRepo.GetListChannelMappingByProviderId(ctx, params, providerId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if totalData <= 0 {
		totalData = -1
	}

	return
}

func (s *defaultChannelMapping) GetChannelMappingById(ctx context.Context, id string) (resp *domain.PaymentProviderChannelMappings, err error) {
	resp, err = s.channelMappingRepo.GetChannelMappingById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultChannelMapping) checkPaymentChannelAndPaymentProvider(ctx context.Context, paymentChannelId, paymentProviderId int64) (err error) {
	provider, err := s.paymentProviderRepo.GetPaymentProviderById(ctx, strconv.Itoa(int(paymentProviderId)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment provider not found")
		logger.Error(ctx, err.Error())
		return
	}
	channel, err := s.paymentChannelRepo.GetPaymentChannelById(ctx, strconv.Itoa(int(paymentChannelId)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}
	if !provider.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Payment provider is not active")
		logger.Error(ctx, err.Error())
		return
	}
	if !channel.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Payment channel is not active")
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultChannelMapping) GetChannelMappingByCode(ctx context.Context, code string) (resp *domain.PaymentProviderChannelMappings, err error) {
	resp, err = s.channelMappingRepo.GetChannelMappingByCode(ctx, code)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel mapping not found")
		logger.Error(ctx, err.Error())
		return
	}
	return
}
