package usecase

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (s *defaultCompanyManagement) GetCompanyByIdShow(ctx context.Context, id string) (resp entity.CompanyShow, err error) {
	company, err := s.GetCompanyById(ctx, id)
	if err != nil {
		return
	}

	resp = entity.CompanyShow{
		ID:              company.ID,
		ParentCompanyID: company.ParentCompanyID,
		Code:            company.Code,
		Name:            company.Name,
		Alias:           company.Alias,
		Email:           company.Email,
		Secrets:         company.Secrets,
		Status:          company.Status,
		CreatedAt:       company.CreatedAt,
		UpdatedAt:       company.UpdatedAt,
		DeletedAt:       company.DeletedAt,
	}
	return
}

func (s *defaultCompanyManagement) GenerateRandomApiKey(ctx context.Context) (resp string, err error) {
	// TODO: improvement on generate unique string
	username := str.RandString(20)
	password := str.RandString(20)
	basicStr := fmt.Sprintf("%s:%s", username, password)
	fmt.Println(basicStr)
	return base64.URLEncoding.EncodeToString([]byte(basicStr)), nil
}

func (s *defaultCompanyManagement) GetCompanyByCode(ctx context.Context, code string) (resp *domain.Companies, err error) {
	resp, err = s.getCacheCompany(ctx, code)
	return
}

func (s *defaultCompanyManagement) validate(ctx context.Context, req *domain.Companies) (err error) {
	// common validation
	if (req.ParentCompanyID != nil) && (*req.ParentCompanyID < 1 || *req.ParentCompanyID == req.ID) {
		req.ParentCompanyID = nil
	}
	var parentCompany *domain.Companies
	if req.ParentCompanyID != nil && *req.ParentCompanyID > 0 {
		parentCompany, err = s.companyRepo.GetCompanyById(ctx, strconv.Itoa(*req.ParentCompanyID))
		if err != nil {
			logger.Error(ctx, err.Error())
			err = errors.SetErrorMessage(http.StatusBadRequest, "parent company not found")
			return err
		}
		req.ParentCompanyID = &parentCompany.ID // assign parent company id
	}

	if req.ID > 0 { // update validation
		exist, err := s.companyRepo.GetCompanyById(ctx, strconv.Itoa(req.ID))
		if err != nil {
			logger.Error(ctx, err.Error())
			err = errors.SetError(http.StatusNotFound, err.Error())
			return err
		}
		if len(req.Secrets) < 1 {
			req.Secrets = exist.Secrets
		}
		req.CreatedAt = exist.CreatedAt
		req.UpdatedAt = time.Now()

		if !req.Status { // if inactive status, check existing active data
			existRelationData, err := s.companyRepo.ChekExistingRelationData(ctx, exist.ID, true)
			if err != nil && err != gorm.ErrRecordNotFound {
				err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
				logger.Error(ctx, err.Error())
				return err
			}
			if existRelationData {
				err = errors.SetErrorMessage(http.StatusForbidden, "there is active relation data")
				logger.Error(ctx, err.Error())
				return err
			}
		}

		// if parentCompany != nil && req.Status && !parentCompany.Status {
		// 	err = errors.SetErrorMessage(http.StatusBadRequest, "parent company not active")
		// 	logger.Error(ctx, err.Error())
		// 	return err
		// }
	}

	return nil
}

func (s *defaultCompanyManagement) CreateCompany(ctx context.Context, req *domain.Companies) (err error) {
	err = s.validate(ctx, req)
	if err != nil {
		return
	}

	// TODO: encrypt secret with our key before save to DB
	// decode base 64 secrets
	base64B, err := base64.URLEncoding.DecodeString(req.Secrets)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error decode base64 %v", err))
	}
	base64S := strings.Split(string(base64B), ":")

	secrets := entity.CompanySecret{
		Username: base64S[0],
		Password: base64S[1],
	}

	err = utils.ValidateStruct(secrets)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	err = s.companyRepo.CreateCompany(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.setCacheCompany(ctx, req)
	_ = s.setCacheCompanySecrets(ctx, req)

	return
}

func (s *defaultCompanyManagement) UpdateCompany(ctx context.Context, req *domain.Companies) (err error) {
	err = s.validate(ctx, req)
	if err != nil {
		return
	}

	err = s.companyRepo.UpdateCompany(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.setCacheCompany(ctx, req)
	_ = s.setCacheCompanySecrets(ctx, req)

	return
}

func (s *defaultCompanyManagement) DeleteCompany(ctx context.Context, id string) (err error) {
	//check existing company products
	idInt, _ := strconv.Atoi(id)
	existRelationData, err := s.companyRepo.ChekExistingRelationData(ctx, idInt, false)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if existRelationData {
		err = errors.SetErrorMessage(http.StatusForbidden, "company already have relation data")
		logger.Error(ctx, err.Error())
		return
	}

	company, err := s.companyRepo.GetCompanyById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	err = s.companyRepo.DeleteCompany(ctx, company)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.deleteCacheCompany(ctx, company.Code)
	_ = s.deleteCacheCompanySecrets(ctx, company.Secrets)

	return
}

func (s *defaultCompanyManagement) SearchCompany(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error) {
	switch true {
	case paginate.Value != "":
		resp, totalData, err = s.companyRepo.SearchCompany(ctx, paginate)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	default:
		resp, totalData, err = s.companyRepo.CompanyList(ctx, paginate)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}
	return
}

func (s *defaultCompanyManagement) GetCompanyListAll(ctx context.Context, status string) (resp *[]domain.Companies, totalData int64, err error) {
	switch status {
	case "true":
		resp, totalData, err = s.companyRepo.CompanyListAllActive(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	case "false":
		resp, totalData, err = s.companyRepo.CompanyListAllNotActive(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	default:
		resp, totalData, err = s.companyRepo.CompanyListAll(ctx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	return
}

func (s *defaultCompanyManagement) GetCompanyById(ctx context.Context, id string) (resp *domain.Companies, err error) {
	resp, err = s.companyRepo.GetCompanyById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyManagement) GetCompanyBySecrets(ctx context.Context, secrets string) (resp *domain.Companies, err error) {
	resp, err = s.getCacheCompanySecrets(ctx, secrets)
	return
}

func (s *defaultCompanyManagement) ValidatorCompanySecret(ctx context.Context, auth string) (resp *domain.Companies, err error) {
	secret, err := utils.ValidatorBasicAuth(auth)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusUnauthorized, err.Error())
		return
	}
	resp, err = s.GetCompanyBySecrets(ctx, secret)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "unauthorize auth token")
		logger.Error(ctx, err.Error())
		return
	}
	if !resp.Status {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "company is disabled, please contact admin to activate it")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCompanyManagement) SelectByIds(ctx context.Context, ids []int) (resp []domain.Companies, err error) {
	resp, err = s.companyRepo.SelectByIds(ctx, ids)

	return
}

func (s *defaultCompanyManagement) IsActive(ctx context.Context, companyID int64) (resp bool, err error) {
	return s.companyRepo.IsActive(ctx, companyID)
}

func (s *defaultCompanyManagement) UpdateStatus(ctx context.Context, id string, status bool) error {
	resp, err := s.companyRepo.GetCompanyById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
		logger.Error(ctx, err.Error())
		return err
	}
	resp.Status = status

	err = s.validate(ctx, resp)
	if err != nil {
		return err
	}

	err = s.companyRepo.UpdateCompany(ctx, resp)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return err
	}

	s.setCacheCompany(ctx, resp)
	s.setCacheCompanySecrets(ctx, resp)
	return nil
}
