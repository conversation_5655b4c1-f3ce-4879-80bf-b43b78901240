package doku

import (
	"context"
	"net/http"
	"net/url"
	"testing"

	"github.com/go-resty/resty/v2"
)

func Test_wrapper_VerifyCallbackSignature(t *testing.T) {
	type fields struct {
		Endpoint  string
		isSandbox bool
		client    *resty.Client
	}
	type args struct {
		ctx       context.Context
		comp      SignatureComponent
		signature string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "verify signature",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				comp: SignatureComponent{
					Credential: Credential{
						ClientID:  "BRN-0206-*************",
						SecretKey: "SK-b4PNngoNR3CMxNnjbqU8",
					},
					RequestID:        "VIRTUAL_ACCOUNT_BCA2226230507222629655107169182221000173504",
					RequestTimestamp: "2023-05-07T15:35:00Z",
					RequestTarget:    "/doku/va/callback",
					RequestBody: []byte(`{
						"service": {
						  "id": "VIRTUAL_ACCOUNT"
						},
						"acquirer": {
						  "id": "BCA"
						},
						"channel": {
						  "id": "VIRTUAL_ACCOUNT_BCA"
						},
						"order": {
						  "invoice_number": "f1fa9006-7ea6-4acb-80e1-b6683e7a369a",
						  "amount": 12000
						},
						"virtual_account_info": {
						  "virtual_account_number": "****************"
						},
						"virtual_account_payment": {
						  "date": "**************",
						  "systrace_number": "17444",
						  "reference_number": "12210",
						  "channel_code": "",
						  "request_id": "891609",
						  "identifier": [
							{
							  "name": "REQUEST_ID",
							  "value": "891609"
							},
							{
							  "name": "REFERENCE",
							  "value": "12210"
							},
							{
							  "name": "CHANNEL_TYPE",
							  "value": "6014"
							}
						  ]
						},
						"transaction": {
						  "status": "SUCCESS",
						  "date": "2023-05-07T15:26:29Z",
						  "original_request_id": "a5f3dc10-3577-47ce-88d6-19c360fea0e4"
						}
					  }`),
				},
				signature: "HMACSHA256=RFFYD55sChkW2DopwXyfNTyFSzgV5J85ZQqtTstuSEk=",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &wrapper{
				Endpoint:  tt.fields.Endpoint,
				isSandbox: tt.fields.isSandbox,
				client:    tt.fields.client,
			}
			if err := w.VerifyCallbackSignature(tt.args.ctx, tt.args.comp, tt.args.signature); (err != nil) != tt.wantErr {
				t.Errorf("wrapper.VerifyCallbackSignature() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_URL(t *testing.T) {

	url, _ := url.Parse("https://827a-182-1-89-254.ap.ngrok.io/doku/va/callback")
	t.Log(url.Path)
}

func Test_wrapper_verifyResponseSignature(t *testing.T) {
	type fields struct {
		Endpoint  string
		isSandbox bool
		client    *resty.Client
		config    Config
	}
	type args struct {
		ctx       context.Context
		comp      SignatureComponent
		signature string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "verify",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				comp: SignatureComponent{
					Credential: Credential{
						ClientID:  "BRN-0206-*************",
						SecretKey: "SK-b4PNngoNR3CMxNnjbqU8",
					},
					Method:            http.MethodGet,
					RequestID:         "3202b44c-2b23-4696-bf07-7e1980b68e5d",
					ResponseTimestamp: "2023-05-07T17:45:06Z",
					RequestTarget:     "/orders/v1/status/f1fa9006-7ea6-4acb-80e1-b6683e7a369a",
					ResponseBody:      []byte(`{"order":{"invoice_number":"f1fa9006-7ea6-4acb-80e1-b6683e7a369a","amount":12000},"transaction":{"status":"SUCCESS","date":"2023-05-07T15:26:29Z","original_request_id":"a5f3dc10-3577-47ce-88d6-19c360fea0e4"},"service":{"id":"VIRTUAL_ACCOUNT"},"acquirer":{"id":"BCA"},"channel":{"id":"VIRTUAL_ACCOUNT_BCA"},"virtual_account_info":{"virtual_account_number":"****************","created_date":"**************","expired_date":"**************","reusable_status":false},"virtual_account_payment":{"identifier":[{"name":"REQUEST_ID","value":"891609"},{"name":"REFERENCE","value":"12210"},{"name":"CHANNEL_TYPE","value":"6014"}],"reference_number":"12210"}}`),
				},
				signature: "HMACSHA256=SqV7YvBDrXC4tFppNw7J7pWYkwh8KOvOs4SD8CsHPaM=",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &wrapper{
				Endpoint:  tt.fields.Endpoint,
				isSandbox: tt.fields.isSandbox,
				client:    tt.fields.client,
				config:    tt.fields.config,
			}
			if err := w.verifyResponseSignature(tt.args.ctx, tt.args.comp, tt.args.signature); (err != nil) != tt.wantErr {
				t.Errorf("wrapper.verifyResponseSignature() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
