package domain

import (
	"time"

	"gorm.io/gorm"
)

type CompanyProducts struct {
	ID                   int            `gorm:"primaryKey" json:"id"`
	CompanyID            int            `json:"company_id"`
	Code                 string         `json:"code" validate:"min=4,alphanum"`
	ProductName          string         `json:"product_name" validate:"required"`
	FeeFixValue          float64        `json:"fee_fix_value"`
	FeePercentage        float64        `json:"fee_percentage"`
	CashoutFeeFixValue   float64        `json:"cashout_fee_fix_value"`
	CashoutFeePercentage float64        `json:"cashout_fee_percentage"`
	Status               bool           `json:"status"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
