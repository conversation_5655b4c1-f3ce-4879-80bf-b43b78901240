package usecase

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
)

type Repository interface {
	BeginTrans(opts ...*sql.TxOptions) *gorm.DB
	Create(ctx context.Context, data domain.CompanyProductProviderChannelMapping)
	GetCompanyProduct(ctx context.Context, productID int) (res domain.CompanyProducts, err error)
	GetCompanyProductByCode(ctx context.Context, productCode string) (res domain.CompanyProducts, err error)
	GetCompanyProviderChannelMappings(ctx context.Context, channelMappingIDs []int) (res []domain.CompanyPaymentProviderChannelMappings, err error)
	GetExistingMaps(ctx context.Context, productIDs []int, channelIDs []int) (exists map[int]map[int]bool, err error)
	CreateBulkWithProductID(ctx context.Context, tx *gorm.DB, productID int, datas []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	GetOneByProductIDAndChannelID(ctx context.Context, productID, channelID int) (res domain.CompanyProductProviderChannelMapping, err error)
	GetAllByIDs(ctx context.Context, ids []int) (res []domain.CompanyProductProviderChannelMapping, err error)
	GetAllByProductID(ctx context.Context, productID int) (res []domain.CompanyProductProviderChannelMapping, err error)
	GetAllWithDetailByProductID(ctx context.Context, productID int) (res []domain.CompanyProductProviderChannelMapping, err error)
	GetOneWithDetailByCompany(ctx context.Context, companyID int, productCode, provChannelCode string) (res domain.CompanyProductProviderChannelMapping, err error)
	UpdateBulk(ctx context.Context, tx *gorm.DB, datas []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	DeleteBulk(ctx context.Context, tx *gorm.DB, ids []int) (err error)
}

type Usecase interface {
	CreateBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	GetOneByProductIDAndChannelID(ctx context.Context, companyProductID, channelID int) (res domain.CompanyProductProviderChannelMapping, err error)
	GetAllWithDetail(ctx context.Context, companyProductID int) (res []domain.CompanyProductProviderChannelMapping, err error)
	PatchDataBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	UpdateBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	DeleteBulk(ctx context.Context, companyProductID int, data []domain.CompanyProductProviderChannelMapping) (res []domain.CompanyProductProviderChannelMapping, err error)
	// OPEN API
	GetOneWithDetailOpenAPI(ctx context.Context, auth, productCode, channelCode string) (res domain.CompanyProductProviderChannelMapping, err error)
	CalculateOpenAPI(ctx context.Context, auth, productCode, channelCode string, amount float64) (res domain.CompanyProductProviderChannelMappingCalculate, err error)
}
