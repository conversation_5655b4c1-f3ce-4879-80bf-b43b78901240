package snapbca

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

func (w *Wrapper) generateOauthSignature(ctx context.Context, cred Credential) (signature string, now time.Time, err error) {
	now = time.Now().In(timepkg.TimeJakartaLoc)

	xtimestamp := now.Format(time.RFC3339)

	signature, err = snaputil.GenerateAsymetricSignature(cred.PrivateKey(), cred.ClientID, xtimestamp)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error generate signature snap bca %s", err))
		return
	}

	return
}

func (w *Wrapper) buildOauthHeader(ctx context.Context, cred Credential) map[string]string {
	signature, now, _ := w.generateOauthSignature(ctx, cred)
	return map[string]string{
		headerTimestamp: now.Format(time.RFC3339),
		headerClientKey: cred.ClientID,
		headerSignature: signature,
	}
}

func (w *Wrapper) AccessTokenB2B(ctx context.Context, req AccessTokenB2BReq, cred Credential) (resp AccessTokenB2BResp, err error) {

	doReq := DoReq{
		Header: w.buildOauthHeader(ctx, cred),
		Method: http.MethodPost,
		Path:   "/openapi/v1.0/access-token/b2b",
	}
	doReq.ReqBody = req

	_, err = w.do(ctx, doReq, &resp)
	if err != nil {
		return
	}

	return
}

func (w *Wrapper) buildReqHeader(ctx context.Context, acccesToken string, req DoReq, cred Credential) error {
	reqURL, _ := url.Parse(w.client.BaseURL + req.Path)

	path := reqURL.Path + reqURL.Query().Encode()
	reqBodyB, _ := json.Marshal(req.ReqBody)
	xtimestampStr := time.Now().In(timepkg.TimeJakartaLoc).Format(time.RFC3339)

	signature, err := snaputil.GenerateSymetricSignature(acccesToken, req.Method, path, string(reqBodyB), xtimestampStr, cred.ClientSecret)
	if err != nil {
		logger.Error(ctx, "snaputil.GenerateSymetricSignature error", logger.Err(err))
		return err
	}
	req.Header[headerExternalID] = strings.ReplaceAll(uuid.NewString(), "-", "")
	req.Header[headerSignature] = signature
	req.Header[headerTimestamp] = xtimestampStr

	return nil
}
