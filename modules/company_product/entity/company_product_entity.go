package entity

type CompanyProductShow struct {
	ID                   int     `json:"id"`
	CompanyID            int     `json:"company_id"`
	ProductName          string  `json:"product_name"`
	ProductCode          string  `json:"product_code"`
	FeeFixValue          float64 `json:"fee_fix_value"`
	FeePercentage        float64 `json:"fee_percentage"`
	CashoutFeeFixValue   float64 `json:"cashout_fee_fix_value"`
	CashoutFeePercentage float64 `json:"cashout_fee_percentage"`
	CompanyName          string  `json:"company_name"`
	Status               bool    `json:"status"`
}
