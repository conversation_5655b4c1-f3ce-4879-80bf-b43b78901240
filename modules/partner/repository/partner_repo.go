package repository

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/partner/entity"
	"repo.nusatek.id/nusatek/payment/modules/partner/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
)

type partnerRepository struct {
	DB *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.PartnerRepository {
	return &partnerRepository{db}
}

func (ps *partnerRepository) BeginsTrans() *gorm.DB {
	return ps.DB.Begin()
}

func (ps *partnerRepository) GetPartnerByID(ctx context.Context, id string) (resp *domain.Partners, err error) {
	err = ps.DB.WithContext(ctx).First(&resp, id).Error
	return
}

func (ps *partnerRepository) GetByCompanyCode(ctx context.Context, code string) (res domain.Companies, exist bool) {
	var company domain.Companies

	rs := ps.DB.WithContext(ctx).Where("code = ? ", code).First(&company).RowsAffected
	if rs > 0 {
		return company, true
	} else {
		return company, false
	}
}

func (ps *partnerRepository) List(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (resp *[]entity.PartnerShow, totalItem int64, err error) {
	queryParam := func(db *gorm.DB) *gorm.DB {
		condition := db.Where("1=1 AND p.deleted_at IS NULL")
		if param["tag"] != nil {
			condition.Where("t.tag_id = ? AND t.deleted_at IS NULL", fmt.Sprint(param["tag"]))
		}

		if param["value"] != nil {
			value := "%" + fmt.Sprint(param["value"]) + "%"
			if param["key"] != nil {
				switch true {
				case param["key"] == "company_name":
					condition.Where("c.name ILIKE ?", value)
				case param["key"] == "partner_name":
					condition.Where("p.name ILIKE ?", value)
				case param["key"] == "partner_code":
					condition.Where("p.code ILIKE ?", value)
				case param["key"] == "phone_number":
					condition.Where("p.phone ILIKE ?", value)
				case param["key"] == "email":
					condition.Where("p.email ILIKE ?", value)
				case param["key"] == "payment_channel":
					condition.Where("n.name ILIKE ?", value)
				case param["key"] == "bank_account_number":
					condition.Where("p.ap_account ILIKE ?", value)
				case param["key"] == "account_receivable":
					condition.Where("p.ar_account ILIKE ?", value)
				case param["key"] == "fee":
					condition.Where("p.fee_value = ?", param["value"])
				case param["key"] == "sla":
					condition.Where("p.sla = ?", param["value"])
				case param["key"] == "status":
					condition.Where("p.status = ?", param["value"])
				case param["key"] == "company_id":
					condition.Where("p.company_id = ?", param["value"])
				default:
					condition.Where("CONCAT(p.name, ' ', c.name) ILIKE ?", value)
				}
			} else {
				condition.Where("p.code ILIKE ? OR p.name ILIKE ? OR n.name ILIKE ?", value, value, value)
			}
		}
		if param["search"] != nil {
			if param["search"].(string) != "" {
				searchValue := "%" + param["search"].(string) + "%"
				condition.Where("p.code ILIKE ? OR p.name ILIKE ? OR n.name ILIKE ?", searchValue, searchValue, searchValue)
			}
		}
		if param["status"] != nil {
			if param["status"].(string) != "" {
				condition.Where("p.status = ?", param["status"])
			}
		}

		return condition
	}

	page, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["start"]))
	limit, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["limit"]))

	joinQuery := "LEFT JOIN companies c ON p.company_id = c.id LEFT JOIN payment_channels n ON p.payment_channel_id = n.id LEFT JOIN partner_tags t ON p.id = t.partner_id"
	selectField := "DISTINCT(p.id), p.name AS partner, c.name AS company_partner, p.sla, c.code AS company_code, " +
		"p.phone, p.ar_account as account_receivable, p.ap_account as bank_account_number, p.fee_percentage, p.fee_fix_value, " +
		"p.status, p.code as partner_code, p.email, p.emails, p.is_send_email, n.name as payment_channel_name, p.payment_channel_id, p.fee_behind"

	err = ps.DB.WithContext(ctx).Debug().Scopes(utils.Paginate(page, limit)).Table("partners p").Select(selectField).
		Joins(joinQuery).Scopes(queryParam).Order("p.id DESC").Find(&resp).Error
	if err != nil {
		return
	}

	err = ps.DB.WithContext(ctx).Table("partners p").Joins(joinQuery).Scopes(queryParam).Group("p.id").Count(&totalItem).Error
	return
}

func (ps *partnerRepository) View(ctx context.Context, param map[string]interface{}) (resp *entity.PartnerShow, err error) {
	err = ps.DB.WithContext(ctx).
		Table("partners p").
		Select("p.id, p.name AS partner, c.name AS company_partner, p.sla, c.code AS company_code, "+
			"p.phone, p.ar_account as account_receivable, p.ap_account as bank_account_number, "+
			"p.fee_percentage, p.fee_fix_value, p.status, p.code as partner_code, p.email, p.emails, p.is_send_email, "+
			"n.name as payment_channel_name, p.payment_channel_id, p.fee_behind").
		Joins("LEFT JOIN companies c ON p.company_id = c.id").
		Joins("LEFT JOIN payment_channels n ON p.payment_channel_id = n.id").
		Where("p.id = ? AND p.deleted_at IS NULL", param["partner_id"]).
		Take(&resp).Error

	return
}

func (ps *partnerRepository) DeletePartner(ctx context.Context, req *domain.Partners) (err error) {
	tx := ps.DB.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("partner_id = ?", req.ID).Delete(&domain.PartnerTags{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (ps *partnerRepository) CreatePartner(ctx context.Context, req *domain.Partners, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(&req).Error
	return
}

func (ps *partnerRepository) UpdatePartner(ctx context.Context, req *domain.Partners, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Save(&req).Error
	return
}

func (ps *partnerRepository) PatchStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status bool) (partners []domain.Partners, err error) {
	err = tx.WithContext(ctx).Clauses(clause.Returning{}).Model(&domain.Partners{}).
		Where("id IN ?", ids).Update("status", status).Scan(&partners).Error
	return
}

func (ps *partnerRepository) ListHistory(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	var result []interface{}

	condition := "AND t.deleted_at IS NULL "
	if params["from"] != nil && params["to"] != nil {
		condition += "AND t.created_at BETWEEN @from AND @to "
	}

	type partner struct {
		BatchNumber  string    `json:"batch_number"`
		CreatedAt    time.Time `json:"created_at"`
		ProviderName string    `json:"provider_name"`
		ChannelName  string    `json:"channel_name"`
		ArAccount    string    `json:"ar_account"`
		AdminFee     float64   `json:"admin_fee"`
		Total        float64   `json:"total"`
		Status       string    `json:"status"`
	}
	var pr []partner

	type total struct {
		TotalItem int64 `json:"total_item"`
	}
	tot := new(total)

	query := "SELECT COUNT(t.id) AS total_item " +
		"FROM cash_out_transactions t " +
		"WHERE 1=1 " + condition

	err = ps.DB.WithContext(ctx).Raw(query, params).Scan(&tot).Error

	if err != nil {
		logger.Error(ctx, "err query", logger.Err(err))
		return nil, 0, err
	}

	query = "SELECT t.batch_number, t.created_at, y.name AS provider_name, l.name AS channel_name, p.ar_account, t.admin_fee, t.total, t.status " +
		"FROM cash_out_transactions t " +
		"LEFT JOIN companies c ON t.company_id = c.id " +
		"LEFT JOIN partners p ON t.partner_id = p.id " +
		"LEFT JOIN payment_providers y ON t.payment_provider_id = y.id " +
		"LEFT JOIN payment_channels l ON t.payment_channel_id = l.id " +
		"WHERE 1=1 " + condition +
		"ORDER BY t.created_at DESC " +
		"OFFSET @start LIMIT @limit "
	err = ps.DB.WithContext(ctx).Raw(query, params, orderParam).Scan(&pr).Error
	if err != nil {
		logger.Error(ctx, "err query", logger.Err(err))
	}

	for _, v := range pr {
		m := map[string]interface{}{
			"batch_number":  v.BatchNumber,
			"created_at":    v.CreatedAt,
			"provider_name": v.ProviderName,
			"channel_name":  v.ChannelName,
			"ar_account":    v.ArAccount,
			"admin_fee":     v.AdminFee,
			"total":         v.Total,
			"status":        v.Status,
		}
		result = append(result, m)
	}
	return result, tot.TotalItem, err
}

func (ps *partnerRepository) ViewHistory(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	var result []interface{}

	condition := "AND t.deleted_at IS NULL "

	type partner struct {
		BatchNumber    string `json:"batch_number"`
		SettlementDate string `json:"settlement_date"`
		Status         string `json:"status"`
		PaymentStatus  string `json:"payment_status"`
		Total          string `json:"total"`
		InvoiceNumber  string `json:"invoice_number"`
		CreatedAt      string `json:"created_at"`
	}
	var pr []partner

	type total struct {
		TotalItem int64 `json:"total_item"`
	}
	tot := new(total)

	query := "SELECT COUNT(t.id) AS total_item " +
		"FROM cash_out_transactions t " +
		"WHERE 1=1 AND t.batch_number = @batch_number " + condition

	err = ps.DB.WithContext(ctx).Raw(query, params).Scan(&tot).Error

	if err != nil {
		logger.Error(ctx, "err query", logger.Err(err))
		return nil, 0, err
	}

	query = "SELECT t.batch_number, i.created_at, i.invoice_number, i.total, h.status, h.payment_status, h.created_at AS settlement_date " +
		"FROM cash_out_transactions t " +
		"LEFT JOIN cash_out_transaction_details d ON t.id = d.cash_out_transaction_id " +
		"LEFT JOIN cash_in_transactions i ON d.cash_in_transaction_id = i.id " +
		"LEFT JOIN cash_in_transaction_histories h ON i.id = h.cash_in_transaction_id " +
		"WHERE 1=1 AND t.batch_number = @batch_number " + condition +
		"ORDER BY t.created_at DESC " +
		"OFFSET @start LIMIT @limit "
	err = ps.DB.WithContext(ctx).Raw(query, params, orderParam).Scan(&pr).Error
	if err != nil {
		logger.Error(ctx, "err query", logger.Err(err))
	}

	for _, v := range pr {
		m := map[string]interface{}{
			"batch_number":    v.BatchNumber,
			"settlement_date": v.SettlementDate,
			"status":          v.Status,
			"payment_status":  v.PaymentStatus,
			"total":           v.Total,
			"invoice_number":  v.InvoiceNumber,
			"created_at":      v.CreatedAt,
		}
		result = append(result, m)
	}
	return result, tot.TotalItem, err
}

func (ps *partnerRepository) GetByBatchNumber(ctx context.Context, batchNumber string) (res domain.CashOutTransactions, exist bool) {
	var cashOut domain.CashOutTransactions

	rs := ps.DB.WithContext(ctx).Where("batch_number = ? ", batchNumber).First(&cashOut).RowsAffected
	if rs > 0 {
		return cashOut, true
	} else {
		return cashOut, false
	}
}

func (r *partnerRepository) GetPartnerByCompanyId(ctx context.Context, companyId string) (resp *[]domain.Partners, err error) {
	err = r.DB.WithContext(ctx).Model(domain.Partners{}).Where("company_id = ?", companyId).Find(&resp).Error
	return
}

func (r *partnerRepository) GetPartnerByCode(ctx context.Context, code string) (resp *domain.Partners, err error) {
	err = r.DB.WithContext(ctx).Model(domain.Partners{}).Where("code = ?", code).Take(&resp).Error
	return
}

func (r *partnerRepository) UpdatePaymentChannelPartnerInCashOutTrx(ctx context.Context, req *domain.CashOutTransactions, partnerId, status string, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Where("partner_id = ? AND status = ?", partnerId, status).Updates(&req).Error
	return
}

func (r *partnerRepository) SelectByIds(ctx context.Context, ids []int) (resp []domain.Partners, err error) {
	err = r.DB.Where("id IN ?", ids).Table("partners").Order("created_at").Scan(&resp).Error

	return resp, err
}
