package handler

import (
	"context"
	"encoding/json"

	"github.com/streadway/amqp"
	"repo.nusatek.id/moaja/backend/libraries/logger"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/logutil"
)

func (h *WebhookHandler) WebhookCashInListener(c *amqp.Delivery) error {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerWebhookCashIn", logger.String("body", string(c.Body)))

	var req domain.CashinTransactionCallBack
	err := c.Ack(false)
	if err != nil {
		return err
	}

	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	if err = h.WebhookUseCase.CashInWebhook(ctx, req, c.Body); err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	return nil
}

func (h *WebhookHandler) WebhookCashOutListener(c *amqp.Delivery) error {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerWebhookCashOut", logger.String("body", string(c.Body)))

	var req domain.CashOutCallback
	err := c.Ack(false)
	if err != nil {
		return err
	}

	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	if err = h.WebhookUseCase.CashOutWebhook(ctx, req, c.Body); err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	return nil
}
