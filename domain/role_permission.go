package domain

import (
	"time"

	"gorm.io/gorm"
)

type RolePermissions struct {
	ID          int            `json:"id"`
	RoleId      int            `json:"role_id"`
	MenuId      int            `json:"menu_id"`
	Permissions string         `json:"permission"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}
