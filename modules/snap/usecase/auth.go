package usecase

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

func (uc defaultUC) GetOneClient(ctx context.Context, clientId string) (res domain.SnapClient, err error) {
	br := snaputil.NewBaseRespCtx(ctx)
	res, err = uc.repo.GetOneClient(ctx, clientId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error get client %v", err))
		err = br.Set(http.StatusUnauthorized, "00", "Unauthorized. [Unknown client]")
		return
	}

	return
}

func (uc defaultUC) ValidateExternalID(ctx context.Context, clientId, externalId string) (err error) {
	br := snaputil.NewBaseRespCtx(ctx)
	if len(externalId) <= 0 {
		logger.Error(ctx, fmt.Sprintf("empty external id"))
		err = br.Set(http.StatusBadRequest, "00", "Invalid Mandatory Field {X-EXTERNAL-ID}")
		return
	}

	loc, _ := time.LoadLocation(config.GetString("tz"))
	now := time.Now().In(loc)
	end := time.Date(now.Year(), now.Month(), now.Day(), 24, 0, 0, 0, loc)
	duration := end.Sub(now)
	ok, err := uc.cache.SetNX(ctx, fmt.Sprintf("snap_ext_id:%s_%s", clientId, externalId), "", duration).Result()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error save external id %v", err))
		err = br.Set(http.StatusInternalServerError, "00", "error validate external id")
		return
	}
	if !ok {
		logger.Error(ctx, fmt.Sprintf("external id %s is used", externalId))
		err = br.Set(http.StatusConflict, "00", "Conflict")
		return
	}

	return
}

func (uc defaultUC) validateExternalID(ctx context.Context, br *domain.SnapBaseResp) (ok bool, err error) {
	client := snaputil.GetClient(ctx)
	header := snaputil.GetHeaderReq(ctx)

	if len(header.XExternalId) <= 0 {
		logger.Error(ctx, "empty external id")
		err = br.Set(http.StatusBadRequest, "00", "Invalid Mandatory Field {X-EXTERNAL-ID}")
		return
	}

	loc, _ := time.LoadLocation(config.GetString("tz"))
	now := time.Now().In(loc)
	end := time.Date(now.Year(), now.Month(), now.Day(), 24, 0, 0, 0, loc)
	duration := end.Sub(now)
	ok, err = uc.cache.SetNX(ctx, fmt.Sprintf("snap_ext_id:%s_%s", client.Id, header.XExternalId), "", duration).Result()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error save external id %v", err))
		err = br.Set(http.StatusInternalServerError, "00", "error validate external id")
		return
	}
	if !ok {
		logger.Error(ctx, fmt.Sprintf("external id %s is used", header.XExternalId))
	}

	return
}

func (uc defaultUC) GetAccessTokenB2B(ctx context.Context, req domain.SnapAccesTokenB2BReq) (resp domain.SnapAccesTokenB2BResp, err error) {
	br := snaputil.NewBaseRespCtx(ctx)

	switch req.GrantType {
	case "client_credentials":
		client := snaputil.GetClient(ctx)

		exp := time.Duration(uc.snapSecretJWT.ExpiredSeconds) * time.Second

		var accessToken string

		accessToken, err = uc.generateToken(ctx, client.Id, uc.snapSecretJWT.Secret, exp)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error generate token %v", err))
			err = br.Set(http.StatusInternalServerError, "00", "General Error")
			return
		}

		resp = domain.SnapAccesTokenB2BResp{
			SnapBaseResp: br,
			AccessToken:  accessToken,
			TokenType:    "Bearer",
			ExpiresIn:    fmt.Sprint(int(exp / time.Second)),
		}
	default:
		err = br.Set(http.StatusBadRequest, "01", "Invalid field format [clientId/clientSecret/grantType]")
		return
	}

	return
}

func (uc defaultUC) generateToken(ctx context.Context, clientID, secretKey string, exp time.Duration) (token string, err error) {
	expirationTime := time.Now().Add(exp)

	claims := jwt.RegisteredClaims{
		ID:        clientID,
		ExpiresAt: &jwt.NumericDate{Time: expirationTime},
	}

	// Create token with claims
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(secretKey))
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error signed %v", err))
		return
	}

	return
}

func (uc defaultUC) ValidateToken(ctx context.Context, accessToken string) (claims *jwt.RegisteredClaims, err error) {
	br := snaputil.NewBaseRespCtx(ctx)
	claims = &jwt.RegisteredClaims{}
	_, err = jwt.ParseWithClaims(accessToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(uc.snapSecretJWT.Secret), nil
	})
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error jwt.ParseWithClaims %v", err))
		err = br.Set(http.StatusUnauthorized, "01", "Invalid Token (B2B)")
		return
	}

	if claims.ExpiresAt.Time.Before(time.Now()) {
		logger.Error(ctx, "jwt expired")
		err = br.Set(http.StatusUnauthorized, "00", "token expired")
		return
	}

	// client := snaputil.GetClient(ctx)

	// if claims.ID != client.ClientId {
	// 	logger.Error(ctx, "client not equal with jwt")
	// 	err = br.Set(http.StatusUnauthorized, "00", "invalid client")
	// 	return
	// }

	return
}
