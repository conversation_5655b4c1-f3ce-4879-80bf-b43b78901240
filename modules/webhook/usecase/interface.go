package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/webhook/entity"
)

type WebhookService interface {
	GetWebhookDeliveryLogs(ctx context.Context, req *entity.WebhookLogPaginate) ([]entity.WebhookDeliveryRespose, int64, error)
	GetWebhookDeliveryLogHistory(ctx context.Context, id int64, req *entity.WebhookDeliveryLogHistoryPaginate) ([]*entity.WebhookDeliveryRespose, int64, error)
	GetWebhookDeliveryLogById(ctx context.Context, id int64) (*entity.WebhookDeliveryRespose, error)

	VerifyEndpoint(ctx context.Context, req *entity.WebhookRequest) (*entity.WebhookVerifyResponse, error)
	CashInWebhook(ctx context.Context, cashInCallback domain.CashinTransactionCallBack, payload []byte) error
	CashOutWebhook(ctx context.Context, cashOutCallback domain.CashOutCallback, payload []byte) error
}
