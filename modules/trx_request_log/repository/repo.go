package repository

import (
	"context"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/trx_request_log/entity"
	"repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
)

type repo struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.TrxRequestLogRepository {
	return &repo{
		db: db,
	}
}

func (r *repo) Insert(ctx context.Context, data *domain.TrxRequestLog) (err error) {
	row := entity.TrxRequestLog{}
	row.FromDomain(data)
	// err = r.db.Clauses(clause.Returning{}).Create(data).Error
	err = r.db.Create(&row).Error
	if err != nil {
		logger.Error(ctx, "error insert trx request log", zap.Error(err))
		return err
	}

	data.Id = row.Id.String()
	return
}

func (r *repo) UpdateResource(ctx context.Context, data *domain.TrxRequestLog) (err error) {
	row := entity.TrxRequestLog{}
	row.FromDomain(data)

	err = r.db.Table(row.TableName()).Select("resource_id", "resource_type").
		Where("id = ?", data.Id).Updates(&row).Error
	if err != nil {
		logger.Error(ctx, "error update trx request log", zap.Error(err))
		return err
	}

	return
}

func (r *repo) UpdateResponse(ctx context.Context, data *domain.TrxRequestLog) (err error) {
	row := entity.TrxRequestLog{}
	row.FromDomain(data)

	err = r.db.Table(row.TableName()).Select("response_status", "response_body").
		Where("id = ?", data.Id).Updates(&row).Error
	if err != nil {
		logger.Error(ctx, "error update trx request log", zap.Error(err))
		return err
	}

	return
}
