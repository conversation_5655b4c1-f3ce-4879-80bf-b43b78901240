package main

import (
	"context"
	"log"
	"time"

	"repo.nusatek.id/nusatek/payment/infrastructure/telegram"
)

func main() {
	// default configuration
	// err := telegram.SendTelegram(telegram.Message{
	// 	CompanyName:       "Example Corp",
	// 	BatchNumber:       "BATCH123",
	// 	PartnerName:       "Partner Inc",
	// 	TotalDisbursement: 1000.00,
	// 	FailedEmail:       "<EMAIL>",
	// 	ErrorMessage:      "Connection timeout",
	// })
	// if err != nil {
	// 	log.Printf("Failed to send telegram: %v", err)
	// }

	// custom configuration
	config := telegram.Config{
		APIEndpoint: "https://custom.endpoint/send",
		BearerToken: "custom-token",
		ChatID:      "custom-chat-id",
		HTTPTimeout: 15 * time.Second,
	}

	service, err := telegram.NewService(config)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	ctx := context.Background()
	err = service.SendErrorNotification(ctx, telegram.Message{
		CompanyName:       "Example Corp",
		BatchNumber:       "BATCH123",
		PartnerName:       "Partner Inc",
		TotalDisbursement: "1000.00",
		FailedEmail:       "<EMAIL>",
		ErrorMessage:      "Connection timeout",
	})
	if err != nil {
		log.Printf("Failed to send notification: %v", err)
	}
}
