package entity

import (
	"time"

	"gorm.io/gorm"
)

type CompanySecret struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type CompanyShow struct {
	ID              int            `json:"id"`
	ParentCompanyID *int           `json:"parent_company_id"`
	Code            string         `json:"code"`
	Name            string         `json:"name"`
	Email           string         `json:"email"`
	Alias           string         `json:"alias"`
	Status          bool           `json:"status"`
	Secrets         string         `json:"secrets"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
