package repository

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type EmailLogRepository interface {
	GetEmailLogs(ctx context.Context, payload *entity.EmailLogPaginate) ([]entity.EmailLog, int64, error)
	GetEmailLogDetail(ctx context.Context, id int64, param map[string]interface{}, orderParam map[string]interface{}) ([]entity.EmailLog, error)
	GetEmailLogById(ctx context.Context, id int64) (*entity.EmailLog, error)
	GetCashoutEmailLogById(ctx context.Context, id int64) (*entity.CashoutEmailLog, error)
	CreateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error)
	UpdateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error)
	DeleteEmailLog(ctx context.Context, id int64) error
	IsExistPartnerByEmail(ctx context.Context, email string) (bool, error)
	GetEmailLogStatusExists(ctx context.Context, emailLog *entity.EmailLog, status string) (bool, error)
	BeginsTrans() *gorm.DB
}

type emailLogRepository struct {
	DB *gorm.DB
}

func NewEmailLogRepository(db *gorm.DB) EmailLogRepository {
	return &emailLogRepository{DB: db}
}

func (r *emailLogRepository) BeginsTrans() *gorm.DB {
	return r.DB.Begin()
}

func (r *emailLogRepository) GetEmailLogs(ctx context.Context, payload *entity.EmailLogPaginate) ([]entity.EmailLog, int64, error) {
	var emailLogs []entity.EmailLog
	var totalData int64
	paginate := payload.Pagination

	query := func(db *gorm.DB) *gorm.DB {
		var condition = db.Where("email_logs.parent_id IS NULL") // Only fetch top-level logs

		if paginate.Search != "" {
			condition = condition.Where("email_logs.email ILIKE ? or email_logs.batch_number ILIKE ?", "%"+paginate.Search+"%", "%"+paginate.Search+"%")
		}

		if payload.Status != "" {
			condition = condition.Where("email_logs.status = ?", payload.Status)
		}

		if payload.Email != "" {
			condition = condition.Where("email_logs.email = ?", payload.Email)
		}

		if payload.BatchNumber != "" {
			condition = condition.Where("email_logs.batch_number = ?", payload.BatchNumber)
		}

		if payload.Company != 0 {
			condition = condition.Where("email_logs.company_id = ?", payload.Company)
		}

		if payload.Partner != 0 {
			condition = condition.Where("email_logs.partner_id = ?", payload.Partner)
		}

		startDate, endDate := payload.DateFilter()
		if startDate != "" && endDate != "" {
			condition = condition.Where("email_logs.created_at BETWEEN ? AND ?", startDate, endDate)
		}

		return condition
	}

	err := r.DB.WithContext(ctx).
		Scopes(utils.Paginate(paginate.Page, paginate.Limit)).
		Preload("Company").
		Preload("Partner").
		Joins("LEFT JOIN companies ON companies.id = email_logs.company_id").
		Joins("LEFT JOIN partners ON partners.id = email_logs.partner_id").
		Scopes(query).
		Scopes(getOrderBy(paginate.GetOrderBy(""), "email_logs.created_at desc")).
		Find(&emailLogs).Error
	if err != nil {
		return nil, 0, err
	}

	// Count only top-level logs
	err = r.DB.WithContext(ctx).Scopes(query).Model(&entity.EmailLog{}).Count(&totalData).Error
	if err != nil {
		return nil, 0, err
	}

	return emailLogs, totalData, nil
}

func getOrderBy(sort string, defaultSort string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if sort == "" {
			if defaultSort != "" {
				return db.Order(defaultSort)
			}
			return db
		}

		s := strings.Split(sort, " ")
		direction := "ASC"

		if len(s) > 1 {
			direction = s[1]
			sort = s[0]
		}

		// Map of allowed sort fields to their corresponding database columns
		sortFields := map[string]string{
			"company.name": "companies.name",
			"partner.name": "partners.name",
			"batch_number": "email_logs.batch_number",
			"email":        "email_logs.email",
			"status":       "email_logs.status",
			"created_at":   "email_logs.created_at",
			"updated_at":   "email_logs.updated_at",
		}

		if dbField, ok := sortFields[sort]; ok {
			return db.Order(fmt.Sprintf("%s %s", dbField, direction))
		}

		// If invalid sort field, fall back to default sort
		if defaultSort != "" {
			return db.Order(defaultSort)
		}
		return db
	}
}

func (r *emailLogRepository) GetEmailLogDetail(ctx context.Context, id int64, param map[string]interface{}, orderParam map[string]interface{}) ([]entity.EmailLog, error) {
	var emailLogs []entity.EmailLog

	queryParam := func(db *gorm.DB) *gorm.DB {
		if param["value"] != nil {
			value := "%" + fmt.Sprint(param["value"]) + "%"
			if param["key"] != nil {
				switch true {
				case param["key"] == "batch_number":
					db = db.Where("email_logs.batch_number ILIKE ?", value) // Mengacu ke tabel email_logs
				case param["key"] == "email":
					db = db.Where("email_logs.email ILIKE ?", value) // Mengacu ke tabel email_logs
				default:
					db = db.Where("CONCAT(email_logs.batch_number, ' ', email_logs.email) ILIKE ?", value) // Mengacu ke tabel email_logs
				}
			}
		}

		if param["search"] != nil {
			if param["search"].(string) != "" {
				searchValue := "%" + param["search"].(string) + "%"
				db = db.Where("email_logs.batch_number ILIKE ? OR email_logs.batch_number ILIKE ?", searchValue, searchValue) // Mengacu ke tabel email_logs
			}
		}

		return db
	}

	page, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["start"]))
	limit, _ := strconv.Atoi(fmt.Sprintf("%v", orderParam["limit"]))

	// First, check if the specified log exists
	var parentLog entity.EmailLog
	err := r.DB.WithContext(ctx).First(&parentLog, id).Error
	if err != nil {
		return nil, err
	}

	err = r.DB.WithContext(ctx).
		Preload("Company").
		Preload("Partner").
		Scopes(queryParam).
		Where("id = ? OR parent_id = ?", id, id).
		Order("created_at DESC").
		Limit(limit).
		Offset((page - 1) * limit).
		Find(&emailLogs).Error

	if err != nil {
		return nil, err
	}

	return emailLogs, nil
}

func (r *emailLogRepository) GetEmailLogById(ctx context.Context, id int64) (*entity.EmailLog, error) {
	var emailLog entity.EmailLog

	err := r.DB.WithContext(ctx).Table("email_logs").First(&emailLog, id).Error
	if err != nil {
		return nil, err
	}

	return &emailLog, nil
}

func (r *emailLogRepository) GetCashoutEmailLogById(ctx context.Context, id int64) (*entity.CashoutEmailLog, error) {
	var cashoutEmailLog entity.CashoutEmailLog

	err := r.DB.WithContext(ctx).Table("cashout_email_logs").First(&cashoutEmailLog, id).Error
	if err != nil {
		return nil, err
	}
	return &cashoutEmailLog, nil
}

func (r *emailLogRepository) IsExistPartnerByEmail(ctx context.Context, email string) (bool, error) {
	var count int64
	// the table is array emails, use query to check if the email is exist in array field emails
	err := r.DB.WithContext(ctx).Table("partners").Where("emails @> ARRAY[?]", email).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *emailLogRepository) GetEmailLogStatusExists(ctx context.Context, emailLog *entity.EmailLog, status string) (bool, error) {
	var count int64

	err := r.DB.WithContext(ctx).
		Table("email_logs").
		Where("company_id = ? AND partner_id = ? AND batch_number = ? AND status = ? AND email = ?",
			emailLog.CompanyId, emailLog.PartnerId, emailLog.BatchNumber, status, emailLog.Email).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *emailLogRepository) CreateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// get cashout log id
	var cashOutLogId int64
	err := tx.Table("cashout_email_logs").
		Where("company_id = ? AND partner_id = ? AND batch_number = ? AND email = ?", emailLog.CompanyId, emailLog.PartnerId, emailLog.BatchNumber, emailLog.Email).
		Select("id").Scan(&cashOutLogId).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if cashOutLogId == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("cashout email log not found for email %s in batch number %s", emailLog.Email, emailLog.BatchNumber)
	}

	// Create the new log
	emailLog.CashoutEmailLogId = &cashOutLogId
	if err := tx.Create(emailLog).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Find all previous logs with the same company_id, partner_id, batch_number, and email
	var previousLogs []entity.EmailLog
	if err := tx.Where("company_id = ? AND partner_id = ? AND batch_number = ? AND email = ? AND id != ?",
		emailLog.CompanyId, emailLog.PartnerId, emailLog.BatchNumber, emailLog.Email, emailLog.Id).
		Find(&previousLogs).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Update all previous logs to set the new log as their parent
	if len(previousLogs) > 0 {
		if err := tx.Model(&entity.EmailLog{}).
			Where("id IN ?", getIDs(previousLogs)).
			Update("parent_id", emailLog.Id).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// update status to cashout_email_logs
	if err := tx.Table("cashout_email_logs").Where("id = ?", cashOutLogId).Update("last_status", string(emailLog.Status)).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Fetch the created log with its children
	var createdLog entity.EmailLog
	err = r.DB.Preload("Children").First(&createdLog, emailLog.Id).Error
	if err != nil {
		return nil, err
	}

	return &createdLog, nil
}

// Helper function to extract IDs from a slice of EmailLog
func getIDs(logs []entity.EmailLog) []int64 {
	ids := make([]int64, len(logs))
	for i, log := range logs {
		ids[i] = log.Id
	}
	return ids
}

func (r *emailLogRepository) UpdateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update the email log
	if err := tx.Save(emailLog).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Fetch the updated log
	var updatedLog entity.EmailLog
	err := r.DB.First(&updatedLog, emailLog.Id).Error
	if err != nil {
		return nil, err
	}

	return &updatedLog, nil
}

func (r *emailLogRepository) DeleteEmailLog(ctx context.Context, id int64) error {
	return r.DB.WithContext(ctx).Delete(&entity.EmailLog{}, id).Error
}
