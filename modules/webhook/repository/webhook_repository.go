package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/modules/webhook/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type WebhookDeliveryLogRepository interface {
	CreateWebhookDeliveryLog(ctx context.Context, log *entity.WebhookDeliveryLog) error
	GetParentWebhookDeliveryLogs(ctx context.Context, log *entity.WebhookDeliveryLog) (*entity.WebhookDeliveryLog, error)
	GetWebhookDeliveryLogs(ctx context.Context, payload *entity.WebhookLogPaginate) ([]entity.WebhookDeliveryLog, int64, error)
	GetWebhookDeliveryLogHistory(ctx context.Context, id int64, payload *entity.WebhookDeliveryLogHistoryPaginate) ([]entity.WebhookDeliveryLog, int64, error)
	GetWebhookDeliveryLogById(ctx context.Context, id int64) (*entity.WebhookDeliveryLog, error)
	UpdateWebhookDeliveryLog(ctx context.Context, log *entity.WebhookDeliveryLog) (*entity.WebhookDeliveryLog, error)
	DeleteWebhookDeliveryLog(ctx context.Context, id int64) error
	BeginsTrans() *gorm.DB
}

type webhookDeliveryLogRepository struct {
	DB *gorm.DB
}

func NewWebhookDeliveryLogRepository(db *gorm.DB) WebhookDeliveryLogRepository {
	return &webhookDeliveryLogRepository{DB: db}
}

func (r *webhookDeliveryLogRepository) BeginsTrans() *gorm.DB {
	return r.DB.Begin()
}

// CreateWebhookDeliveryLog creates a new webhook delivery log and updates parent-child relationships.
func (r *webhookDeliveryLogRepository) CreateWebhookDeliveryLog(ctx context.Context, log *entity.WebhookDeliveryLog) error {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Find previous logs with the same criteria
	previousLogs, err := r.findPreviousLogs(tx, log)
	if err != nil {
		tx.Rollback()
		return err
	}

	// Create the new log
	if err := tx.Create(log).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update previous logs to set the new log as their parent
	if len(previousLogs) > 0 {
		if err := tx.Model(&entity.WebhookDeliveryLog{}).
			Where("id IN ?", getIDs(previousLogs)).
			Update("parent_id", log.Id).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetParentWebhookDeliveryLogs retrieves the parent log for a given webhook delivery log.
func (r *webhookDeliveryLogRepository) GetParentWebhookDeliveryLogs(ctx context.Context, log *entity.WebhookDeliveryLog) (*entity.WebhookDeliveryLog, error) {
	var parentLog entity.WebhookDeliveryLog
	err := r.DB.WithContext(ctx).
		Where("company_id = ? AND invoice_number = ? AND event_type = ? AND endpoint = ? AND (parent_id IS NULL OR parent_id <= 0)",
			log.CompanyId, log.InvoiceNumber, log.EventType, log.Endpoint).
		First(&parentLog).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &entity.WebhookDeliveryLog{}, nil
		}
		return nil, fmt.Errorf("failed to get parent webhook delivery log: %w", err)
	}
	return &parentLog, nil
}

// GetWebhookDeliveryLogs retrieves paginated webhook delivery logs based on filters.
func (r *webhookDeliveryLogRepository) GetWebhookDeliveryLogs(ctx context.Context, payload *entity.WebhookLogPaginate) ([]entity.WebhookDeliveryLog, int64, error) {
	var logs []entity.WebhookDeliveryLog
	var total int64

	query := r.buildQuery(payload)
	err := r.DB.WithContext(ctx).
		Scopes(utils.Paginate(payload.Pagination.Page, payload.Pagination.Limit)).
		Preload("Company").
		Joins("LEFT JOIN companies ON companies.id = webhook_delivery_logs.company_id").
		Scopes(query).
		Scopes(getOrderBy(payload.Pagination.GetOrderBy(""), "webhook_delivery_logs.created_at desc")).
		Where("parent_id IS NULL OR parent_id <= 0").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	// Count total logs
	err = r.DB.WithContext(ctx).Scopes(query).Model(&entity.WebhookDeliveryLog{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetWebhookDeliveryLogDetail retrieves detailed logs for a specific webhook delivery log.
func (r *webhookDeliveryLogRepository) GetWebhookDeliveryLogHistory(ctx context.Context, id int64, payload *entity.WebhookDeliveryLogHistoryPaginate) ([]entity.WebhookDeliveryLog, int64, error) {
	var logs []entity.WebhookDeliveryLog
	var total int64

	query := r.buildHistoryQuery(payload)

	err := r.DB.WithContext(ctx).
		Scopes(utils.Paginate(payload.Pagination.Page, payload.Pagination.Limit)).
		Preload("Company").
		Joins("LEFT JOIN companies ON companies.id = webhook_delivery_logs.company_id").
		Scopes(query).
		Scopes(getOrderBy(payload.Pagination.GetOrderBy(""), "webhook_delivery_logs.created_at desc")).
		Where("webhook_delivery_logs.id = ? OR webhook_delivery_logs.parent_id = ?", id, id).
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	// Count total logs
	err = r.DB.WithContext(ctx).Scopes(query).Model(&entity.WebhookDeliveryLog{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetWebhookDeliveryLogById retrieves a webhook delivery log by its ID.
func (r *webhookDeliveryLogRepository) GetWebhookDeliveryLogById(ctx context.Context, id int64) (*entity.WebhookDeliveryLog, error) {
	var log entity.WebhookDeliveryLog
	err := r.DB.WithContext(ctx).Preload("Company").First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// UpdateWebhookDeliveryLog updates an existing webhook delivery log.
func (r *webhookDeliveryLogRepository) UpdateWebhookDeliveryLog(ctx context.Context, log *entity.WebhookDeliveryLog) (*entity.WebhookDeliveryLog, error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Save(log).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return log, nil
}

// DeleteWebhookDeliveryLog deletes a webhook delivery log by its ID.
func (r *webhookDeliveryLogRepository) DeleteWebhookDeliveryLog(ctx context.Context, id int64) error {
	return r.DB.WithContext(ctx).Delete(&entity.WebhookDeliveryLog{}, id).Error
}

// Helper Functions

// buildQuery constructs the query for filtering webhook delivery logs.
func (r *webhookDeliveryLogRepository) buildQuery(payload *entity.WebhookLogPaginate) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		query := db.Where("webhook_delivery_logs.parent_id IS NULL")

		if payload.Pagination.Search != "" {
			query = query.Where("companies.company_name ILIKE ? OR webhook_delivery_logs.invoice_number ILIKE ?",
				"%"+payload.Pagination.Search+"%", "%"+payload.Pagination.Search+"%")
		}

		if payload.EventType != "" {
			query = query.Where("webhook_delivery_logs.event_type = ?", payload.EventType)
		}

		if payload.Status != "" {
			query = query.Where("webhook_delivery_logs.status = ?", payload.Status)
		}

		if payload.InvoiceNumber != "" {
			query = query.Where("webhook_delivery_logs.invoice_number = ?", payload.InvoiceNumber)
		}

		if payload.Company != 0 {
			query = query.Where("webhook_delivery_logs.company_id = ?", payload.Company)
		}

		startDate, endDate := payload.DateFilter()
		if startDate != "" && endDate != "" {
			query = query.Where("webhook_delivery_logs.created_at BETWEEN ? AND ?", startDate, endDate)
		}

		return query
	}
}

// buildDetailQuery constructs the query for detailed log retrieval.
func (r *webhookDeliveryLogRepository) buildHistoryQuery(payload *entity.WebhookDeliveryLogHistoryPaginate) func(db *gorm.DB) *gorm.DB {
	return func(query *gorm.DB) *gorm.DB {
		if payload.Pagination.Search != "" {
			query = query.Where("companies.company_name ILIKE ? OR webhook_delivery_logs.invoice_number ILIKE ?",
				"%"+payload.Pagination.Search+"%", "%"+payload.Pagination.Search+"%")
		}

		if payload.Status != "" {
			query = query.Where("webhook_delivery_logs.status = ?", payload.Status)
		}

		startDate, endDate := payload.DateFilter()
		if startDate != "" && endDate != "" {
			query = query.Where("webhook_delivery_logs.created_at BETWEEN ? AND ?", startDate, endDate)
		}

		return query
	}
}

// findPreviousLogs finds logs with the same criteria as the given log.
func (r *webhookDeliveryLogRepository) findPreviousLogs(db *gorm.DB, log *entity.WebhookDeliveryLog) ([]entity.WebhookDeliveryLog, error) {
	var logs []entity.WebhookDeliveryLog
	err := db.Where("company_id = ? AND invoice_number = ? AND endpoint = ? AND event_type = ?",
		log.CompanyId, log.InvoiceNumber, log.Endpoint, log.EventType).
		Find(&logs).Error
	return logs, err
}

// getIDs extracts IDs from a slice of WebhookDeliveryLog.
func getIDs(logs []entity.WebhookDeliveryLog) []int64 {
	ids := make([]int64, len(logs))
	for i, log := range logs {
		ids[i] = log.Id
	}
	return ids
}

// getOrderBy constructs the order clause for queries.
func getOrderBy(sort string, defaultSort string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if sort == "" {
			return db.Order(defaultSort)
		}

		s := strings.Split(sort, " ")
		direction := "ASC"
		if len(s) > 1 {
			direction = s[1]
			sort = s[0]
		}

		sortFields := map[string]string{
			"company.name":   "companies.name",
			"invoice_number": "webhook_delivery_logs.invoice_number",
			"event":          "webhook_delivery_logs.event_type",
			"status":         "webhook_delivery_logs.status",
			"created_at":     "webhook_delivery_logs.created_at",
			"updated_at":     "webhook_delivery_logs.updated_at",
		}

		if dbField, ok := sortFields[sort]; ok {
			return db.Order(fmt.Sprintf("%s %s", dbField, direction))
		}

		return db.Order(defaultSort)
	}
}
