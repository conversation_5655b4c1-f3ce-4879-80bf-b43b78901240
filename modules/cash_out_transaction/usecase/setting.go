package usecase

import (
	"github.com/go-redis/redis/v8"

	emailclient "repo.nusatek.id/nusatek/payment/infrastructure/email_client"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	cashin "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/usecase"
	cashinUc "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/usecase"
	channelMapping "repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	company "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	companyMapping "repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	compproduct "repo.nusatek.id/nusatek/payment/modules/company_product/usecase"
	emailtemplate "repo.nusatek.id/nusatek/payment/modules/configuration/repository"
	partner "repo.nusatek.id/nusatek/payment/modules/partner/usecase"
	channel "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	trxReqLog "repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
	user "repo.nusatek.id/nusatek/payment/modules/user_management/usecase"
)

type defaultCashOutUsecase struct {
	cashOutRepository     CashOutRepository
	providerService       usecase.PaymentProviderUsecase
	channelRepository     channel.PaymentChannelRepository
	xfersRest             xfers.RestXfers
	cashInRepo            cashin.CashinTranscationRepo
	companyMappingRepo    companyMapping.CompanyMappingRepository
	cache                 *redis.Client
	companyMappingService companyMapping.CompanyMappingUsecase
	userService           user.UserManagementUsecase
	partnerRepo           partner.PartnerRepository
	comProductRepo        compproduct.CompanyProductRepo
	xenditRest            xendit.RestXendit
	channelMappingRepo    channelMapping.ChannelMappingRepository
	companyService        company.CompanyManagementUsecase
	userRepo              user.UserManagementRepository
	emailClient           emailclient.IEmailClient
	cashinUc              cashinUc.CashinTransactionUsecase
	trxReqLogService      trxReqLog.TrxRequestLogUseCase
	emailTemplate         emailtemplate.EmailTemplateRepository
}

func Setup() *defaultCashOutUsecase {
	return &defaultCashOutUsecase{}
}

func (s *defaultCashOutUsecase) SetCashOutRepo(t CashOutRepository) *defaultCashOutUsecase {
	s.cashOutRepository = t
	return s
}

func (s *defaultCashOutUsecase) SetPaymentProviderService(t usecase.PaymentProviderUsecase) *defaultCashOutUsecase {
	s.providerService = t
	return s
}

func (s *defaultCashOutUsecase) SetChannelRepo(t channel.PaymentChannelRepository) *defaultCashOutUsecase {
	s.channelRepository = t
	return s
}

func (s *defaultCashOutUsecase) SetXfersRest(t xfers.RestXfers) *defaultCashOutUsecase {
	s.xfersRest = t
	return s
}

func (s *defaultCashOutUsecase) SetCashinRepo(t cashin.CashinTranscationRepo) *defaultCashOutUsecase {
	s.cashInRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetCompanyMappingRepo(t companyMapping.CompanyMappingRepository) *defaultCashOutUsecase {
	s.companyMappingRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetRedisClient(t *redis.Client) *defaultCashOutUsecase {
	s.cache = t
	return s
}

func (s *defaultCashOutUsecase) SetCompanyMappingService(t companyMapping.CompanyMappingUsecase) *defaultCashOutUsecase {
	s.companyMappingService = t
	return s
}

func (s *defaultCashOutUsecase) SetUserManagementService(t user.UserManagementUsecase) *defaultCashOutUsecase {
	s.userService = t
	return s
}

func (s *defaultCashOutUsecase) SetPartnerRepo(t partner.PartnerRepository) *defaultCashOutUsecase {
	s.partnerRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetComProductRepo(t compproduct.CompanyProductRepo) *defaultCashOutUsecase {
	s.comProductRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetXenditRest(t xendit.RestXendit) *defaultCashOutUsecase {
	s.xenditRest = t
	return s
}

func (s *defaultCashOutUsecase) SetChannelMappingRepo(t channelMapping.ChannelMappingRepository) *defaultCashOutUsecase {
	s.channelMappingRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetCompanyService(t company.CompanyManagementUsecase) *defaultCashOutUsecase {
	s.companyService = t
	return s
}

func (s *defaultCashOutUsecase) SetUserManagementRepo(t user.UserManagementRepository) *defaultCashOutUsecase {
	s.userRepo = t
	return s
}

func (s *defaultCashOutUsecase) SetEmailClient(t emailclient.IEmailClient) *defaultCashOutUsecase {
	s.emailClient = t
	return s
}

func (s *defaultCashOutUsecase) SetCashinUc(cashinUcPub cashinUc.CashinTransactionUsecase) *defaultCashOutUsecase {
	s.cashinUc = cashinUcPub
	return s
}

func (s *defaultCashOutUsecase) SetTrxReqLogService(trxReqLogService trxReqLog.TrxRequestLogUseCase) *defaultCashOutUsecase {
	s.trxReqLogService = trxReqLogService
	return s
}

func (s *defaultCashOutUsecase) SetEmailTemplateRepo(t emailtemplate.EmailTemplateRepository) *defaultCashOutUsecase {
	s.emailTemplate = t
	return s
}

func (s *defaultCashOutUsecase) Validate() CashOutUseCase {
	if s.cashOutRepository == nil {
		panic("cash out repo is nil")
	}

	if s.providerService == nil {
		panic("payment provider service is nil")
	}

	if s.channelRepository == nil {
		panic("channel repo is nil")
	}

	if s.xfersRest == nil {
		panic("xfers rest is nil")
	}

	if s.cashInRepo == nil {
		panic("cash in repo is nil")
	}

	if s.companyMappingRepo == nil {
		panic("company mapping repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.companyMappingService == nil {
		panic("company mapping service is nil")
	}

	if s.userService == nil {
		panic("user service is nil")
	}

	if s.partnerRepo == nil {
		panic("partner repo is nil")
	}

	if s.xenditRest == nil {
		panic("xendit service is nil")
	}

	if s.channelMappingRepo == nil {
		panic("channel mapping repo is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	if s.userRepo == nil {
		panic("user repo is nil")
	}

	return s
}
