package utils

import (
	"encoding/base32"
	"fmt"

	auth "github.com/dgryski/dgoogauth"
)

type defaultConfig struct {
	config auth.OTPConfig
} 

func SetupGoogleAutorization(secret string) *defaultConfig {
	secretBase32 := base32.StdEncoding.EncodeToString([]byte(secret))
	conn := auth.OTPConfig{
		Secret:      secretBase32,
		WindowSize:  3,
		HotpCounter: 0,
		UTC:         false,
	}
	return &defaultConfig{conn}
}

func (c *defaultConfig) GenerateGoogleAuthQR(user, issuer string) string {
	qrString := c.config.ProvisionURIWithIssuer(user, issuer)
	return qrString
}

func (c *defaultConfig) AuthentificationGoogleAuth(otp string) (bool, error) {
	isValid, err := c.config.Authenticate(otp)
	if err != nil {
		return false, fmt.Erro<PERSON>("%s", err.<PERSON><PERSON>r())
	}

	return isValid, nil
}