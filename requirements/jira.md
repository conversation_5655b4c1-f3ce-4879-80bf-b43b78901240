Documentation API Contract / Spec OttoPay

File PDF →  Pass: OttoDigital2023

Berikut company code (prefix) dan sub-company code (sub-prefix) yang dapat digunakan dalam development:

Merchant

VA Bank

Channel Type

Company Code (Prefix)

Sub Company Code (Sub Prefix)
opsional

Customer Number

Moaja

Mandiri

BMRI

71101

409

xxxxxxxxxxx

BRI

BRI

15772

453

xxxxxxxxxxx

BINA

BINA

98192

501

xxxxxxxxxxx

BNI

BNI

8428

399

xxxxxxxxxxx

Ket:

Virtual Account = [Prefix] + [CustomerNumber]

Customer Number adalah Digit yang dapat dikonfigurasi oleh tim <PERSON> (String (max 11))

Suggest: Better tidak perlu pakai Subprefix agar Customer Number bisa kita gunakan phone number

Ex: Nomor user ************, maka: ****************

Jika nomor ada 13 digit, maka ambil 11 digit dari belakang. Ex: 085**********, maka: 711015**********

Whitelist IP

Mohon ditambahkan ke daftar whitelist, IP-Publik OttoPay:

************
*************

Informasi Simulator Bayar

URL : https://portal.ottodigital.id/

Username : MOA

Password : MOA123

Adjustment

Provider Channel

Menambahkan Secret Template, untuk Prefix masing-masing Channel:

secret template name

secret template type

Channel Type akan diinput pada field “Provider Channel Code“

Company Provider

Menambahkan field pada “Update Provider Secret” sesuai yang ditambahkan pada Provider Channel, dengan jumlah sesuai yang diintegrasikan (saat ini ada 4 channel)

Prefix untuk Mandiri

Prefix untuk BRI

Prefix untuk BINA

Prefix untuk BNI

Prefix ini berfungsi ketika ada inquiry dari OttoPay (setelah proses authentication), PS membaca Channel Type ini milik Company mana di PS dengan mencocokkan Company Code (Prefix) yang sudah di set tersebut, kemudian memberikan response berupa isi tagihannya.

Inquiry request:

Inquiry response: (sesuai dengan panduan Inquiry Status pada docs)

Cash In

Tambah button Paid Manual, dimana bisa mengubah status Failed/Expired menjadi Paid.

Handling jika transaksi masih Pending, namun di OttoPay sudah terbayar  

Functional Requirement

Title A: Merchant menggunakan API Akun Virtual OttoPay (Non-Billing VA Integration)
Functional Requirements (A) – Interaction Flow

No.

Requirement

Description

FR-1

Checkout Initiation

Customer melakukan proses checkout berdasarkan channel yang sudah terdaftar dan telah dipetakan dengan kode channel pembayaran OttoPay.

FR-2

Generate Payment Code (VA)

Merchant Backend menghasilkan kode pembayaran (VA) dan menampilkannya ke customer. (Terkait proses pada FR-B)

FR-3

Token Retrieval from Merchant

OttoPay harus memanggil endpoint backend Merchant untuk mendapatkan token autentikasi sebelum memproses inquiry atau payment.

FR-4

Forward Inquiry Request

OttoPay menerima permintaan inquiry dari Issuer VA, lalu meneruskannya ke endpoint inquiry Merchant Backend.

FR-5

Inquiry Response from Merchant

Merchant memberikan respons inquiry dengan data virtual account, seperti nama pelanggan, deskripsi, dan nominal (jika tersedia).

FR-6

Return Inquiry Response

OttoPay meneruskan response inquiry ke Issuer VA sesuai format yang disepakati.

FR-7

Receive Payment Request

Issuer VA mengirim permintaan pembayaran ke OttoPay setelah customer melakukan pembayaran.

FR-8

Forward Payment to Merchant

OttoPay meneruskan data pembayaran (VA, nominal, payment reference, dsb.) ke endpoint backend Merchant.

FR-9

Payment Validation & Acknowledgment

Merchant memverifikasi dan memproses transaksi pembayaran, kemudian memberikan response valid ke OttoPay.

FR-10

Final Payment Confirmation

OttoPay meneruskan hasil akhir transaksi ke Issuer VA sebagai konfirmasi sukses atau gagalnya pembayaran.

FR-11

Signature Validation

Semua request antara OttoPay dan Merchant harus dilindungi menggunakan digital signature dan wajib divalidasi sebelum diproses.

FR-12

Idempotent Payment Handling

Merchant harus memastikan bahwa pembayaran dengan data yang sama hanya diproses sekali (idempotent).

FR-13

Error Response & Timeout Handling

Merchant harus memberikan response error yang sesuai jika data inquiry/payment tidak valid. Maksimal waktu respons adalah 3 detik.

FR-14

Audit Logging

Semua request inquiry dan payment harus dicatat dalam sistem log untuk keperluan audit dan debugging.

Title B: Merchant Backend – Generate Virtual Account (VA)
Functional Requirements (B) 

No.

Requirement

Description

FR-B1

Retrieve Payment Method Details

Merchant Backend harus mengambil data dari tabel Company Provider - Payment Channel. Jika provider yang terhubung adalah OTTOPAY, sistem harus mengambil informasi rahasia dari provider_secrets, seperti: x-api-key, username, password, dan daftar va_prefix untuk masing-masing bank (mis. va_prefix_bina, va_prefix_bmri, va_prefix_bca, dll).

FR-B2

Generate VA Number

Merchant Backend harus menghasilkan Virtual Account Number sesuai struktur berikut: VA Number = [Prefix] + [Customer Number] Prosedur pembentukan VA: Ambil Prefix dari provider_secrets sesuai bank/channel yang dipilih.Sub-Prefix tidak digunakan, agar alokasi digit dapat dimaksimalkan untuk Customer Number.Customer Number adalah bagian yang dikonfigurasi oleh tim Moaja, dan dapat berupa nomor telepon customer.Jika nomor customer lebih dari 11 digit, ambil 11 digit terakhir.

FR-B3

Customer Number Rule & Example

Customer Number adalah String dengan panjang maksimum 11 digit. - Jika user memiliki nomor telepon ************, maka VA akan menjadi: **************** (prefix = 71101, customer = ***********). - Jika nomor memiliki 13 digit, contoh 085**********, maka ambil 11 digit terakhir → **********, maka VA = 71101**********.

FR-B4

Validation & Acknowledgement

Merchant Backend harus memverifikasi apakah payment_channel_code yang diberikan oleh frontend sesuai dengan konfigurasi va_prefix dalam provider_secrets. Sistem harus mengembalikan response success/failure sesuai hasil validasi ke frontend.

FR-B5

Inventory VA Number Record

Setelah proses validasi dan generate berhasil (FR-B4), Merchant Backend wajib menyimpan va_number ke dalam tabel fva_customer, beserta informasi terkait seperti user_id, provider_id, payment_channel_code, status, dan created_at.

FR-B6

Audit Logging

Semua aktivitas generate dan penyimpanan va_number, termasuk proses validasi channel dan penyimpanan ke fva_customer, harus dicatat dalam sistem audit log. Log minimal mencatat: timestamp, actor (user atau sistem), operation (generate VA), va_number, dan status.

Squence Diagram (Flow Proses)

