package emailclient

import (
	"context"
	"testing"
)

func TestSendEmail(t *testing.T) {
	w := NewWrapper()

	// domain := "finance.nusatek.id"
	// apiKey := "48723e8e522a149f21c8b8ca8af0badc-b7b36bc2-00c12c09"

	_ = w.SendMsg(context.Background(), SendMsgReq{
		Sender:    "<EMAIL>",
		Recipient: "<EMAIL>",
		Subject:   "Disburse Proses",
		Body: `Dear client,
		<PERSON> disbursement transaksi <partner_name> sudah ditransfer ke rekening <payment_channel_partner> <bank_account_number> a.n. <bank_account_partner>.
		Terima kasih
		Best regards,
		Finance Nusatek`,
		Attachs: []SendMsgReqAttachment{
			{
				Filename: "test.txt",
				Buffer: []byte(`
				testingg
				asdasdasd
				sadasdassd
				asdasdas
				`),
			},
		},
		CCs: []string{"<EMAIL>", "<EMAIL>"},
	})

}
