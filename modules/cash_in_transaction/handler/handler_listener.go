package handler

import (
	"context"
	"encoding/json"

	"github.com/streadway/amqp"
	"repo.nusatek.id/moaja/backend/libraries/logger"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (h *CashinHandler) ListenCallbackXfers(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenCallbackXfers", logger.String("body", string(c.Body)))

	var req xfers.PaymentResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	go h.cashinUsecase.CallbackXfersCashinTranscationProcess(ctx, req)

	return
}

func (h *CashinHandler) ListenerCallbackNicePay(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackNicePay", logger.String("body", string(c.Body)))

	var req nicepay.NotificationRequest
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	go h.cashinUsecase.CallbackNicePayCashInTransactionProcess(ctx, req)

	return
}

func (h *CashinHandler) ListenerCallbackSnapNicePay(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackSnapNicePay", logger.String("body", string(c.Body)))

	var req snapclient.NicepaySnapNotifRequest
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	logger.Info(ctx, "ListenerCallbackSnapNicePay", logger.Any("req", req))

	go h.cashinUsecase.CallbackSnapNicePayCashInTransactionProcess(ctx, req)

	return
}

func (h *CashinHandler) ListenerCallbackXenditVa(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackXenditVa", logger.String("body", string(c.Body)))

	var vaResponse xendit.CallbackVAResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &vaResponse)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := vaResponse.ExternalID
	paymentId := vaResponse.CallbackVirtualAccountID
	if paymentId == "" {
		paymentId = vaResponse.ID
	}
	reqByte, _ := json.Marshal(vaResponse)

	go h.cashinUsecase.CallbackXenditCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxVirtualAccount, reqByte)

	return
}

func (h *CashinHandler) ListenerCallbackXenditEwallet(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackXenditEwallet", logger.String("body", string(c.Body)))

	var ewalletResponse xendit.CallbackEwalletResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &ewalletResponse)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := ewalletResponse.Data.ReferenceId
	if referenceId == "" {
		referenceId = ewalletResponse.ReferenceId
	}

	paymentId := ewalletResponse.Data.ID
	if paymentId == "" {
		paymentId = ewalletResponse.ID
	}

	reqByte, _ := json.Marshal(ewalletResponse)

	go h.cashinUsecase.CallbackXenditCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxEwallet, reqByte)

	return
}

func (h *CashinHandler) ListenerCallbackXenditInvoice(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackXenditInvoice", logger.String("body", string(c.Body)))

	var ewalletResponse xendit.CallbackInvoiceResponse
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &ewalletResponse)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := ewalletResponse.ExternalID
	paymentId := ewalletResponse.ID
	reqByte, _ := json.Marshal(ewalletResponse)

	go h.cashinUsecase.CallbackXenditCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxCc, reqByte)

	return
}

func (h *CashinHandler) ListenerCallbackXenditFixedPaymentCode(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackXenditFixedPaymentCode", logger.String("body", string(c.Body)))

	var callbackData xendit.CallbackFixedPaymentCode
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &callbackData)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := callbackData.ExternalID
	paymentId := callbackData.FixedPaymentCodeID
	if paymentId == "" {
		paymentId = callbackData.ID
	}
	reqByte, _ := json.Marshal(callbackData)

	go h.cashinUsecase.CallbackXenditCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxRetail, reqByte)

	return
}

func (h *CashinHandler) ListenerCallbackOttocash(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackOttocash", logger.String("body", string(c.Body)))

	var response ottocash.InquiryCallback
	err = c.Ack(false)
	if err != nil {
		return
	}
	err = json.Unmarshal(c.Body, &response)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := response.TrxID
	paymentId := response.ReferenceNumber
	reqByte, _ := json.Marshal(response)

	go h.cashinUsecase.CallbackOttocashCashinTranscationProcess(ctx, referenceId, paymentId, response.ResponseCode, reqByte)

	return
}

func (h *CashinHandler) ListenerCallbackDokuVa(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackDokuVa", logger.String("body", string(c.Body)))

	var vaResponse doku.VACallback
	err = c.Ack(false)
	if err != nil {
		return
	}

	err = json.Unmarshal(c.Body, &vaResponse)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := vaResponse.Order.InvoiceNumber
	paymentId := ""
	go h.cashinUsecase.CallbackDokuCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxVirtualAccount, c.Body)

	return
}

func (h *CashinHandler) ListenerCallbackCashlezOfflinePg(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackCashlezOfflinePg", logger.String("body", string(c.Body)))

	var data cashlez.CallbackOfflinePG
	err = c.Ack(false)
	if err != nil {
		return
	}

	err = json.Unmarshal(c.Body, &data)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	referenceId := data.MerchantTrxID
	paymentId := data.CashlezTransactionID
	go h.cashinUsecase.CallbackCashlezCashinTranscationProcess(ctx, referenceId, paymentId, constants.TrxOfflinePG, c.Body)

	return
}

func (h *CashinHandler) ListenerCallbackBankBcaSnap(c *amqp.Delivery) (err error) {
	ctx := context.Background()
	ctx = logutil.NewCtxFromAmqpTable(ctx, c.Headers)
	logger.Info(ctx, "ListenerCallbackBankBcaSnap", logger.String("body", string(c.Body)))

	var data domain.SnapCbCashInTransaction
	err = c.Ack(false)
	if err != nil {
		return
	}

	err = json.Unmarshal(c.Body, &data)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	go h.cashinUsecase.CallbackBankBcaSnapCashinTranscationProcess(ctx, data)

	return
}
