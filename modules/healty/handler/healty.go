package handler

import (
	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/modules/healty/usecase"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type HealtyHandler struct {
	healtyService usecase.HealtyService
}

func NewHandler(healtyService usecase.HealtyService) *HealtyHandler {
	return &HealtyHandler{healtyService}
}

func (h *HealtyHandler) HealtyCheck(c *fiber.Ctx) (err error) {
	ctx := c.UserContext()
	res := h.healtyService.CheckHealty(ctx)
	response.HandleSuccess(c, res)
	return
}