package signature

import (
	"testing"
)

const (
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
	pubKeyExample = `-----BEGIN PUBLIC KEY----- 
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtatInMYNr4JY7l46SD6VJ1gC/TbEVrRTFeBRR60togv9Uuw/DNZ9W/XYuwuU2LzanKJSLS4QNOlzIJu1RetaWR4vAgospls2vT71O/Q3wvVjRXQaYEsWouHRQlcWADPHbt1XH5U+KmD20Lx1WHcswgBJdv+n3nFbJKdycqb7Gb5mjrO 
dzxXSKaBECHBd2Cvl4OFhopDRLoBpzhUoKOtFnfeibOkaOEDXYJlD5e7r6xyem0p3DfyJcH9Xixi+m1NOJYDBZh8CnSAGhELmWC4zDdaTaAiwxRXSf2AFKMtN+tOFfyFzYikYK0fbhkiPCmBe8CTZ9Mu+nOl+g6W/H2vvwwIDAQAB
-----END PUBLIC KEY-----`
)

func TestParseRsaPublicKeyFromPemStr(t *testing.T) {
	pubKey, err := ParseRsaPublicKeyFromPemStr(pubKeyExample)
	if err != nil {
		t.Fatalf("error parse rsa pub %v", err)
	}

	err = VerifyPKCS(pubKey,
		"1afadbfe-2655-4268-bc4e-e01fcb8fc2a7|2024-01-26T16:00:39+07:00",
		"dpSINWFq71s9k3jW2ON0k1PlaWoaseA1Dg4EMAOBaH1OsKDyrtbNYFmrdU4TeYbup54PGR5Rwnb6phgZASIJEEueNARiVE+NK2Zn7D378NZ5edkJ6HOj5zmZHTowH6QwARZfeF70mI+0DaENnTCg5YCGh7/ZidGfCwOnQEehoXC+z968q5uT9iMFTd+Ev8J7eTOMctSU7bFmxAkrM/UOrNsqaYTTdtEQ9/XfivuetIgEIRsQ187Em7c8LVIROtu8piZQm/7stHo9106YPGOo27XToghsSBE6uXijaDVlPmO5oOdqbvs4i3a/8cUQN4nucbiJxk2uFaUdIGoFXWCsKg==",
	)
	if err != nil {
		t.Fatalf("error VerifyPKCS %v", err)
	}
}
