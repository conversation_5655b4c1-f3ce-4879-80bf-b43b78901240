package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/user_management/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

func (s *defaultUserManagement) CreateUser(ctx context.Context, req *entity.UsersRequest) (err error) {
	if err != nil {
		return
	}

	exist, err := s.userRepo.GetUserByEmail(ctx, req.Email)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetErrorMessage(http.StatusNotFound, fmt.Sprintf("err get user by email %v", err))
		logger.Error(ctx, err.Error())
		return err
	}

	if exist.ID > 0 {
		err = errors.SetErrorMessage(http.StatusConflict, "email already used")
		logger.Error(ctx, err.Error())
		return err
	}

	_, err = s.userRepo.GetRoleById(ctx, strconv.Itoa(req.RoleId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}

	pass := utils.GenerateSignatureHmac256(req.Password, config.GetString("user_key"))
	req.Password = pass
	reqUser := domain.Users{
		Name:     req.Name,
		Email:    req.Email,
		Password: pass,
		RoleId:   req.RoleId,
		Status:   req.Status,
	}

	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	err = s.userRepo.CreateUser(ctx, &reqUser, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	for i := 0; i < len(req.CompanyId); i++ {
		_, err = s.companyService.GetCompanyById(ctx, strconv.Itoa(req.CompanyId[i]))
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
			logger.Error(ctx, err.Error())
			return
		}
		reqCompany := domain.UserCompanies{
			UserId:    reqUser.ID,
			CompanyId: req.CompanyId[i],
		}
		err = s.userRepo.CreateUserCompany(ctx, &reqCompany, tx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	req.ID = reqUser.ID
	s.setCacheUser(ctx, &reqUser)

	tx.Commit()
	return
}

func (s *defaultUserManagement) UpdateUser(ctx context.Context, req *entity.UsersRequest) (err error) {
	user, err := s.userRepo.GetUserById(ctx, req.ID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "user not found")
		logger.Error(ctx, err.Error())
		return
	}
	_, err = s.userRepo.GetRoleById(ctx, strconv.Itoa(req.RoleId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}

	if req.Password != "" {
		pass := utils.GenerateSignatureHmac256(req.Password, config.GetString("user_key"))
		req.Password = pass
	} else {
		req.Password = user.Password
	}

	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	req.ID = user.ID
	userReq := domain.Users{
		ID:          user.ID,
		Name:        req.Name,
		Email:       req.Email,
		Password:    req.Password,
		RoleId:      req.RoleId,
		LastLoginAt: user.LastLoginAt,
		Status:      req.Status,
		CreatedAt:   user.CreatedAt,
	}
	err = s.userRepo.UpdateUser(ctx, &userReq, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	err = s.userRepo.DeleteUserCompanyByUserId(ctx, req.ID, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	for i := 0; i < len(req.CompanyId); i++ {
		_, err = s.companyService.GetCompanyById(ctx, strconv.Itoa(req.CompanyId[i]))
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "company not found")
			logger.Error(ctx, err.Error())
			return
		}
		reqCompany := domain.UserCompanies{
			UserId:    userReq.ID,
			CompanyId: req.CompanyId[i],
		}
		err = s.userRepo.CreateUserCompany(ctx, &reqCompany, tx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	s.deleteCacheUser(ctx, strconv.Itoa(req.ID))
	s.setCacheUser(ctx, &userReq)
	tx.Commit()
	return
}

func (s *defaultUserManagement) GetUserById(ctx context.Context, id string) (resp *domain.Users, err error) {
	resp, err = s.getCacheUser(ctx, id)
	return
}

func (s *defaultUserManagement) GetUserByIdShow(ctx context.Context, id string) (resp *entity.UsersResponse, err error) {
	user, err := s.getCacheUser(ctx, id)
	if err != nil {
		return
	}
	userCompany, err := s.userRepo.GetUserCompanyByUserId(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var companyArr []int
	for i := 0; i < len(*userCompany); i++ {
		companyArr = append(companyArr, (*userCompany)[i].CompanyId)
	}
	resp = &entity.UsersResponse{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		Password:  user.Password,
		RoleId:    user.RoleId,
		Status:    user.Status,
		CompanyId: companyArr,
	}
	return
}

func (s *defaultUserManagement) UserLogin(ctx context.Context, req string) (resp model.JwtTokenShow, err error) {
	// res, err := utils.DecryptAesCbc([]byte(config.GetString("login_key")), req)
	// if err != nil {
	// 	err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	// 	logger.Error(ctx, err.Error())
	// 	return
	// }
	var logUsr entity.UserLogin
	err = json.Unmarshal([]byte(req), &logUsr)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	user, err := s.userRepo.GetUserByEmail(ctx, logUsr.Email)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "email not valid")
		logger.Error(ctx, err.Error())
		return
	}
	pass := utils.GenerateSignatureHmac256(logUsr.Password, config.GetString("user_key"))
	if pass != user.Password {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "wrong password")
		logger.Error(ctx, err.Error())
		return
	}
	if !user.Status {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "inactive user")
		logger.Error(ctx, err.Error())
		return
	}
	role, err := s.userRepo.GetRoleById(ctx, strconv.Itoa(user.RoleId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role user not found")
		logger.Error(ctx, err.Error())
		return
	}
	if !role.Status {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "inactive user role")
		logger.Error(ctx, err.Error())
		return
	}

	rolePermission, err := s.userRepo.GetRolePermissionByRoleId(ctx, strconv.Itoa(role.ID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role permission not found")
		logger.Error(ctx, err.Error())
		return
	}
	var userCompanyId []int
	userCompany, errRes := s.userRepo.GetUserCompanyByUserId(ctx, strconv.Itoa(user.ID))
	if errRes == nil {
		var companyInt []int
		for i := 0; i < len(*userCompany); i++ {
			companyInt = append(companyInt, (*userCompany)[i].CompanyId)
		}
		userCompanyId = companyInt
	}

	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	user.LastLoginAt = time.Now().Unix()
	err = s.userRepo.UpdateUser(ctx, user, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	tx.Commit()

	var roles []model.RolePermissions
	for i := 0; i < len(*rolePermission); i++ {
		var perm map[string]bool
		_ = json.Unmarshal([]byte((*rolePermission)[i].Permissions), &perm)
		menu, errRes := s.GetUserMenuById(ctx, strconv.Itoa((*rolePermission)[i].MenuId))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "menu not found")
			logger.Error(ctx, err.Error())
			return
		}
		ty := model.RolePermissions{
			ID:          (*rolePermission)[i].ID,
			MenuId:      (*rolePermission)[i].MenuId,
			MenuCode:    menu.Code,
			Permissions: perm,
		}
		roles = append(roles, ty)
	}

	rolePer, _ := json.Marshal(roles)
	expiredTime := time.Now().Add(time.Hour * 72).Unix()
	token := jwt.New(jwt.SigningMethodHS256)
	claims := token.Claims.(jwt.MapClaims)
	claims["user_id"] = user.ID
	claims["exp"] = expiredTime
	claims["permissions"] = string(rolePer)
	claims["role_id"] = role.ID

	tokenClaim, err := token.SignedString([]byte(config.GetString("jwtKey")))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = model.JwtTokenShow{
		UserId:        user.ID,
		Username:      user.Name,
		Exp:           uint32(expiredTime),
		Permissions:   roles,
		RoleName:      role.Name,
		RoleId:        role.ID,
		UserCompanyId: userCompanyId,
		Token:         tokenClaim,
	}
	return
}

func (s *defaultUserManagement) DeleteUser(ctx context.Context, id int) (err error) {
	user, err := s.userRepo.GetUserById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	err = s.userRepo.DeleteUser(ctx, user)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultUserManagement) SearchUserList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.UserList, totalData int64, err error) {
	resp, totalData, err = s.userRepo.SearchUserList(ctx, paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	for i := 0; i < len(*resp); i++ {
		company, errRes := s.userRepo.GetUserCompanyByUserId(ctx, strconv.Itoa((*resp)[i].Id))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		var companyId []int
		for k := 0; k < len(*company); k++ {
			companyId = append(companyId, (*company)[k].CompanyId)
		}

		(*resp)[i].CompanyId = companyId
	}
	for i := 0; i < len(*resp); i++ {
		if (*resp)[i].LastLoginAt != "0" {
			unixString, errRes := utils.ParseUnixTime((*resp)[i].LastLoginAt)
			if errRes != nil {
				logger.Error(ctx, errRes.Error())
			}
			(*resp)[i].LastLoginAt = unixString
		}

		if (*resp)[i].LastLoginAt == "0" {
			(*resp)[i].LastLoginAt = ""
		}
	}
	return
}

func (s *defaultUserManagement) UpdateStatus(ctx context.Context, id int, status bool) (err error) {
	user, err := s.userRepo.GetUserById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "user not found")
		logger.Error(ctx, err.Error())
		return
	}

	user.Status = status
	err = s.userRepo.UpdateUser(ctx, user, s.userRepo.BeginTrans())
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheUser(ctx, strconv.Itoa(id))
	s.setCacheUser(ctx, user)
	return
}
