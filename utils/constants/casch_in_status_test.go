package constants

import "testing"

func TestDefineCashInStatusFromItemStatuses(t *testing.T) {
	type args struct {
		currStatus   string
		itemStatuses []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "should be cash in status pending",
			args: args{
				currStatus:   CashInStatusPending,
				itemStatuses: []string{CashInItemStatusPending},
			},
			want: CashInStatusPending,
		},
		{
			name: "should be cash in status partial reconciled",
			args: args{
				currStatus:   CashInStatusPaid,
				itemStatuses: []string{CashInItemStatusReconciled, CashInItemStatusPaid},
			},
			want: CashInStatusPartialReconciled,
		},
		{
			name: "should be cash in status partial reconciled",
			args: args{
				currStatus:   CashInStatusPaid,
				itemStatuses: []string{CashInItemStatusReconciled},
			},
			want: CashInStatusReconciled,
		},
		{
			name: "should be cash in status partial done",
			args: args{
				currStatus:   CashInStatusReconciled,
				itemStatuses: []string{CashInItemStatusDone},
			},
			want: CashInStatusDone,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DefineCashInStatusFromItemStatuses(tt.args.currStatus, tt.args.itemStatuses); got != tt.want {
				t.Errorf("DefineCashInStatusFromItemStatuses() = %v, want %v", got, tt.want)
			}
		})
	}
}
