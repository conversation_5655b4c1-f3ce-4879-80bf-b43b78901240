package usecase

import (
	"context"
	"fmt"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (s *defaultCashinTransaction) ManualPayment(ctx context.Context, req entity.ManualPaymentRequest) (err error) {
	cashInData, err := s.cashinRepo.GetCashinTransactionById(ctx, fmt.Sprintf("%d", req.Id))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	// check payment channel
	paymentChannel, err := s.channelService.GetPaymentChannelById(ctx, fmt.Sprintf("%d", cashInData.PaymentChannelID))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	paymentChannelType, err := s.channelService.GetPaymentChannelTypeById(ctx, fmt.Sprintf("%d", paymentChannel.PaymentChannelTypeId))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	if !str.Contains([]string{constants.TrxSNAP}, paymentChannelType.PaymentType) {
		err = errors.SetError(http.StatusBadRequest, "invalid payment channel type for manual payment")
		logger.Error(ctx, err.Error())
		return
	}

	if !str.Contains([]string{constants.CashInStatusPending, constants.CashInStatusExpired, constants.CashInStatusFailed}, cashInData.Status) {
		err = errors.SetError(http.StatusBadRequest, fmt.Sprintf("status %s is invalid for manual payment", cashInData.Status))
		logger.Error(ctx, err.Error())
		return
	}

	err = cashInData.ManualPayment(ctx, req.PaymentAt)
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	user, err := s.userService.GetUserById(ctx, fmt.Sprintf("%d", req.UpdatedBy))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	historyDesc := fmt.Sprintf("manual payment by %s", user.Email)

	paymentProvider, err := s.providerService.GetPaymentProviderById(ctx, fmt.Sprintf("%d", cashInData.PaymentProviderID))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	cb := cashInData.ToSnapCb(domain.ToSnapCbOpts{
		PaymentProviderName: paymentProvider.Name,
		PartnerId:           "",
	})

	err = s.UpdateStatusSnapCashinTransaction(ctx, cashInData, cb.ToStatus(constants.CashInStatusPaid), domain.UpdateWachingStatusCashinOpts{
		HistoryDescription: historyDesc,
	})
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return nil
}
