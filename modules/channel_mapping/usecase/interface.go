package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	entity "repo.nusatek.id/nusatek/payment/modules/channel_mapping/entity"
)

type ChannelMappingRepository interface {
	CreateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error)
	UpdateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error)
	GetChannelMappingById(ctx context.Context, id string) (resp *domain.PaymentProviderChannelMappings, err error)
	ChekExistingRelationDataCompany(ctx context.Context, providerId, channelID int64, capability int) (existingData bool, err error)
	DeleteChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error)
	GetListChannelMappingByProviderId(ctx context.Context, params domain.GetPaymentChannelsParams, providerID string) (resp *[]entity.PaymentChannelMappingList, totalData int64, err error)
	GetChannelMappingCashIn(ctx context.Context, providerId, channelId string) (resp *domain.PaymentProviderChannelMappings, err error)
	GetChannelMappingByProviderChannelIdAndCapability(ctx context.Context, providerId, channelId, cabability string) (resp *domain.PaymentProviderChannelMappings, err error)
	GetChannelMappingByCode(ctx context.Context, code string) (resp *domain.PaymentProviderChannelMappings, err error)
}

type ChannelMappingUsecase interface {
	CreateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings) (err error)
	UpdateChannelMapping(ctx context.Context, req *domain.PaymentProviderChannelMappings, id string) (err error)
	DeleteChannelMapping(ctx context.Context, id string) (err error)
	GetChannelMappingById(ctx context.Context, id string) (resp *domain.PaymentProviderChannelMappings, err error)
	GetListChannelMappingByProviderId(ctx context.Context, params domain.GetPaymentChannelsParams, providerId string) (resp *[]entity.PaymentChannelMappingList, totalData int64, err error)
	GetChannelMappingByCode(ctx context.Context, code string) (resp *domain.PaymentProviderChannelMappings, err error)
}
