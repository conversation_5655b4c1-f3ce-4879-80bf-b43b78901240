-- migrate:up
ALTER TABLE payment_channel_types ADD COLUMN config_templates JSONB; -- for general setting per channel
/*
[
  {
    "name": "is_active_fva",
    "type": "boolean"
  },
  {
    "name": "merchant_code",
    "type": "string"
  },
  {
    "name": "virtual_account_start",
    "type": "integer"
  },
  {
    "name": "virtual_account_end",
    "type": "integer"
  }
]
*/

ALTER TABLE company_payment_provider_channel_mappings ADD COLUMN channel_type_configs JSONB; -- for general setting per channel

CREATE TABLE fva_customers (
	id SERIAL NOT NULL PRIMARY KEY,
	company_payment_provider_channel_mapping_id INT NOT NULL REFERENCES company_payment_provider_channel_mappings(id),
	counter INTEGER NOT NULL, 
	va_number TEXT NOT NULL, -- virtual account number
	name TEXT NOT NULL,
	phone TEXT NOT NULL,
	last_pg_reference_id TEXT NOT NULL, -- latest transaction id from payment gateway (payment gateway reference id)
	expired_at TIMESTAMPTZ NOT NULL, -- is needed ??
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

CREATE UNIQUE INDEX fva_customers_unique
ON fva_customers(company_payment_provider_channel_mapping_id, phone) 
WHERE deleted_at IS NULL; -- exclude soft delete


CREATE TABLE company_payment_provider_channel_mapping_counters (
	id SERIAL NOT NULL PRIMARY KEY,
	company_payment_provider_channel_mapping_id INT NOT NULL REFERENCES company_payment_provider_channel_mappings(id),
	type TEXT NOT NULL, -- fixed_va, others
  counter INTEGER NOT NULL DEFAULT 0,
	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL
);

INSERT INTO public.company_payment_provider_channel_mapping_counters
(company_payment_provider_channel_mapping_id, "type", counter, created_at, updated_at, deleted_at)
VALUES(170, 'fixed_va', 0,NOW(),NOW(), NULL);

CREATE UNIQUE INDEX company_payment_provider_channel_mapping_counter_unique
ON company_payment_provider_channel_mapping_counters(company_payment_provider_channel_mapping_id, type) 
WHERE deleted_at IS NULL; -- exclude soft delete


/*
company_payment_provider_channel_mappings
- add column is_fva_need_prefix bool
    - alternative more general name fva_name_format (use golang format)
- add column fva_prefix string (hopefully could be used for general payment provider, nee to check other provider)
- or add column fva_settings with JSONB type
- add column len

need table for fva_counter
for fixed va ,would is_single_use is false
*/

/*
perlu get va account untuk cek tagihan
*/

-- insert merchant code inside company_payment_provider_mappings column secrets

-- migrate:down
ALTER TABLE payment_channel_types DROP COLUMN config_templates;
ALTER TABLE company_payment_provider_channel_mappings DROP COLUMN channel_type_configs;

DROP TABLE fva_customers;

DROP INDEX fva_customers_unique;

DROP TABLE fva_customer_counters;

DROP INDEX company_payment_provider_channel_mapping_counter_unique;

