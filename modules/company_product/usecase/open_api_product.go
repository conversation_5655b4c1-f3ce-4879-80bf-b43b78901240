package usecase

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

func (s *defaultCompanyProduct) CreateCompanyProductAPI(ctx context.Context, req *domain.CompanyProducts, auth string) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	req.CompanyID = company.ID
	err = s.companyProductRepo.CreateCompanyProduct(ctx, req)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheCompanyProductCode(ctx, req)
	s.setCacheCompanyProductId(ctx, req)
	return
}

func (s *defaultCompanyProduct) UpdateCompanyProductAPI(ctx context.Context, id, auth string, req *domain.CompanyProducts) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	product, err := s.GetCompanyProductById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
		logger.Error(ctx, err.Error())
		return
	}

	if product.CompanyID != company.ID {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "company does not have access to this product")
		logger.Error(ctx, err.Error())
		return
	}

	req.CreatedAt = product.CreatedAt
	req.ID = product.ID
	req.CompanyID = company.ID
	err = s.companyProductRepo.UpdateCompanyProduct(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheCompanyProductCode(ctx, req)
	s.setCacheCompanyProductId(ctx, req)

	return
}

func (s *defaultCompanyProduct) DeleteCompanyProductAPI(ctx context.Context, id, auth string) (err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	product, err := s.GetCompanyProductById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "product not found")
		logger.Error(ctx, err.Error())
		return
	}

	if product.CompanyID != company.ID {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "this company does not have access to this product")
		logger.Error(ctx, err.Error())
		return
	}

	err = s.companyProductRepo.DeleteCompanyProduct(ctx, product)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheCompanyProductCode(ctx, product.Code)
	s.deleteCacheCompanyProductId(ctx, strconv.Itoa(product.ID))
	return
}

func (s *defaultCompanyProduct) ListCompanyProductAPI(ctx context.Context, paginate utils.Pagination, auth string) (resp *[]entity.CompanyProductShow, totalData int64, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	paginate.Key = "company_id"
	paginate.Value = strconv.Itoa(company.ID)
	resp, totalData, err = s.companyProductRepo.SearchCompanyProduct(ctx, paginate)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCompanyProduct) GetCompanyProductCodeAPI(ctx context.Context, auth, code string) (resp *entity.CompanyProductShow, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	data, err := s.companyProductRepo.GetCompanyProductByCode(ctx, code)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = &entity.CompanyProductShow{
		ID:            data.ID,
		CompanyID:     data.CompanyID,
		CompanyName:   company.Name,
		ProductName:   data.ProductName,
		ProductCode:   data.Code,
		FeeFixValue:   data.FeeFixValue,
		FeePercentage: data.FeePercentage,
		Status:        data.Status,
	}

	if resp.CompanyID != company.ID {
		err = errors.SetError(http.StatusInternalServerError, "invalid_grant")
		logger.Error(ctx, err.Error())
		return
	}

	return
}
