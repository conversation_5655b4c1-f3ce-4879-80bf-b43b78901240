package snaputil

import "testing"

func TestAddLeftPaddingSpace(t *testing.T) {
	type args struct {
		text  string
		digit int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case 1",
			args: args{
				text:  "12345",
				digit: 8,
			},
			want: "   12345",
		},
		{
			name: "case 2",
			args: args{
				text:  "12345",
				digit: 6,
			},
			want: " 12345",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AddLeftPaddingSpace(tt.args.text, tt.args.digit); got != tt.want {
				t.Errorf("AddLeftPaddingSpace() = %v, want %v", got, tt.want)
			}
		})
	}
}
