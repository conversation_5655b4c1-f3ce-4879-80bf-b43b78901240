package snapclient

import (
	"encoding/json"
	"fmt"

	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

// get credential from secret with result in CB
func GetSnapCredentialCB(secret string, cred iCredential) (err error) {
	dec, err := utils.DecryptAES(secret, config.GetString("secret_key_company"))
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(dec), &cred)
	if err != nil {
		err = fmt.Errorf("unmarshan credential error: %v", err.Error())
		return
	}

	return
}
