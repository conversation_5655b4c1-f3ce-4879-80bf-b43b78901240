package usecase

import (
	"github.com/go-redis/redis/v8"

	"repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
)

type defaultCompanyProduct struct {
	companyProductRepo CompanyProductRepo
	companyRepo        usecase.CompanyManagementRepo
	cache              *redis.Client
	companyService     usecase.CompanyManagementUsecase
}

func Setup() *defaultCompanyProduct {
	return &defaultCompanyProduct{}
}

func (s *defaultCompanyProduct) SetCompanyProductRepo(t CompanyProductRepo) *defaultCompanyProduct {
	s.companyProductRepo = t
	return s
}

func (s *defaultCompanyProduct) SetCompanyRepo(t usecase.CompanyManagementRepo) *defaultCompanyProduct {
	s.companyRepo = t
	return s
}

func (s *defaultCompanyProduct) SetRedisClient(t *redis.Client) *defaultCompanyProduct {
	s.cache = t
	return s
}

func (s *defaultCompanyProduct) SetCompanyService(t usecase.CompanyManagementUsecase) *defaultCompanyProduct {
	s.companyService = t
	return s
}

func (s *defaultCompanyProduct) Validate() CompanyProductUsecase {
	if s.companyProductRepo == nil {
		panic("company product repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.companyRepo == nil {
		panic("company repo is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	return s
}
