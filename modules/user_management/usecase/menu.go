package usecase

import (
	"context"
	"encoding/json"
	"net/http"
	"sort"
	"strconv"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/user_management/entity"
)

func (s *defaultUserManagement) GetListMenuMappingUserPermissionByRoleId(ctx context.Context, roleId string) (resp []entity.MenuMappingPermission, err error) {
	_, err = s.userRepo.GetRoleById(ctx, roleId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}
	userPermission, err := s.userRepo.GetRolePermissionByRoleId(ctx, roleId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	menus, err := s.userRepo.GetListUserMenuAll(ctx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	isCreate, err := s.createRolePermissionMenu(ctx, userPermission, menus, roleId, tx)
	if err != nil {
		return
	}

	tx.Commit()

	if isCreate {
		userPermission, err = s.userRepo.GetRolePermissionByRoleId(ctx, roleId)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	var mapping []entity.MenuMappingPermission
	for i := 0; i < len(*userPermission); i++ {
		menuId := strconv.Itoa((*userPermission)[i].MenuId)
		menu, errRes := s.GetUserMenuById(ctx, menuId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		var permission map[string]bool
		json.Unmarshal([]byte((*userPermission)[i].Permissions), &permission)
		var res = entity.MenuMappingPermission{
			Id:         (*userPermission)[i].ID,
			MenuId:     menu.ID,
			MenuName:   menu.Name,
			MenuCode:   menu.Code,
			Permission: permission,
		}
		mapping = append(mapping, res)
	}

	sort.Slice(mapping, func(i, j int) bool {
		return mapping[i].MenuId < mapping[j].MenuId
	})

	resp = append(resp, mapping...)
	return
}

func (s *defaultUserManagement) UpdateListMenuMappingUserPermissionByRoleId(ctx context.Context, req []entity.MenuMappingPermission, roleId string) (err error) {
	_, err = s.userRepo.GetRoleById(ctx, roleId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}
	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	for i := 0; i < len(req); i++ {
		rolePermision, errRes := s.userRepo.GetRolePermissionById(ctx, strconv.Itoa(req[i].Id))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "role permission not found")
			logger.Error(ctx, err.Error())
			return
		}
		if roleId != strconv.Itoa(rolePermision.RoleId) {
			err = errors.SetErrorMessage(http.StatusBadRequest, "role permission not found in role")
			logger.Error(ctx, err.Error())
			return
		}
		permisssion, _ := json.Marshal((req)[i].Permission)
		updateRolePermission := domain.RolePermissions{
			ID:          rolePermision.ID,
			RoleId:      rolePermision.RoleId,
			MenuId:      rolePermision.MenuId,
			Permissions: string(permisssion),
			CreatedAt:   rolePermision.CreatedAt,
		}
		err = s.userRepo.UpdateRolePermission(ctx, &updateRolePermission, tx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	tx.Commit()
	return
}

func (s *defaultUserManagement) CreateUserMenu(ctx context.Context, req *domain.Menus) (err error) {
	err = s.userRepo.CreateUserMenu(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.setCacheUserMenu(ctx, req)
	return
}

func (s *defaultUserManagement) UpdateUserMenu(ctx context.Context, req *domain.Menus, id string) (err error) {
	menu, err := s.userRepo.GetUserMenuById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "menu not found")
		logger.Error(ctx, err.Error())
		return
	}
	req.CreatedAt = menu.CreatedAt
	req.ID = menu.ID
	err = s.userRepo.UpdateUserMenu(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheUserMenu(ctx, id)
	s.setCacheUserMenu(ctx, req)
	return
}

func (s *defaultUserManagement) GetUserMenuById(ctx context.Context, id string) (resp *domain.Menus, err error) {
	return s.getCacheUserMenu(ctx, id)
}

func (s *defaultUserManagement) DeleteUserMenuById(ctx context.Context, id string) (err error) {
	menu, err := s.userRepo.GetUserMenuById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "menu not found")
		logger.Error(ctx, err.Error())
		return
	}
	err = s.userRepo.DeleteUserMenu(ctx, menu)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheUserMenu(ctx, id)
	return
}
