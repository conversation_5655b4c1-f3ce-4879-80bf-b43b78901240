package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_management/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type CompanyManagementHandler struct {
	companyService usecase.CompanyManagementUsecase
}

func NewHandler(companyService usecase.CompanyManagementUsecase) *CompanyManagementHandler {
	return &CompanyManagementHandler{companyService}
}

func (h *CompanyManagementHandler) CreateCompany(c *fiber.Ctx) (err error) {
	var req domain.Companies
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.companyService.CreateCompany(c.UserContext(), &req)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *CompanyManagementHandler) GenerateRandomApiKey(c *fiber.Ctx) (err error) {
	resp, err := h.companyService.GenerateRandomApiKey(c.UserContext())
	if err != nil {
		return
	}
	res := entity.HTTPGenerateApiKeyResp{
		ApiKey: resp,
	}
	return response.HandleSuccess(c, res)
}

func (h *CompanyManagementHandler) GetCompanyByID(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.companyService.GetCompanyByIdShow(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *CompanyManagementHandler) UpdateCompany(c *fiber.Ctx) (err error) {
	var req domain.Companies
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	req.ID, err = strconv.Atoi(id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid id")
	}

	err = h.companyService.UpdateCompany(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *CompanyManagementHandler) DeleteCompany(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.companyService.DeleteCompany(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *CompanyManagementHandler) SearchCompany(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}
	company, totalData, err := h.companyService.SearchCompany(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, company)
}

func (h *CompanyManagementHandler) GetCompanyListAll(c *fiber.Ctx) (err error) {
	status := c.Query("status")
	resp, _, err := h.companyService.GetCompanyListAll(c.UserContext(), status)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *CompanyManagementHandler) UpdateStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id := c.Params("id")
	req := new(request)

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = h.companyService.UpdateStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}
