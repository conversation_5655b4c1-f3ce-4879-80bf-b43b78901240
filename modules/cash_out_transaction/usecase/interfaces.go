package usecase

import (
	"context"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

// CashOutRepository
type CashOutRepository interface {
	ListCashOut(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) ([]domain.CashOutTransactionDetail, int64, error)
	ListHistory(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	Update(ctx context.Context, cashOut *domain.CashOutTransactions, tx *gorm.DB) (res *domain.CashOutTransactions, err error)
	UpdateAmount(ctx context.Context, cashOut *domain.CashOutTransactions, tx *gorm.DB) (res *domain.CashOutTransactions, err error)
	GetByID(ctx context.Context, ID int) (resp domain.CashOutTransactions, err error)
	GetDetailByID(ctx context.Context, id int) (res domain.CashOutTransactionDetail, err error)
	GetCashInInfo(ctx context.Context, cashOutIDs []int64) (res []domain.CashOutCashInInfo, err error)
	GetCashInByCashoutID(ctx context.Context, cashoutID int) (resp []domain.CashInTransactionItems, err error)
	CountDetail(ctx context.Context, tx *gorm.DB, cashOutID int) (res int, err error)
	GetByIDs(ctx context.Context, IDs []int) (resp []domain.CashOutTransactions, err error)
	GetRequestedCashOutByIds(ctx context.Context, Ids []int) (resp []domain.CashOutTransactions, err error)
	CreateHistory(ctx context.Context, cashOut *domain.CashOutTransactionHistories, tx *gorm.DB) (*domain.CashOutTransactionHistories, error)
	RequestDisbursement(ctx context.Context) ([]domain.CashOutTransactions, error)
	UpdateCashOutTransactionHistory(ctx context.Context, req *domain.CashOutTransactionHistories) (err error)
	GetCashOutTransactionByBatchNumber(ctx context.Context, batchNumber string) (resp *domain.CashOutTransactions, err error)
	GetCashOutTransactionDetailsByCashOutId(ctx context.Context, id int) (resp []domain.CashOutTransactionDetails, err error)
	GetListItemCashInBatchNumber(ctx context.Context, req domain.GetCashoutItemParam) (resp *[]entity.ListItemCashInBatchNumber, total int64, err error)
	DeleteCashOutTransaction(ctx context.Context, req *domain.CashOutTransactions, tx *gorm.DB) (err error)
	UpdateCashInItemStatusBulk(ctx context.Context, tx *gorm.DB, itemIds []int, status string) (err error)
	RollbackCashInItemByCashout(ctx context.Context, tx *gorm.DB, cashoutIds []int) (err error)
	GetAllCompleteDetailByCashOutItemIDs(ctx context.Context, cashOutId int, cashOutItemIds []int) (resp []domain.CashOutTransactionDetailComplete, err error)
	DeleteCashOutItemsByIDs(ctx context.Context, tx *gorm.DB, cashOutId int, itemIds []int) (err error)
	GetAllOutstandingItemCashIn(ctx context.Context, params domain.CashInTransactionItemsOutstandingParam) (resp []domain.CashInTransactionItemsOutstanding, total int64, err error)
	Export(ctx context.Context, req *domain.CashoutExportReq) (res []domain.CashoutExportRes, err error)
	UpdateCashOutDetails(ctx context.Context, cashOutId int, tx *gorm.DB) (err error)
	SaveEmailLog(ctx context.Context, emailLog entity.EmailLog) (err error)
}

// CashOutUseCase
type CashOutUseCase interface {
	List(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) ([]domain.CashOutTransactionDetail, int64, error)
	ListHistory(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	GetByID(ctx context.Context, ID int) (resp domain.CashOutTransactions, err error)
	GetDetailByID(ctx context.Context, ID int) (resp domain.CashOutTransactionDetail, err error)
	RequestDisbursement(ctx context.Context, req entity.UpdateRequest, cashOutId, userId int) (err error)
	DisbursementProcess(ctx context.Context)
	GenerateQRGoogleAuthorization(ctx context.Context, userId int) (resp model.Qr, err error)
	UpdateCashOutTransactionStatus(ctx context.Context, req entity.OTP, userId int, statusUpdate string) (resp *domain.CashOutTransactions, err error)
	BulkApproveCashOutTransactionStatus(ctx context.Context, req entity.OTP, userId int) (err error)
	CallbackCashOutTransactionProces(ctx context.Context, batchNumber, paymentId, providerName string, body []byte)
	PushCallbackProviderCashOutTranscation(ctx context.Context, signature string, req interface{}, providerName string) (err error)
	UpdateCashOutTransactionStatusFeePaymentStatus(ctx context.Context, req entity.FeePaymentStatusRequest, userId int) (err error)
	ListAPI(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}, auth string) (res []domain.CashOutTransactionDetail, totalItem int64, err error)
	CheckStatusCashOutAPI(ctx context.Context, cashOutId, auth string) (resp interface{}, err error)
	GetListItemCashInBatchNumber(ctx context.Context, req domain.GetCashoutItemParam) (resp *[]entity.ListItemCashInBatchNumber, total int64, err error)
	AddCashOutItemsByIDs(ctx context.Context, cashOutId int, cashInItemIDs []int) error
	DeleteCashOutItemsByIDs(ctx context.Context, cashOutId int, cashoutIDs []int) error
	GetAllOutstandingItemCashIn(ctx context.Context, param domain.CashInTransactionItemsOutstandingParam) (resp []domain.CashInTransactionItemsOutstanding, total int64, err error)
	Export(ctx context.Context, req *domain.CashoutExportReq) (filename string, err error)
	UpdateCashOutTransaction(ctx context.Context, cashOutId int, p *entity.ManualDoneRequest) (err error)
	SendCashoutEmail(ctx context.Context, coId int) error
	ResendEmail(ctx context.Context, coId int) error
}
