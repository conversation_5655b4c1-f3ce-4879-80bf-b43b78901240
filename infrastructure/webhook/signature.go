package webhook

import (
	"crypto/hmac"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"fmt"
	"hash"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// SignatureScheme defines the signature algorithm
type SignatureScheme string

const (
	// Header constants
	SchemeHMACSHA512 SignatureScheme = "HMACSHA512"
	SchemeHMACSHA256 SignatureScheme = "HMACSHA256"
	AUTHORIZATION                    = "Authorization"
	CONTENT_TYPE                     = "Content-Type"
	TIMESTAMP                        = "Timestamp"
	X_REQUEST_ID                     = "X-Request-ID"
	DIGEST                           = "Digest"
	SYMBOL_COLON                     = ":"
)

// SignatureConfig holds configuration for webhook signature verification
type SignatureConfig struct {
	RequestId       string          // Request ID
	Secret          string          // Secret key used for signing
	HeaderName      string          // Name of the header containing the signature
	Scheme          SignatureScheme // Signature scheme to use
	TimestampHeader string          // Name of the header containing the timestamp
	TimestampMaxAge time.Duration   // Maximum age of timestamp for replay protection
	DigestHeader    string          // Name of the header containing the payload digest
}

// DefaultSignatureConfig returns a default signature configuration
func DefaultSignatureConfig() SignatureConfig {
	return SignatureConfig{
		HeaderName:      "Signature",
		Scheme:          SchemeHMACSHA256,
		TimestampHeader: "Timestamp",
		TimestampMaxAge: 5 * time.Minute,
		DigestHeader:    "Digest",
	}
}

// SignatureVerifier handles webhook signature verification
type SignatureVerifier struct {
	config SignatureConfig
}

// NewSignatureVerifier creates a new signature verifier
func NewSignatureVerifier(config SignatureConfig) *SignatureVerifier {
	if config.HeaderName == "" {
		config.HeaderName = DefaultSignatureConfig().HeaderName
	}
	if config.Scheme == "" {
		config.Scheme = DefaultSignatureConfig().Scheme
	}
	if config.TimestampHeader == "" {
		config.TimestampHeader = DefaultSignatureConfig().TimestampHeader
	}
	if config.TimestampMaxAge == 0 {
		config.TimestampMaxAge = DefaultSignatureConfig().TimestampMaxAge
	}
	if config.DigestHeader == "" {
		config.DigestHeader = DefaultSignatureConfig().DigestHeader
	}

	config.RequestId = uuid.NewString()

	return &SignatureVerifier{
		config: config,
	}
}

func (v *SignatureVerifier) Secret(s string) *SignatureVerifier {
	v.config.Secret = s
	return v
}

// getHasher returns the appropriate hash function based on the scheme
func (v *SignatureVerifier) getHasher() func() hash.Hash {
	switch v.config.Scheme {
	case SchemeHMACSHA256:
		return sha256.New
	default:
		return sha512.New
	}
}

// GeneratePayloadDigest generates a digest of the payload using the configured hash function
func (v *SignatureVerifier) GeneratePayloadDigest(payload []byte) string {
	h := v.getHasher()()
	h.Write(payload)
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// GenerateSignature generates a signature for the given payload, timestamp, and digest
func (v *SignatureVerifier) GenerateSignature(payload []byte, timestamp int64, contentType string) (string, string) {
	// Generate payload digest first
	digest := v.GeneratePayloadDigest(payload)

	h := hmac.New(v.getHasher(), []byte(v.config.Secret))

	// Include timestamp and digest in signature to prevent replay attacks and tampering
	timestampStr := strconv.FormatInt(timestamp, 10)

	// h.Write([]byte(v.config.RequestId))
	// h.Write([]byte(timestampStr))
	// h.Write([]byte(digest))
	// h.Write(payload)
	// Prepare Signature Component
	fmt.Println("----- Component Signature -----")
	var componentSignature strings.Builder
	componentSignature.WriteString(AUTHORIZATION + SYMBOL_COLON + fmt.Sprintf("Basic %s", v.config.Secret))
	componentSignature.WriteString("\n")
	componentSignature.WriteString(CONTENT_TYPE + SYMBOL_COLON + contentType)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(TIMESTAMP + SYMBOL_COLON + timestampStr)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(X_REQUEST_ID + SYMBOL_COLON + v.config.RequestId)
	if len(digest) > 0 {
		componentSignature.WriteString("\n")
		componentSignature.WriteString(DIGEST + SYMBOL_COLON + digest)
	}
	fmt.Println(componentSignature.String())
	fmt.Println("")

	h.Write([]byte(componentSignature.String()))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	// Signature format: scheme.timestamp.hmac
	return fmt.Sprintf("%s.%s", v.config.Scheme, signature), digest
}

// VerifySignature verifies the signature of a payload
func (v *SignatureVerifier) VerifySignature(payload []byte, signature, digest string, timestamp int64) error {
	parts := strings.Split(signature, ".")
	if len(parts) != 2 {
		return fmt.Errorf("invalid signature format: expected scheme.hmac")
	}

	// Verify scheme
	scheme := SignatureScheme(parts[0])
	if scheme != v.config.Scheme {
		return fmt.Errorf("invalid signature scheme: expected %s, got %s", v.config.Scheme, scheme)
	}

	// Verify timestamp
	if err := v.verifyTimestamp(timestamp); err != nil {
		return fmt.Errorf("timestamp verification failed: %w", err)
	}

	// Verify digest matches
	expectedDigest := v.GeneratePayloadDigest(payload)
	if digest != expectedDigest {
		return fmt.Errorf("invalid payload digest")
	}

	// Compare actual signature part
	expectedSignature, _ := v.GenerateSignature(payload, timestamp, "application/json")
	if !hmac.Equal([]byte(parts[1]), []byte(strings.Split(expectedSignature, ".")[1])) {
		return fmt.Errorf("invalid signature")
	}

	return nil
}

func (v *SignatureVerifier) verifyTimestamp(timestamp int64) error {
	now := time.Now().Unix()
	age := now - timestamp

	if age < 0 {
		return fmt.Errorf("signature timestamp is in the future")
	}

	if age > int64(v.config.TimestampMaxAge.Seconds()) {
		return fmt.Errorf("signature has expired")
	}

	return nil
}

func (v SignatureVerifier) BuildHeaders(timestamp int64, signature, digest string) map[string]string {
	return map[string]string{
		v.config.TimestampHeader: fmt.Sprintf("%d", timestamp),
		v.config.HeaderName:      signature,
		"X-Request-ID":           v.config.RequestId,
	}
}
