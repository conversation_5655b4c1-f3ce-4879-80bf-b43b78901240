package database

import (
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	// "repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

func NewDatabase() *gorm.DB {
	fmt.Println("Checking NewDatabase ...")

	dsn := fmt.Sprintf("host=%s port=%s dbname=%s user=%s password=%s sslmode=%s timezone=%s",
		config.GetString("db.host"),
		config.GetString("db.port"),
		config.GetString("db.name"),
		config.GetString("db.user"),
		config.GetString("db.pass"),
		config.GetString("db.ssl"),
		config.GetString("db.timezone"),
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetMaxIdleConns(config.GetInt("db.maxIdle"))
	sqlDB.SetMaxOpenConns(config.GetInt("db.maxOpenConn"))
	sqlDB.SetConnMaxIdleTime(time.Duration(config.GetInt("db.maxTimeIdleConn")) * time.Second)
	sqlDB.SetConnMaxLifetime(time.Hour)

	if config.GetBool("db.debug") {
		db = db.Debug()
	}

	// if config.GetString("env") != appconfig.ENV_PRODUCTION {
	// 	// db.AutoMigrate(domain.Role{})
	// }

	return db
}
