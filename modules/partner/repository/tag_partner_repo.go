package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
)

func (r *partnerRepository) CreateTag(ctx context.Context, req *domain.Tags) (err error) {
	err = r.DB.WithContext(ctx).Create(req).Error
	return
}

func (r *partnerRepository) UpdateTag(ctx context.Context, req *domain.Tags) (err error) {
	err = r.DB.WithContext(ctx).Save(req).Error
	return
}

func (r *partnerRepository) GetTagById(ctx context.Context, id string) (resp *domain.Tags, err error) {
	err = r.DB.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *partnerRepository) DeleteTag(ctx context.Context, req *domain.Tags) (err error) {
	tx := r.DB.<PERSON>gin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("tag_id = ?", req.ID).Delete(&domain.PartnerTags{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *partnerRepository) CreatePartnerTag(ctx context.Context, req *domain.PartnerTags, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *partnerRepository) UpdatePartnerTag(ctx context.Context, req *domain.PartnerTags, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *partnerRepository) GetPartnerTagById(ctx context.Context, id string) (resp *domain.PartnerTags, err error) {
	err = r.DB.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *partnerRepository) DeletePartnerTag(ctx context.Context, req *domain.PartnerTags) (err error) {
	err = r.DB.WithContext(ctx).Delete(req).Error
	return
}

func (r *partnerRepository) DeletePartnerTagByPartnerId(ctx context.Context, partnerId string, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Where("partner_id = ?", partnerId).Delete(&domain.PartnerTags{}).Error
	return
}

func (r *partnerRepository) GetPartnerTagByPartnerId(ctx context.Context, partnerId string) (resp *[]domain.PartnerTags, err error) {
	err = r.DB.WithContext(ctx).Where("partner_id = ?", partnerId).Find(&resp).Error
	return
}

func (r *partnerRepository) GetPartnerTagByTagId(ctx context.Context, tagId string) (resp *[]domain.PartnerTags, err error) {
	err = r.DB.WithContext(ctx).Where("tag_id = ?", tagId).Find(&resp).Error
	return
}

func (r *partnerRepository) GetListAllTag(ctx context.Context) (resp *[]domain.Tags, err error) {
	err = r.DB.WithContext(ctx).Where("id > 0").Find(&resp).Error
	return
}
