package usecase

import (
	"net/http"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	"repo.nusatek.id/moaja/backend/libraries/message-broker/message"

	messagebroker "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	wbs "repo.nusatek.id/nusatek/payment/infrastructure/webhook"
	"repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	redisretry "repo.nusatek.id/nusatek/payment/modules/webhook/pkg"
	"repo.nusatek.id/nusatek/payment/modules/webhook/repository"
)

type defaultWebhook struct {
	webhookRepo  repository.WebhookDeliveryLogRepository
	companyRepo  usecase.CompanyManagementRepo
	webhookSvc   *wbs.SimpleWebhookService
	retryService *redisretry.RedisRetry
}

func Setup() *defaultWebhook {
	return &defaultWebhook{}
}

func (s *defaultWebhook) SetWebhookRepo(r repository.WebhookDeliveryLogRepository) *defaultWebhook {
	s.webhookRepo = r
	return s
}

func (s *defaultWebhook) SetCompanyRepo(r usecase.CompanyManagementRepo) *defaultWebhook {
	s.companyRepo = r
	return s
}

func (s *defaultWebhook) SetRetryService(r *redisretry.RedisRetry) *defaultWebhook {
	s.retryService = r
	return s
}

func (s *defaultWebhook) SetWebhookService() *defaultWebhook {
	httpConfig := wbs.HTTPClientConfig{
		Timeout:         10 * time.Second,
		MaxIdleConns:    100,
		IdleConnTimeout: 90 * time.Second,
	}

	// Create circuit breaker config
	circuitConfig := wbs.CircuitBreakerConfig{
		MaxFailures:  5,
		ResetTimeout: 30 * time.Second,
	}

	client := wbs.NewRestyHTTPClient(httpConfig, circuitConfig)
	client.SetDebugMode(true)
	initCashInQueue()

	webhookSvc := wbs.NewSimpleWebhookService(wbs.WithSimpleHTTPClient(client))

	s.webhookSvc = webhookSvc
	return s
}

func initCashInQueue() {
	messagebroker.PublishMessage("", &message.Message{
		Type:    config.CONTENT_TYPE_JSON,
		Content: []byte(`{"type": "cash_in"}`),
	})
}

func (s *defaultWebhook) Validate() WebhookService {
	if s.companyRepo == nil {
		panic("company repository is nil")
	}

	if s.webhookRepo == nil {
		panic("webhook repository is nil")
	}

	return s
}

type basicAuth struct {
	Token string
}

func (b basicAuth) Apply(req *http.Request) error {
	req.Header.Set("Authorization", "Basic "+b.Token)
	return nil
}
