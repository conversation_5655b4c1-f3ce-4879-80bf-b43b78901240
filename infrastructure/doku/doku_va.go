package doku

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (w *wrapper) getVAPath(bankChannel string) (string, error) {
	bankChannel = strings.ToLower(bankChannel)
	path, exist := w.vaBankApiPath[bankChannel]
	if !exist {
		return "", errors.SetError(http.StatusNotFound, "va bank channel not found")
	}
	return path, nil
}

func (w *wrapper) validateVAReq(vaPath string, req *VAPaymentCodeReq) error {
	if vaPath == VA_BANK_CHANNEL_BNI_API_PATH { //need replace value for bni channel
		switch req.VirtualAccountInfo.BillingType {
		case VA_BILLING_TYPE_FIX_BILL:
			req.VirtualAccountInfo.BillingType = VA_BNI_BILLING_TYPE_FIXED
		default:
			return errors.SetError(http.StatusNotFound, "invalid billing type BNI mapping")
		}
	}
	if vaPath != VA_BANK_CHANNEL_BNI_API_PATH { // MerchantUniqueReference only required for BNI
		req.VirtualAccountInfo.MerchantUniqueReference = ""
	}

	return nil
}

// virtual account related
func (w *wrapper) VAPaymentCode(ctx context.Context, credential Credential, bankChannel string, req VAPaymentCodeReq) (rawResp []byte, resp VAPaymentCodeRes, err error) {
	vaPath, err := w.getVAPath(bankChannel)
	if err != nil {
		logger.Error(ctx, "getVAPath", logger.Err(err))
		return
	}
	path := vaPath + "/v2/payment-code"
	err = w.validateVAReq(vaPath, &req)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	reqB, _ := json.Marshal(req)

	hrw := HttpReqWrapper{
		Credential: credential,
		Method:     http.MethodPost,
		Path:       path,
		ReqBody:    reqB,
		Header:     make(map[string]string),
	}
	hrsp, err := w.httpReqWrapper(ctx, hrw, &resp)
	rawResp = hrsp.RespBody
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		return
	}

	return
}
