package usecase

import (
	"context"
	"net/http"
	"strconv"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (pu *defaultCompanyMappingUsecase) PaymentChannelList(ctx context.Context, req *domain.GetCompanyMappingReq, paginate utils.Pagination) (resp []entity.CompanyMappingShow, totalItem int64, err error) {

	return pu.companyMappingRepo.GetListCompanyProviderChannelMapping(ctx, req, paginate)
}

func (pu *defaultCompanyMappingUsecase) validate(ctx context.Context, provider *domain.CompanyPaymentProviderChannelMappings) (err error) {
	if provider.Capability == constants.CashInCapability && provider.ExpiredTime <= 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "expired time required for cash in capability")
		logger.Error(ctx, err.Error())
		return
	}

	_, err = pu.paymentProviderService.GetPaymentProviderById(ctx, strconv.Itoa(int(provider.PaymentProviderID)))
	if err != nil {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusBadRequest, "provider id not found")
		return
	}

	_, exist := pu.companyMappingRepo.GetCompanyByID(ctx, provider.CompanyID)
	if !exist {
		err = errors.SetErrorMessage(http.StatusBadRequest, "company id not found")
		return err
	}

	return nil
}

func (pu *defaultCompanyMappingUsecase) CreateMapping(ctx context.Context, provider *domain.CompanyPaymentProviderChannelMappings, providerCode string) (res *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = pu.validate(ctx, provider)
	if err != nil {
		return
	}

	// data, exist := pu.companyMappingRepo.GetProviderChannelMapByCode(ctx, providerCode)
	// if !exist {
	// 	return res, errors.SetErrorMessage(http.StatusBadRequest, "provider channel code not found")
	// }
	data, err := pu.companyMappingRepo.GetCompleteProviderChannelMapByCode(ctx, providerCode)
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, "provider channel code not found")
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}

	if provider.Status {
		err = pu.companyMappingRepo.UpdateCompanyChannelMappingStatusByCompanyIdAndChannelId(ctx, strconv.Itoa(int(provider.CompanyID)), strconv.Itoa(int(data.PaymentProviderChannelMappings.PaymentChannelID)), strconv.Itoa(constants.CashInCapability), false)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	if !str.Contains(constants.PaymentChannelTypeWhitelist, data.PaymentChannelTypes.PaymentType) {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid payment type channel")
		return
	}

	var vaSetting domain.ChannelTypeConfigVA

	switch data.PaymentChannelTypes.PaymentType {
	case constants.TrxVirtualAccount:
		vaSetting = provider.ChannelTypeConfigs.ToVA()
		err = vaSetting.Valid()
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return
		}
	}

	provider.PaymentChannelID = data.PaymentProviderChannelMappings.PaymentChannelID
	provider.Capability = data.PaymentProviderChannelMappings.Capability

	tx := pu.companyMappingRepo.BeginTrans()
	defer tx.Rollback()

	res, err = pu.companyMappingRepo.CreateMapping(ctx, tx, provider)
	if err != nil {
		return
	}

	switch data.PaymentChannelTypes.PaymentType {
	case constants.TrxVirtualAccount:
		_, err = pu.counterRepo.CreateIfNotExist(ctx, tx, &domain.CompanyPaymentProviderChannelMappingCounter{
			CompanyPaymentProviderChannelMappingId: int(res.ID),
			Type:                                   constants.CompanyMappingCounterTypeFixedVA,
			Counter:                                vaSetting.FixedVirtualAccountStart,
		})
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return
		}
	}

	err = tx.Commit().Error
	if err != nil {
		return
	}

	return
}

func (pu *defaultCompanyMappingUsecase) UpdateMapping(ctx context.Context, provider *domain.CompanyPaymentProviderChannelMappings, providerCode string) (res *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = pu.validate(ctx, provider)
	if err != nil {
		return
	}

	companyMapping, err := pu.companyMappingRepo.GetMappingById(ctx, int(provider.ID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	// data, exist := pu.companyMappingRepo.GetProviderChannelMapByCode(ctx, providerCode)
	// if !exist {
	// 	return res, errors.SetErrorMessage(http.StatusBadRequest, "provider channel  not found")
	// }
	data, err := pu.companyMappingRepo.GetCompleteProviderChannelMapByCode(ctx, providerCode)
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, "provider channel code not found")
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}

	if !str.Contains(constants.PaymentChannelTypeWhitelist, data.PaymentChannelTypes.PaymentType) {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid payment type channel")
		return
	}

	var vaSetting domain.ChannelTypeConfigVA
	switch data.PaymentChannelTypes.PaymentType {
	case constants.TrxVirtualAccount:
		vaSetting = provider.ChannelTypeConfigs.ToVA()
		err = vaSetting.Valid()
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return
		}
	}

	if provider.Status {
		err = pu.companyMappingRepo.UpdateCompanyChannelMappingStatusByCompanyIdAndChannelId(ctx, strconv.Itoa(int(provider.CompanyID)), strconv.Itoa(int(data.PaymentProviderChannelMappings.PaymentChannelID)), strconv.Itoa(constants.CashInCapability), false)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	provider.PaymentChannelID = data.PaymentProviderChannelMappings.PaymentChannelID
	provider.Capability = data.PaymentProviderChannelMappings.Capability
	provider.CreatedAt = companyMapping.CreatedAt

	tx := pu.companyMappingRepo.BeginTrans()
	defer tx.Rollback()

	err = pu.companyMappingRepo.UpdateMapping(ctx, tx, provider)
	if err != nil {
		return
	}

	switch data.PaymentChannelTypes.PaymentType {
	case constants.TrxVirtualAccount:
		currentSetting := companyMapping.ChannelTypeConfigs.ToVA()
		_, err = pu.counterRepo.CreateOrUpdateCounter(ctx, tx, &domain.CompanyPaymentProviderChannelMappingCounter{
			CompanyPaymentProviderChannelMappingId: int(companyMapping.ID),
			Type:                                   constants.CompanyMappingCounterTypeFixedVA,
			Counter:                                vaSetting.FixedVirtualAccountStart,
		}, currentSetting.FixedVirtualAccountStart)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
			return
		}
	}

	err = tx.Commit().Error
	if err != nil {
		return
	}
	res = provider
	return
}

func (pu *defaultCompanyMappingUsecase) ViewMapping(ctx context.Context, id int) (res interface{}, err error) {
	res, err = pu.companyMappingRepo.ViewMapping(ctx, id)
	if err != nil {
		return
	}
	return
}

func (pu *defaultCompanyMappingUsecase) DeleteMapping(ctx context.Context, ID int64) (isSuccess bool, err error) {
	_, exist := pu.companyMappingRepo.GetCompanyProviderChannelMappingByID(ctx, ID)
	if !exist {
		return false, errors.SetErrorMessage(http.StatusBadRequest, "id not found")
	}

	isSuccess, err = pu.companyMappingRepo.DeleteMapping(ctx, ID)
	return
}

func (pu *defaultCompanyMappingUsecase) ListCompanyXProviderMapping(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	res, totalItem, err = pu.companyMappingRepo.ListCompanyXProviderMapping(ctx, params, orderParam)
	if err != nil {
		return
	}
	return
}

func (pu *defaultCompanyMappingUsecase) CreateCompanyXProviderMapping(ctx context.Context, provider *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error) {
	paymentProvider, err := pu.paymentProviderService.GetPaymentProviderById(ctx, strconv.Itoa(int(provider.PaymentProviderID)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "provider id not found")
		logger.Error(ctx, err.Error())
		return
	}

	_, exist := pu.companyMappingRepo.GetCompanyByID(ctx, provider.CompanyID)
	if !exist {
		err = errors.SetErrorMessage(http.StatusNotFound, "company id not found")
		logger.Error(ctx, err.Error())
		return
	}

	companyId := strconv.Itoa(int(provider.CompanyID))
	providerId := strconv.Itoa(int(provider.PaymentProviderID))
	_, err = pu.companyMappingRepo.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err == nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment provider has been registered in company")
		logger.Error(ctx, err.Error())
		return
	}

	secrets, err := getSecretTemplateValidation(ctx, provider.ProviderSecrets, paymentProvider.SecretTemplates)
	if err != nil {
		return
	}

	provider.ProviderSecrets = secrets
	res, err = pu.companyMappingRepo.CreateCompanyXProviderMapping(ctx, provider)
	if err != nil {
		return
	}

	if err = pu.setCacheCompanyPaymentProviderMapping(ctx, provider); err != nil {
		return
	}

	return
}

func (pu *defaultCompanyMappingUsecase) UpdateCompanyXProviderMapping(ctx context.Context, req *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error) {
	providerMapping, isExist := pu.companyMappingRepo.GetCompanyProviderMappingByID(ctx, req.ID)
	if !isExist {
		err = errors.SetErrorMessage(http.StatusNotFound, "company provider mapping not found")
		return
	}
	paymentProvider, err := pu.paymentProviderService.GetPaymentProviderById(ctx, strconv.Itoa(int(req.PaymentProviderID)))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "provider id not found")
		return
	}

	_, exist := pu.companyMappingRepo.GetCompanyByID(ctx, req.CompanyID)
	if !exist {
		err = errors.SetErrorMessage(http.StatusBadRequest, "company id not found")
		return
	}

	if providerMapping.PaymentProviderID == req.PaymentProviderID {
		switch req.ProviderSecrets {
		case "":
			req.ProviderSecrets = providerMapping.ProviderSecrets

		default:
			secrets, errRes := getSecretTemplateValidation(ctx, req.ProviderSecrets, paymentProvider.SecretTemplates)
			if errRes != nil {
				err = errRes
				return
			}
			req.ProviderSecrets = secrets
		}
	}

	if providerMapping.PaymentProviderID != req.PaymentProviderID {
		companyId := strconv.Itoa(int(req.CompanyID))
		providerId := strconv.Itoa(int(req.PaymentProviderID))
		_, err = pu.companyMappingRepo.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
		if err == nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, "can't change the payment provider that has been registered in company")
			logger.Error(ctx, err.Error())
			return
		}
		secrets, errRes := getSecretTemplateValidation(ctx, req.ProviderSecrets, paymentProvider.SecretTemplates)
		if errRes != nil {
			err = errRes
			return
		}
		req.ProviderSecrets = secrets
	}

	req.CreatedAt = providerMapping.CreatedAt
	res, err = pu.companyMappingRepo.UpdateCompanyXProviderMapping(ctx, req)
	if err != nil {
		return
	}

	_ = pu.deleteCacheCompanyPaymentProviderMapping(ctx, strconv.Itoa(int(req.CompanyID)), strconv.Itoa(int(req.PaymentProviderID)))
	_ = pu.setCacheCompanyPaymentProviderMapping(ctx, req)
	return
}

func (pu *defaultCompanyMappingUsecase) ViewCompanyXProviderMapping(ctx context.Context, param map[string]interface{}) (res interface{}, err error) {
	res, err = pu.companyMappingRepo.ViewCompanyXProviderMapping(ctx, param)
	if err != nil {
		return
	}
	return
}

func (pu *defaultCompanyMappingUsecase) DeleteCompanyXProviderMapping(ctx context.Context, ID int64) (isSuccess bool, err error) {
	_, exist := pu.companyMappingRepo.GetCompanyProviderMappingByID(ctx, ID)
	if !exist {
		return false, errors.SetErrorMessage(http.StatusBadRequest, "id not found")
	}

	isSuccess, err = pu.companyMappingRepo.DeleteCompanyXProviderMapping(ctx, ID)
	return
}

func (s *defaultCompanyMappingUsecase) GetCompanyProviderMappingByCompanyIdAndProviderId(ctx context.Context, companyId, providerId string) (resp *domain.CompanyPaymentProviderMappings, err error) {
	resp, err = s.getCacheCompanyPaymentProviderMappings(ctx, companyId, providerId)
	return
}

func (s *defaultCompanyMappingUsecase) GetListAllCompanyProviderMappingChannelByCompanyID(ctx context.Context, companyId string) (resp *[]entity.ListCompanyMappingChannel, err error) {
	resp, err = s.companyMappingRepo.GetListAllCompanyProviderMappingChannelByCompanyID(ctx, companyId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyMappingUsecase) GetListAllCompanyProviderMappingAPI(ctx context.Context, auth string, req domain.GetAllCompanyProviderMappingReq) (resp *[]entity.ListCompanyMappingProviderChannel, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}
	resp, err = s.companyMappingRepo.GetListAllCompanyProviderMappingAPI(ctx, strconv.Itoa(company.ID), req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := range *resp {
		if (*resp)[i].ChannelLogoURL != "" {
			(*resp)[i].ChannelLogoURL, err = s.s3.GetURL((*resp)[i].ChannelLogoURL)
			if err != nil {
				logger.Error(ctx, err.Error())
			}
		}
	}

	return
}

func (s *defaultCompanyMappingUsecase) GetCompanyProviderMappingCodeAPI(ctx context.Context, auth, code string) (resp *entity.ListCompanyMappingProviderChannel, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}
	resp, err = s.companyMappingRepo.GetCompanyProviderMappingCodeAPI(ctx, strconv.Itoa(company.ID), code)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if resp.ChannelLogoURL != "" {
		resp.ChannelLogoURL, err = s.s3.GetURL(resp.ChannelLogoURL)
		if err != nil {
			logger.Error(ctx, err.Error())
		}
	}

	return
}

func (s *defaultCompanyMappingUsecase) GetListAllCompanyPaymentProvider(ctx context.Context, companyId, channelId string) (resp *[]entity.ListCompanyMappingChannelCashOut, err error) {
	channelResp, err := s.channelService.GetPaymentChannelById(ctx, channelId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}
	resp, err = s.companyMappingRepo.GetListAllCompanyPaymentProvider(ctx, companyId, channelResp.Name)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyMappingUsecase) GetListMappingCashInPaymentMethodAPI(ctx context.Context, auth string) (resp *[]entity.ListChannelMappingOpenAPI, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	resp, err = s.companyMappingRepo.GetListMappingCashInPaymentMethod(ctx, strconv.Itoa(company.ID))
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCompanyMappingUsecase) CreateFixedVACustomer(ctx context.Context, ignoreCounter bool, req *domain.FixedVACustomer, companyMapping domain.CompanyPaymentProviderChannelMappings) (err error) {
	vaSetting := companyMapping.ChannelTypeConfigs.ToVA()
	var counter domain.CompanyPaymentProviderChannelMappingCounter

	if !ignoreCounter {
		counter, err = s.counterRepo.GetLastCounter(ctx, int(companyMapping.ID), constants.CompanyMappingCounterTypeFixedVA)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		if counter.Counter > vaSetting.FixedVirtualAccountEnd {
			_, err = s.counterRepo.DecreaseCounter(ctx, int(companyMapping.ID), constants.CompanyMappingCounterTypeFixedVA)
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}

			err = errors.SetErrorMessage(http.StatusBadRequest, "counter exceed virtual account max range")
			logger.Error(ctx, err.Error())
			return
		}
	}

	req.CompanyPaymentProviderChannelMappingId = companyMapping.ID

	req.Counter = counter.Counter

	err = s.customerFVARepo.Create(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCompanyMappingUsecase) UpdateFixedVACustomer(ctx context.Context, tx *gorm.DB, req *domain.FixedVACustomer) (err error) {
	err = s.customerFVARepo.Update(ctx, tx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCompanyMappingUsecase) GetOrCreateCustomerFVA(ctx context.Context, ignoreCounter bool, phone string, companyMapping domain.CompanyPaymentProviderChannelMappings) (res domain.FixedVACustomer, err error) {
	if companyMapping.ID <= 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "company mapping empty")
		logger.Error(ctx, err.Error())
	}
	if len(phone) <= 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "customer phone empty")
		logger.Error(ctx, err.Error())
	}

	res, err = s.customerFVARepo.GetOne(ctx, companyMapping.ID, phone)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	res.Phone = phone
	if err == gorm.ErrRecordNotFound {
		err = s.CreateFixedVACustomer(ctx, ignoreCounter, &res, companyMapping)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	return
}

func (s *defaultCompanyMappingUsecase) GetOrCreateCustomerFVAByVaNumber(ctx context.Context, ignoreCounter bool, cust domain.FixedVACustomer, companyMapping domain.CompanyPaymentProviderChannelMappings) (res domain.FixedVACustomer, err error) {
	if companyMapping.ID <= 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "company mapping empty")
		logger.Error(ctx, err.Error())
	}
	if len(cust.VANumber) <= 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "customer phone empty")
		logger.Error(ctx, err.Error())
	}

	res, err = s.customerFVARepo.GetOneByVA(ctx, companyMapping.ID, cust.VANumber)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	res.VANumber = cust.VANumber
	res.Phone = cust.Phone
	res.Name = cust.Name
	if err == gorm.ErrRecordNotFound {
		err = s.CreateFixedVACustomer(ctx, ignoreCounter, &res, companyMapping)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	return
}

func (s *defaultCompanyMappingUsecase) GetOneCustomerFVAByPhone(ctx context.Context, phone string, channelMappingID int64) (res domain.FixedVACustomer, err error) {
	if len(phone) <= 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "customer phone empty")
		logger.Error(ctx, err.Error())
	}

	res, err = s.customerFVARepo.GetOne(ctx, channelMappingID, phone)
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}

	return
}

func (s *defaultCompanyMappingUsecase) UpdateStatusCompanyXProviderMapping(ctx context.Context, id int64, status bool) (res *domain.CompanyPaymentProviderMappings, err error) {
	mapping, exist := s.companyMappingRepo.GetCompanyProviderMappingByID(ctx, id)
	if !exist {
		return nil, errors.SetErrorMessage(http.StatusBadRequest, "id not found")
	}

	mapping.Status = status
	res, err = s.companyMappingRepo.UpdateCompanyXProviderMapping(ctx, &mapping)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}
	return
}

func (s *defaultCompanyMappingUsecase) UpdateStatusChannelMapping(ctx context.Context, id int64, status bool) (res *domain.CompanyProductProviderChannelMapping, err error) {
	err = s.companyMappingRepo.UpdateCompanyChannelMappingStatusById(ctx, id, status)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}
	return
}
