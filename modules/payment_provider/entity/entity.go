package entity

import (
	"encoding/json"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

type PaymentProviderList struct {
	ID              int    `json:"id"`
	Name            string `json:"name"`
	Code            string `json:"code"`
	Status          bool   `json:"status"`
	SecretTemplates string `json:"secret_templates"`
}

type PaymentProvidersShow struct {
	ID              int64                          `gorm:"primaryKey" json:"id"`
	Code            string                         `json:"code" validate:"min=4,alphanum"`
	Name            string                         `json:"name"`
	Status          bool                           `json:"status"`
	SecretTemplates []model.SecretProviderTemplate `json:"secret_templates"`
}

func (s *PaymentProvidersShow) Conver() domain.PaymentProviders {
	secretTemp, _ := json.Marshal(s.SecretTemplates)
	return domain.PaymentProviders{
		ID:              s.ID,
		Code:            s.Code,
		Name:            s.Name,
		Status:          s.Status,
		SecretTemplates: string(secretTemp),
	}
}

func ConverPaymentProviderListToShow(req *[]PaymentProviderList) *[]PaymentProvidersShow {
	var show = []PaymentProvidersShow{}
	for i := 0; i < len(*req); i++ {
		var templates = []model.SecretProviderTemplate{}
		_ = json.Unmarshal([]byte((*req)[i].SecretTemplates), &templates)
		payment := PaymentProvidersShow{
			ID:              int64((*req)[i].ID),
			Code:            (*req)[i].Code,
			Name:            (*req)[i].Name,
			Status:          (*req)[i].Status,
			SecretTemplates: templates,
		}
		show = append(show, payment)
	}
	return &show
}

func ConverPaymentProviderToProviderShow(req *domain.PaymentProviders) PaymentProvidersShow {
	var secretTemp = []model.SecretProviderTemplate{}
	_ = json.Unmarshal([]byte(req.SecretTemplates), &secretTemp)
	return PaymentProvidersShow{
		ID:              req.ID,
		Code:            req.Code,
		Name:            req.Name,
		Status:          req.Status,
		SecretTemplates: secretTemp,
	}
}

type DefaultPaymentProvider struct {
	Name string `json:"name"`
}
