package usecase

import (
	"context"
	"net/http"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_product/contract"
	"repo.nusatek.id/nusatek/payment/utils"
)

type CashinTranscationRepo interface {
	BeginTrans() *gorm.DB
	CreateCashinTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactions) (err error)
	UpdateCashinTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactions) (err error)
	GetCashinTransactionById(ctx context.Context, id string) (resp *domain.CashInTransactions, err error)
	GetCashinTransactionByIdDetail(ctx context.Context, id string) (resp domain.CashInTransactionDetail, err error)
	DeleteCashinTranscation(ctx context.Context, req *domain.CashInTransactions) (err error)
	GetCompanyMappingCashIn(ctx context.Context, companyId, providerId, channelId string) (resp *domain.CompanyPaymentProviderChannelMappings, err error)
	GetCashinTranscationList(ctx context.Context, param entity.CashinListParam) (resp *[]entity.CashinList, totalData int64, err error)
	GetListItemsByTransactionID(ctx context.Context, p utils.Pagination, transactionID int64) (resp []domain.CashInTransactionItemComplete, totalData int64, err error)
	GetListTranscationHistoryByCashInTransactionID(ctx context.Context, paginate utils.Pagination, id string) (resp *[]entity.CashInTransactionHistories, totalData int64, err error)
	GetCashinTransactionByInvoiceNumber(ctx context.Context, invoiceNumber string) (resp *domain.CashInTransactions, err error)
	GetCashinBulk(ctx context.Context, ids []int) (resp *[]domain.CashInTransactions, err error)
	GetCashinByStatus(ctx context.Context, status string) (resp *[]domain.CashInTransactions, err error)
	CreateCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactionHistories) (err error)
	CreateBulkCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req []domain.CashInTransactionHistories) (err error)
	UpdateCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactionHistories) (err error)
	UpdateCashinTransactionHistoryFromCallback(ctx context.Context, req *domain.CashInTransactionHistories, cashinTransactionId, providerId, channelId int) (err error)
	UpdateCashinTransactionItems(ctx context.Context, req *domain.CashInTransactionItems, tx *gorm.DB) (err error)
	GetCashinTranscationItemByCashinId(ctx context.Context, id string) (resp *[]domain.CashInTransactionItems, err error)
	GetCashOutTransactionDetailsByCashinTranscationItemId(ctx context.Context, id string) (resp *domain.CashOutTransactionDetails, err error)
	UpdateStatusCashOutTranscation(ctx context.Context, id int, status string) (err error)
	UpdateStatusCashInTransaction(ctx context.Context, tx *gorm.DB, id int, status string) (err error)
	CreateCashOutTranscationHistory(ctx context.Context, req *domain.CashOutTransactionHistories) (err error)
	UpdateStatusCashinTransactionItemsByCashinTranscationId(ctx context.Context, id int, status string) (err error)
	GetCashinTransactionHistoryFromCallback(ctx context.Context, cashinTransactionId, providerId, channelId int) (resp *domain.CashInTransactionHistories, err error)
	GetCashinTransactionHistoryByPaymentStatus(ctx context.Context, cashinTransactionId int, paymentStatus string) (resp *domain.CashInTransactionHistories, err error)
	CreateCashOutTransaction(ctx context.Context, req *domain.CashOutTransactions, tx *gorm.DB) (err error)
	CreateCashOutTransactionDetail(ctx context.Context, req *domain.CashOutTransactionDetails, tx *gorm.DB) (err error)
	CreateBulkCashOutTransactionDetail(ctx context.Context, req *[]domain.CashOutTransactionDetails, tx *gorm.DB) (err error)
	DeleteCashoutTranscationDetail(ctx context.Context, cashoutId, itemId int, tx *gorm.DB) (err error)
	CreateCompanyCashFlow(ctx context.Context, req *domain.CompanyCashFlows, tx *gorm.DB) (err error)
	CreateCompanyCashFlowBulk(ctx context.Context, req []domain.CompanyCashFlows, tx *gorm.DB) (err error)
	UpdateCompanyCashFlowByCashOutId(ctx context.Context, req *domain.CompanyCashFlows) (err error)
	FilterCashInItemsByDate(ctx context.Context, date string) (resp *[]domain.CashInTransactionItems, err error)
	FilterUnforwardedCashInItems(ctx context.Context) (resp []domain.CashInTransactionItemsCashout, err error)
	FilterUnforwardedOutstandingCashInItems(ctx context.Context) (resp []domain.CashInTransactionItemsCashout, err error)
	GetLastCashOutTransactions(ctx context.Context) (resp *domain.CashOutTransactions, err error)
	GetCashinTransactionItemsById(ctx context.Context, id string) (resp *domain.CashInTransactionItems, err error)
	GetCashinTransactionItemsByIds(ctx context.Context, ids []int) (resp []domain.CashInTransactionItems, err error)
	GetCashinTransactionItemsByIdsWithCashIn(ctx context.Context, ids []int) (resp []domain.CashInTransactionItemsWithCashIn, err error)
	SelectCashOutByPartnerIds(ctx context.Context, partnerIds []int, status string) (resp []domain.CashOutTransactions, err error)
	GetCashInByCashoutID(ctx context.Context, cashoutID int) (resp []domain.CashInTransactionItems, err error)
	UpdateIsCashoutBulk(ctx context.Context, tx *gorm.DB, ids []int) (err error)
	UpdateIsCashoutAndStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status string) (err error)
	UpdateItemStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status string) (err error)
	UpdatePartialCashoutTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashOutTransactions) (err error)
	GetCashinItems(ctx context.Context, cashinID int, isCashout bool) (resp *[]domain.CashInTransactionItems, err error)
	UpdateStatusBulkCashInTranscation(ctx context.Context, tx *gorm.DB, ids []int, status string) (res []domain.CashInTransactions, err error)
	GetAllByIDsWithItemCount(ctx context.Context, tx *gorm.DB, cashInIDs []int) (res []domain.CashInTransactionWithItemCount, err error)
	Export(ctx context.Context, req *domain.CashinExportReq) (res []domain.CashinExportRes, err error)
	GetLastCashinHistoryByVaAndStatus(ctx context.Context, va string, status string) (resp *domain.CashInTransactionHistories, err error)
	GetTotalCompanyProductFees(ctx context.Context, cashinId []int64) (fees float64, err error)

	contract.CashInTransactionProductFee
}

type CashinTransactionUsecase interface {
	CreateCashinTranscation(ctx context.Context, req entity.CashinRequest, auth string) (resp entity.CashinTranscationResponse, err error)
	UpdateTranscactionCashIn(ctx context.Context, req entity.CashinUpdateTransaction, cashInTransactionId, auth string) (resp entity.CashinTranscationResponse, err error)
	SearchCashinTranscation(ctx context.Context, param entity.CashinListParam) (resp *[]entity.CashinList, totalData int64, err error)
	GetByIDDetail(ctx context.Context, id int) (res domain.CashInTransactionDetail, err error)
	CashinPaymentActivityLogByTransactionID(ctx context.Context, paginate utils.Pagination, id string) (resp []entity.CashinActivityLog, totalData int64, err error)
	GetListItemsByTransactionID(ctx context.Context, paginate utils.Pagination, transactionID int64) (resp []domain.CashInTransactionItemComplete, totalData int64, err error)
	PushCallbackXfersCashin(ctx context.Context, signature, paymentType string, req interface{}) (err error)
	CallbackXfersCashinTranscationProcess(ctx context.Context, req xfers.PaymentResponse)
	PushCallbackNicePayCashin(ctx context.Context, req interface{}) (err error)
	PushCallbackSnapNicePayCashin(ctx context.Context, req interface{}) (err error)
	CallbackNicePayCashInTransactionProcess(ctx context.Context, res nicepay.NotificationRequest)
	CallbackSnapNicePayCashInTransactionProcess(ctx context.Context, req snapclient.NicepaySnapNotifRequest)
	PushCallbackXenditCashin(ctx context.Context, token string, req interface{}, paymentType string) (err error)
	CallbackXenditCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte)
	PushCallbackOttocashCashin(ctx context.Context, signature, timestamp string, req interface{}) (err error)
	CallbackOttocashCashinTranscationProcess(ctx context.Context, referenceId, paymentId, responseCode string, body []byte)
	PushCallbackDokuCashin(ctx context.Context, header http.Header, reqByte []byte, paymentType string) (err error)
	CallbackDokuCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte)
	PushCallbackCashlez(ctx context.Context, header http.Header, reqByte []byte, paymentType string) (err error)
	CallbackCashlezCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte)
	CallbackBankBcaSnapCashinTranscationProcess(ctx context.Context, cb domain.SnapCbCashInTransaction)
	//SNAP
	UpdateStatusSnapCashinTransaction(ctx context.Context, cashIn *domain.CashInTransactions, cbData domain.SnapCbCashInTransaction, opts domain.UpdateWachingStatusCashinOpts) error

	GetListCashinTransactionAPI(ctx context.Context, param entity.CashinListParam, auth string) (resp *[]entity.CashinList, totalData int64, err error)
	CheckStatusCashinAPI(ctx context.Context, id, auth string) (resp entity.CashinList, err error)
	BatchingCashInToCashOut(ctx context.Context) (err error)
	BuildCashOutTransaction(ctx context.Context) (err error)
	RefundCashinTranscation(ctx context.Context, req entity.CashinRefundRequest, auth string) (resp *domain.CashInTransactions, err error)
	RefundPaymentCashinTranscation(ctx context.Context, req entity.CashinBulkRequest) (err error)
	CheckPendingCashInTransaction(ctx context.Context) (err error)

	Export(ctx context.Context, req *domain.CashinExportReq) (filename string, err error)

	CheckUnpaidBillVA(ctx context.Context, req *entity.CheckUnpaidBillVARequest, auth string) (res entity.CheckUnpaidBillVAResponse, err error)

	GetPaymentInstructions(ctx context.Context, invNumber, auth string) (res entity.PaymentInstructionResp, err error)

	RetryCallback(ctx context.Context, id int) (err error)

	ManualPayment(ctx context.Context, req entity.ManualPaymentRequest) (err error)
	PublishCashOut(ctx context.Context, data domain.CashOutCallback) (err error)
	CancelCashInTransaction(ctx context.Context, req *entity.CashInCancelationRequest, auth string) (err error)
	SwitchCashinPaymentChannel(ctx context.Context, req *entity.CashInChangeChannelRequest, auth string) (resp entity.CashinTranscationResponse, err error)
}
