package repository

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
)

type companyMappingRepo struct {
	DB *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.CompanyMappingRepository {
	return &companyMappingRepo{db}
}

func (pp *companyMappingRepo) BeginTrans() *gorm.DB {
	return pp.DB.Begin()
}

func (pp *companyMappingRepo) PaymentChannel(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	var result []interface{}

	condition := "AND m.deleted_at IS NULL "

	type provider struct {
		ID                int64   `json:"id"`
		PaymentProviderID int64   `json:"payment_provider_id"`
		PaymentChannelID  int64   `json:"payment_channel_id"`
		CompanyID         int64   `json:"company_id"`
		ProviderName      string  `json:"provider_name"`
		ChannelName       string  `json:"channel_name"`
		CompanyName       string  `json:"company_name"`
		Code              string  `json:"code"`
		MaxTransaction    float64 `json:"max_transaction"`
		FeeFixValue       float64 `json:"fee_fix_value"`
		FeePercentage     float64 `json:"fee_percentage"`
		Sla               int     `json:"sla"`
		CashIn            bool    `json:"cash_in"`
		CashOut           bool    `json:"cash_out"`
		ExpiredTime       int64   `json:"expired_time"`
	}
	var pr []provider

	type total struct {
		TotalItem int64 `json:"total_item"`
	}
	tot := new(total)

	query := "SELECT COUNT(m.id) AS total_item " +
		"FROM company_payment_provider_channel_mappings m " +
		"WHERE 1=1 AND m.company_id = @company_id " + condition

	err = pp.DB.WithContext(ctx).Raw(query, params).Scan(&tot).Error

	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
		return nil, 0, err
	}

	query = "SELECT m.id, m.company_id, m.payment_provider_id, m.payment_channel_id, m.fee_fix_value, m.fee_percentage, " +
		"p.name AS provider_name, c.name AS channel_name, s.name AS company_name, m3.code, m3.max_transaction, m3.sla, m4.expired_time, " +
		"CASE WHEN m.capability = 1 THEN true ELSE false END AS cash_in, CASE WHEN m.capability = 2 THEN true ELSE false END AS cash_out  " +
		"FROM company_payment_provider_channel_mappings m " +
		"LEFT JOIN payment_providers p ON p.id = m.payment_provider_id " +
		"LEFT JOIN payment_channels c ON c.id = m.payment_channel_id " +
		"LEFT JOIN companies s ON s.id = m.company_id " +
		"JOIN LATERAL (SELECT * FROM payment_provider_channel_mappings m2 WHERE m2.payment_provider_id = m.payment_provider_id AND m2.payment_channel_id = m2.payment_channel_id ORDER BY m2.updated_at DESC LIMIT 1) m3 ON TRUE " +
		"JOIN LATERAL (SELECT * FROM company_payment_provider_channel_mappings m2 WHERE m2.company_id = @company_id AND m2.payment_provider_id = m.payment_provider_id AND m2.payment_channel_id = m2.payment_channel_id ORDER BY m2.updated_at DESC LIMIT 1) m4 ON TRUE " +
		"WHERE 1=1 AND m.company_id = @company_id " + condition +
		"ORDER BY m.created_at DESC " +
		"OFFSET @start LIMIT @limit "
	err = pp.DB.WithContext(ctx).Raw(query, params, orderParam).Scan(&pr).Error
	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
	}

	for _, v := range pr {
		m := map[string]interface{}{
			"id":                  v.ID,
			"payment_provider_id": v.PaymentProviderID,
			"payment_channel_id":  v.PaymentChannelID,
			"company_id":          v.CompanyID,
			"provider_name":       v.ProviderName,
			"channel_name":        v.ChannelName,
			"company_name":        v.CompanyName,
			"code":                v.Code,
			"max_transaction":     v.MaxTransaction,
			"fee_fix_value":       v.FeeFixValue,
			"fee_percentage":      v.FeePercentage,
			"sla":                 v.Sla,
			"cash_in":             v.CashIn,
			"cash_out":            v.CashOut,
			"expired_time":        v.ExpiredTime,
		}
		result = append(result, m)
	}
	return result, tot.TotalItem, err
}

func (pp *companyMappingRepo) CreateMapping(ctx context.Context, tx *gorm.DB, providerMapping *domain.CompanyPaymentProviderChannelMappings) (res *domain.CompanyPaymentProviderChannelMappings, err error) {
	rs := pp.DB.WithContext(ctx).Create(&providerMapping)
	if rs.Error != nil {
		logger.Error(ctx, "error create", logger.Err(err))
		return res, rs.Error
	}

	return providerMapping, err
}

func (pp *companyMappingRepo) GetMappingById(ctx context.Context, id int) (resp *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = pp.DB.WithContext(ctx).First(&resp, id).Error
	return
}

func (pp *companyMappingRepo) UpdateMapping(ctx context.Context, tx *gorm.DB, providerMapping *domain.CompanyPaymentProviderChannelMappings) (err error) {
	err = pp.DB.WithContext(ctx).Save(&providerMapping).Error
	return
}

func (pp *companyMappingRepo) DeleteMapping(ctx context.Context, ID int64) (bool, error) {
	rs := pp.DB.WithContext(ctx).Where("id = ?", ID).Delete(&domain.CompanyPaymentProviderChannelMappings{})
	if rs.RowsAffected > 0 {
		return true, rs.Error
	} else {
		logger.Error(ctx, "error delete", logger.Err(rs.Error))
		return false, rs.Error
	}
}

func (pp *companyMappingRepo) ViewMapping(ctx context.Context, id int) (res interface{}, err error) {
	var result interface{}

	type provider struct {
		ID                 int64                     `json:"id"`
		PaymentProviderID  int64                     `json:"payment_provider_id"`
		PaymentChannelID   int64                     `json:"payment_channel_id"`
		CompanyID          int64                     `json:"company_id"`
		ProviderName       string                    `json:"provider_name"`
		ChannelName        string                    `json:"channel_name"`
		CompanyName        string                    `json:"company_name"`
		Code               string                    `json:"code"`
		MaxTransaction     float64                   `json:"max_transaction"`
		FeeFixValue        float64                   `json:"fee_fix_value"`
		FeePercentage      float64                   `json:"fee_percentage"`
		ExpiredTime        int64                     `json:"expired_time"`
		Status             bool                      `json:"status"`
		ChannelTypeConfigs domain.ChannelTypeConfigs `json:"channel_type_configs" gorm:"column:channel_type_configs"`
	}
	var pr provider

	query := `SELECT
		cppcm.id,
		cppcm.payment_provider_id,
		cppcm.payment_channel_id,
		cppcm.company_id,
		pp."name" "provider_name",
		pc."name" "channel_name",
		c."name" "company_name",
		ppcm.code,
		ppcm.max_transaction,
		cppcm.fee_fix_value,
		cppcm.fee_percentage,
		ppcm.sla,
		cppcm.capability = 1 "cash_in",
		cppcm.capability = 2 "cash_out",
		cppcm.expired_time,
		cppcm.status,
		cppcm.channel_type_configs 
	FROM
		company_payment_provider_channel_mappings cppcm
	LEFT JOIN payment_provider_channel_mappings ppcm ON
		ppcm.payment_provider_id = cppcm.payment_provider_id
		AND ppcm.payment_channel_id = cppcm.payment_channel_id
		AND ppcm.capability = cppcm.capability
		AND ppcm.deleted_at IS NULL
	INNER JOIN payment_providers pp ON
		pp.id = ppcm.payment_provider_id
	INNER JOIN payment_channels pc ON
		pc.id = ppcm.payment_channel_id
	INNER JOIN companies c ON
		cppcm.company_id = c.id
	WHERE
		cppcm.deleted_at IS NULL
		AND cppcm.id = ?`
	rs := pp.DB.WithContext(ctx).Raw(query, id).Scan(&pr)
	if rs.Error != nil {
		logger.Error(ctx, "error query", logger.Err(err))
	}
	if rs.RowsAffected > 0 {
		m := map[string]interface{}{
			"id":                   pr.ID,
			"payment_provider_id":  pr.PaymentProviderID,
			"payment_channel_id":   pr.PaymentChannelID,
			"company_id":           pr.CompanyID,
			"provider_name":        pr.ProviderName,
			"channel_name":         pr.ChannelName,
			"company_name":         pr.CompanyName,
			"code":                 pr.Code,
			"max_transaction":      pr.MaxTransaction,
			"fee_fix_value":        pr.FeeFixValue,
			"fee_percentage":       pr.FeePercentage,
			"channel_type_configs": pr.ChannelTypeConfigs,
			"expired_time":         pr.ExpiredTime,
			"status":               pr.Status,
		}
		result = m
	}

	return result, err
}

func (pp *companyMappingRepo) GetProviderChannelMapByCode(ctx context.Context, code string) (res domain.PaymentProviderChannelMappings, exist bool) {
	var channel domain.PaymentProviderChannelMappings

	rs := pp.DB.WithContext(ctx).Where("code", code).First(&channel).RowsAffected
	if rs > 0 {
		return channel, true
	} else {
		return channel, false
	}
}

func (pp *companyMappingRepo) GetCompleteProviderChannelMapByCode(ctx context.Context, code string) (res domain.PaymentProviderChannelMappingsComplete, err error) {

	err = pp.DB.Debug().WithContext(ctx).Raw(`
	SELECT ppcm.id, ppcm.payment_provider_id, ppcm.payment_channel_id, ppcm.code, ppcm.max_transaction, ppcm.sla, ppcm.capability, ppcm.provider_channel_code,
	pct.payment_type
	FROM payment_provider_channel_mappings ppcm
	JOIN payment_channels pc ON pc.id = ppcm.payment_channel_id
	JOIN payment_channel_types pct ON pct.id = pc.payment_channel_type_id
	WHERE ppcm.code = ? AND ppcm.deleted_at IS NULL ORDER BY ppcm.id DESC LIMIT 1`, code).Row().Scan(
		&res.PaymentProviderChannelMappings.ID, &res.PaymentProviderChannelMappings.PaymentProviderID, &res.PaymentProviderChannelMappings.PaymentChannelID,
		&res.PaymentProviderChannelMappings.Code, &res.PaymentProviderChannelMappings.MaxTransaction, &res.PaymentProviderChannelMappings.Sla,
		&res.PaymentProviderChannelMappings.Capability, &res.PaymentProviderChannelMappings.ProviderChannelCode,
		&res.PaymentChannelTypes.PaymentType,
	)

	return
}

func (pp *companyMappingRepo) ListCompanyXProviderMapping(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	var result []interface{}

	condition := "AND m.deleted_at IS NULL "
	searchCondition := ""

	if _, ok := params["search"]; ok {
		search := "%" + fmt.Sprint(params["search"]) + "%"
		params["search"] = search
		searchCondition = "AND (p.name ILIKE @search OR p.code ILIKE @search OR c.name ILIKE @search) "
	}

	type provider struct {
		ID                int64  `json:"id"`
		CompanyID         int64  `json:"company_id"`
		PaymentProviderId int64  `json:"payment_provider_id"`
		ProviderName      string `json:"provider_name"`
		CompanyName       string `json:"company_name"`
		ProviderSecrets   string `json:"provider_secrets"`
		Status            bool   `json:"status"`
	}
	var pr []provider

	type total struct {
		TotalItem int64 `json:"total_item"`
	}
	tot := new(total)

	query := "SELECT COUNT(m.id) AS total_item " +
		"FROM company_payment_provider_mappings m " +
		"LEFT JOIN payment_providers p ON p.id = m.payment_provider_id " +
		"LEFT JOIN companies c ON c.id = m.company_id " +
		"WHERE 1=1 " + condition + searchCondition
	if len(params) == 0 {
		err = pp.DB.WithContext(ctx).Raw(query).Scan(&tot).Error
	} else {
		err = pp.DB.WithContext(ctx).Raw(query, params).Scan(&tot).Error
	}

	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
		return nil, 0, err
	}

	query = "SELECT m.id, m.company_id, m.payment_provider_id, p.name AS provider_name, c.name AS company_name, m.provider_secrets, m.status " +
		"FROM company_payment_provider_mappings m " +
		"LEFT JOIN payment_providers p ON p.id = m.payment_provider_id " +
		"LEFT JOIN companies c ON c.id = m.company_id " +
		"WHERE 1=1 " + condition + searchCondition +
		"ORDER BY m.created_at DESC " +
		"OFFSET @start LIMIT @limit "
	err = pp.DB.WithContext(ctx).Raw(query, params, orderParam).Scan(&pr).Error
	if err != nil {
		logger.Error(ctx, "error query", logger.Err(err))
	}

	for _, v := range pr {
		m := map[string]interface{}{
			"id":                  v.ID,
			"company_id":          v.CompanyID,
			"payment_provider_id": v.PaymentProviderId,
			"provider_name":       v.ProviderName,
			"company_name":        v.CompanyName,
			"provider_secrets":    v.ProviderSecrets,
			"status":              v.Status,
		}
		result = append(result, m)
	}
	return result, tot.TotalItem, err
}

func (pp *companyMappingRepo) CreateCompanyXProviderMapping(ctx context.Context, companyMapping *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error) {
	rs := pp.DB.WithContext(ctx).Create(&companyMapping)
	if rs.Error != nil {
		logger.Error(ctx, "error create", logger.Err(rs.Error))
		return res, rs.Error
	}

	return companyMapping, err
}

func (pp *companyMappingRepo) UpdateCompanyXProviderMapping(ctx context.Context, companyMapping *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error) {
	rs := pp.DB.WithContext(ctx).Save(&companyMapping)
	if rs.Error != nil {
		logger.Error(ctx, "error update", logger.Err(rs.Error))
		return res, rs.Error
	}

	return companyMapping, err
}

func (pp *companyMappingRepo) ViewCompanyXProviderMapping(ctx context.Context, param map[string]interface{}) (res interface{}, err error) {
	var result interface{}

	type provider struct {
		ID                int64  `json:"id"`
		CompanyID         int64  `json:"company_id"`
		PaymentProviderId int64  `json:"payment_provider_id"`
		ProviderName      string `json:"provider_name"`
		CompanyName       string `json:"company_name"`
		ProviderSecrets   string `json:"provider_secrets"`
		Status            bool   `json:"status"`
	}
	var pr provider

	condition := "AND m.deleted_at IS NULL "
	query := "SELECT m.id, m.company_id, m.payment_provider_id, p.name AS provider_name, c.name AS company_name, m.provider_secrets, m.status " +
		"FROM company_payment_provider_mappings m " +
		"LEFT JOIN payment_providers p ON p.id = m.payment_provider_id " +
		"LEFT JOIN companies c ON c.id = m.company_id " +
		"WHERE 1=1 AND m.id = @id " + condition
	rs := pp.DB.WithContext(ctx).Raw(query, param).Scan(&pr)
	if rs.Error != nil {
		logger.Error(ctx, "error query", logger.Err(err))
	}
	if rs.RowsAffected > 0 {
		m := map[string]interface{}{
			"id":                  pr.ID,
			"company_id":          pr.CompanyID,
			"payment_provider_id": pr.PaymentProviderId,
			"provider_name":       pr.ProviderName,
			"company_name":        pr.CompanyName,
			"provider_secrets":    pr.ProviderSecrets,
			"status":              pr.Status,
		}
		result = m
	}

	return result, err
}

func (pp *companyMappingRepo) DeleteCompanyXProviderMapping(ctx context.Context, ID int64) (bool, error) {
	rs := pp.DB.WithContext(ctx).Where("id = ?", ID).Delete(&domain.CompanyPaymentProviderMappings{})
	if rs.RowsAffected > 0 {
		return true, rs.Error
	} else {
		logger.Error(ctx, "error delete", logger.Err(rs.Error))
		return false, rs.Error
	}
}

func (pp *companyMappingRepo) GetCompanyByID(ctx context.Context, ID int64) (res domain.Companies, exist bool) {
	var company domain.Companies

	rs := pp.DB.WithContext(ctx).Where("status = true").First(&company, ID).RowsAffected
	if rs > 0 {
		return company, true
	} else {
		return company, false
	}
}

func (pp *companyMappingRepo) GetCompanyProviderMappingByID(ctx context.Context, ID int64) (res domain.CompanyPaymentProviderMappings, exist bool) {
	var company domain.CompanyPaymentProviderMappings
	rs := pp.DB.WithContext(ctx).First(&company, ID).RowsAffected
	if rs > 0 {
		return company, true
	} else {
		return company, false
	}
}

func (pp *companyMappingRepo) GetCompanyProviderChannelMappingByID(ctx context.Context, ID int64) (res domain.CompanyPaymentProviderChannelMappings, exist bool) {
	var company domain.CompanyPaymentProviderChannelMappings

	rs := pp.DB.WithContext(ctx).First(&company, ID).RowsAffected
	if rs > 0 {
		return company, true
	} else {
		return company, false
	}
}

func (pp *companyMappingRepo) GetCompanyProviderChannelMappingByProviderIdNChannelID(ctx context.Context, providerID int, channelID int) (resp *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = pp.DB.WithContext(ctx).Where("payment_provider_id = ? AND payment_channel_id = ?", providerID, channelID).First(&resp).Error
	return
}

func (r *companyMappingRepo) GetListCompanyProviderChannelMapping(ctx context.Context, req *domain.GetCompanyMappingReq, paginate utils.Pagination) (resp []entity.CompanyMappingShow, totalData int64, err error) {
	orderByMap := map[string]string{
		"channel_name":    "pc.name",
		"code":            "ppcm.code",
		"fee_fix_value":   "cppcm.fee_fix_value",
		"fee_percentage":  "cppcm.fee_percentage",
		"max_transaction": "ppcm.max_transaction",
		"expired_time":    "cppcm.expired_time",
		"cash_in":         "cppcm.capability",
		"cash_out":        "cppcm.capability",
		"status":          "cppcm.status",
	}

	if paginate.Order == "cash_out" {
		if strings.ToLower(paginate.Sort) == "asc" {
			paginate.Sort = "desc"
		} else if strings.ToLower(paginate.Sort) == "desc" {
			paginate.Sort = "asc"
		}
	}

	q := r.DB.WithContext(ctx).Table("company_payment_provider_channel_mappings cppcm").Select(`cppcm.id, cppcm.payment_provider_id, cppcm.payment_channel_id, cppcm.company_id,
	pp."name" "provider_name",pc."name" "channel_name", c."name" "company_name",
	ppcm.code, ppcm.max_transaction, cppcm.fee_fix_value, cppcm.fee_percentage, 
	ppcm.sla, cppcm.capability = 1 "cash_in", cppcm.capability = 2 "cash_out",
	cppcm.expired_time, cppcm.status`).
		Joins(`LEFT JOIN payment_provider_channel_mappings ppcm ON ppcm.payment_provider_id = cppcm.payment_provider_id AND ppcm.payment_channel_id = cppcm.payment_channel_id AND ppcm.capability = cppcm.capability AND ppcm.deleted_at IS NULL`).
		Joins(`INNER JOIN payment_providers pp ON pp.id = ppcm.payment_provider_id`).
		Joins(`INNER JOIN payment_channels pc ON pc.id = ppcm.payment_channel_id`).
		Joins(`INNER JOIN companies c ON cppcm.company_id = c.id`).
		Where("cppcm.deleted_at IS NULL").
		Where("cppcm.company_id = ?", req.CompanyID).
		Where("cppcm.payment_provider_id = ?", req.ProviderID)
	if req.Capability > 0 {
		q = q.Where("cppcm.capability = ?", req.Capability)
	}
	if req.ProviderChannelStatus != nil {
		q = q.Where("cppcm.status = ?", *req.ProviderChannelStatus)
	}
	if len(paginate.Value) > 0 {
		searchValue := "%" + strings.ToLower(paginate.Value) + "%"
		q = q.Where(`pc."name" ILIKE ? OR ppcm.code ILIKE ?`, searchValue, searchValue)
	}
	if paginate.IsValidParam() {
		err = q.Count(&totalData).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return
		}

		q = q.Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Order(paginate.GetOrderByMaps("cppcm", orderByMap))
	}

	err = q.Find(&resp).Error
	if err != nil {
		return
	}

	return
}

func (r *companyMappingRepo) GetCompanyProviderChannelMappingRequestBy(ctx context.Context, companyId, providerId, channelId, capability string) (resp *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = r.DB.WithContext(ctx).Where("company_id = ? AND payment_provider_id = ? AND payment_channel_id = ? AND capability = ?", companyId, providerId, channelId, capability).Take(&resp).Error
	return
}

func (r *companyMappingRepo) GetCompanyProviderMappingByCompanyIdAndProviderId(ctx context.Context, companyId, providerId string) (resp *domain.CompanyPaymentProviderMappings, err error) {
	err = r.DB.WithContext(ctx).Where("company_id = ? AND payment_provider_id = ?", companyId, providerId).Take(&resp).Error
	return
}

func (r *companyMappingRepo) GetListAllCompanyProviderMappingChannelByCompanyID(ctx context.Context, companyId string) (resp *[]entity.ListCompanyMappingChannel, err error) {
	err = r.DB.WithContext(ctx).Table("payment_channels c").Select("DISTINCT(c.id), c.\"name\", m.capability").
		Joins("JOIN company_payment_provider_channel_mappings m ON c.id = m.payment_channel_id").
		Where("m.company_id = ? AND m.deleted_at IS NULL AND c.status = TRUE AND m.status = TRUE AND c.payment_channel_type_id = 1", companyId).
		Find(&resp).Error
	return
}

// old code
// func (r *companyMappingRepo) GetListAllCompanyProviderMappingAPI(ctx context.Context, companyId string, req domain.GetAllCompanyProviderMappingReq) (resp *[]entity.ListCompanyMappingProviderChannel, err error) {
// 	// OLD QUERY, "DISTINCT" used for when there is more than one data that have same payment_channels but different payment_provider
// 	// err = r.DB.WithContext(ctx).Table("company_payment_provider_channel_mappings def").
// 	// 	Select("DISTINCT ppcm.code AS code, pp.name AS provider_name, pc.name AS channel_name, files.url AS channel_logo_url").
// 	// 	Joins("JOIN payment_provider_channel_mappings ppcm ON ppcm.payment_provider_id = def.payment_provider_id AND ppcm.payment_channel_id = def.payment_channel_id AND ppcm.capability = def.capability AND ppcm.deleted_at IS NULL").
// 	// 	Joins("JOIN payment_providers pp ON pp.id = ppcm.payment_provider_id AND pp.deleted_at IS NULL").
// 	// 	Joins("JOIN payment_channels pc ON pc.id = ppcm.payment_channel_id AND pc.deleted_at IS NULL").
// 	// 	Joins("LEFT JOIN files ON files.id = pc.logo_id").
// 	// 	Where(`def.company_id = ? AND def.capability = 1 AND def.deleted_at IS NULL AND def.status = true
// 	// 	AND pp.status = true AND pc.status = true`, companyId).Find(&resp).Error
// 	params := []interface{}{
// 		companyId,
// 	}
// 	paymentTypesQ := ""
// 	if len(req.PaymentTypes) > 0 {
// 		paymentTypesQ = "AND pct.payment_type IN ?"
// 		params = append(params, req.PaymentTypes)
// 	}
// 	searchQ := ""
// 	if len(req.Search) > 0 {
// 		s := "%" + strings.ToLower(req.Search) + "%"
// 		searchQ = "AND (LOWER(pp.name) LIKE ? OR LOWER(ppcm.code) LIKE ? OR LOWER(pc.name) LIKE ?) "
// 		params = append(params, s, s, s)
// 	}
// 	orderQ := "ORDER BY payment_type ASC, channel_sequence ASC"
// 	mapOrder := map[string]bool{
// 		"code":             true,
// 		"provider_name":    true,
// 		"channel_name":     true,
// 		"payment_type":     true,
// 		"channel_sequence": true,
// 	}
// 	if _, exist := mapOrder[req.Order]; exist {
// 		orderQ = fmt.Sprintf("ORDER BY %s %s", req.Order, req.Sort)
// 	}
// 	err = r.DB.WithContext(ctx).Raw(fmt.Sprintf(`
// 	WITH channels as (
// 		SELECT DISTINCT
// 			ppcm.code AS code,
// 			pp.name AS provider_name,
// 			pc.name AS channel_name,
// 			pc."sequence" AS channel_sequence,
// 			pct.payment_type  AS payment_type,
// 			files.url AS channel_logo_url
// 		FROM
// 			company_payment_provider_channel_mappings def
// 		JOIN payment_provider_channel_mappings ppcm ON
// 			ppcm.payment_provider_id = def.payment_provider_id
// 			AND ppcm.payment_channel_id = def.payment_channel_id
// 			AND ppcm.capability = def.capability
// 			AND ppcm.deleted_at IS NULL
// 		JOIN payment_providers pp ON
// 			pp.id = ppcm.payment_provider_id
// 			AND pp.deleted_at IS NULL
// 		JOIN payment_channels pc ON
// 			pc.id = ppcm.payment_channel_id
// 			AND pc.deleted_at IS NULL
// 		LEFT JOIN payment_channel_types  pct ON
// 			pct.id = pc.payment_channel_type_id
// 			AND pct.deleted_at IS NULL
// 		LEFT JOIN files ON
// 			files.id = pc.logo_id
// 		WHERE
// 			def.company_id = ?
// 			AND def.capability = 1
// 			AND def.deleted_at IS NULL
// 			AND def.status = TRUE
// 			AND pp.status = TRUE
// 			AND pc.status = TRUE
// 			%s
// 			%s
// 	)
// 	SELECT code, provider_name, channel_name, payment_type, channel_logo_url FROM channels
// 	%s;
// 	`, paymentTypesQ, searchQ, orderQ), params...).Find(&resp).Error
// 	return
// }

func (r *companyMappingRepo) GetListAllCompanyProviderMappingAPI(ctx context.Context, companyId string, req domain.GetAllCompanyProviderMappingReq) (resp *[]entity.ListCompanyMappingProviderChannel, err error) {
	var (
		mapOrder = map[string]struct{}{
			"code":             {},
			"provider_name":    {},
			"channel_name":     {},
			"payment_type":     {},
			"channel_sequence": {},
		}
	)

	q := r.DB.WithContext(ctx).Table("company_payment_provider_channel_mappings def").
		Joins(`JOIN payment_provider_channel_mappings ppcm ON
			ppcm.payment_provider_id = def.payment_provider_id
			AND ppcm.payment_channel_id = def.payment_channel_id
			AND ppcm.capability = def.capability
			AND ppcm.deleted_at IS NULL`).
		Joins(`JOIN payment_providers pp ON
			pp.id = ppcm.payment_provider_id
			AND pp.deleted_at IS NULL`).
		Joins(`JOIN payment_channels pc ON
			pc.id = ppcm.payment_channel_id
			AND pc.deleted_at IS NULL`).
		Joins(`LEFT JOIN payment_channel_types  pct ON
			pct.id = pc.payment_channel_type_id 
			AND pct.deleted_at IS NULL`).
		Joins(`LEFT JOIN files ON
			files.id = pc.logo_id`).
		Where(`def.company_id = ?
			AND def.capability = 1
			AND def.deleted_at IS NULL
			AND def.status = TRUE
			AND pp.status = TRUE
			AND pc.status = TRUE`, companyId)
	if len(req.ProductCode) > 0 {
		q = q.Joins(`JOIN company_product_provider_channel_mappings product_mapping 
			ON product_mapping.company_payment_provider_channel_mapping_id = def.id 
			AND product_mapping.deleted_at IS NULL AND product_mapping.status IS TRUE`).
			Joins(`JOIN company_products comp_product ON comp_product.id = product_mapping.company_product_id AND comp_product.deleted_at IS NULL`).
			Where(`comp_product.code = ?`, req.ProductCode)
	}
	if len(req.PaymentTypes) > 0 {
		q = q.Where("pct.payment_type IN ?", req.PaymentTypes)
	}
	if len(req.Search) > 0 {
		searchTerm := "%" + strings.ToLower(req.Search) + "%"
		q = q.Where("LOWER(pp.name) LIKE ? OR LOWER(ppcm.code) LIKE ? OR LOWER(pc.name) LIKE ?", searchTerm, searchTerm, searchTerm)
	}
	if _, exist := mapOrder[req.Order]; exist {
		q = q.Order(fmt.Sprintf("%s %s", req.Order, req.Sort))
	} else {
		q = q.Order("payment_type ASC, channel_sequence ASC")
	}

	err = q.Select(`ppcm.code AS code,
			pp.name AS provider_name,
			pc.name AS channel_name,
			pc."sequence" AS channel_sequence,
			pct.payment_type  AS payment_type,
			files.url AS channel_logo_url`).Find(&resp).Error
	return
}

func (r *companyMappingRepo) GetCompanyProviderMappingCodeAPI(ctx context.Context, companyId, code string) (resp *entity.ListCompanyMappingProviderChannel, err error) {
	err = r.DB.WithContext(ctx).Table("company_payment_provider_channel_mappings def").
		Select("ppcm.code AS code, pp.name AS provider_name, pc.name AS channel_name, files.url AS channel_logo_url").
		Joins("JOIN payment_provider_channel_mappings ppcm ON ppcm.payment_provider_id = def.payment_provider_id AND ppcm.payment_channel_id = def.payment_channel_id AND ppcm.capability = def.capability AND ppcm.deleted_at IS NULL").
		Joins("JOIN payment_providers pp ON pp.id = ppcm.payment_provider_id AND pp.deleted_at IS NULL").
		Joins("JOIN payment_channels pc ON pc.id = ppcm.payment_channel_id AND pc.deleted_at IS NULL").
		Joins("LEFT JOIN files ON files.id = pc.logo_id").
		Where(`def.company_id = ? AND ppcm.code = ? AND def.capability = 1 AND def.deleted_at IS NULL AND def.status = true
		AND pp.status = true AND pc.status = true`, companyId, code).Take(&resp).Error
	return
}

func (r *companyMappingRepo) GetListAllCompanyPaymentProvider(ctx context.Context, companyId, channelName string) (resp *[]entity.ListCompanyMappingChannelCashOut, err error) {
	err = r.DB.WithContext(ctx).Table("payment_providers p").Select("DISTINCT(p.id), p.\"name\" as provider_name, m.capability").
		Joins("JOIN company_payment_provider_channel_mappings m ON p.id = m.payment_provider_id JOIN payment_channels c ON m.payment_channel_id = c.id").
		Where("p.deleted_at IS NULL AND m.deleted_at IS NULL AND m.company_id = ? AND p.status = TRUE AND c.\"name\" = ? AND m.capability = 2 AND m.status = TRUE", companyId, channelName).
		Find(&resp).Error
	return
}

func (r *companyMappingRepo) GetListMappingCashInPaymentMethod(ctx context.Context, companyId string) (resp *[]entity.ListChannelMappingOpenAPI, err error) {
	err = r.DB.WithContext(ctx).Table("payment_provider_channel_mappings m").Select("m.code as payment_method_code, ch.\"name\" as channel_name, p.\"name\" as provider_name, ty.payment_type").
		Joins("LEFT JOIN company_payment_provider_channel_mappings c ON m.payment_channel_id = c.payment_channel_id AND m.payment_provider_id = c.payment_provider_id LEFT JOIN payment_providers p ON m.payment_provider_id = p.id LEFT JOIN payment_channels ch ON m.payment_channel_id = ch.id LEFT JOIN payment_channel_types ty ON ch.payment_channel_type_id = ty.id").
		Where("c.company_id = ? AND c.capability = 1 AND m.capability = 1 AND m.deleted_at IS NULL AND c.deleted_at IS NULL AND p.status = TRUE AND ch.status = TRUE AND c.status = TRUE", companyId).
		Find(&resp).Error
	return
}

func (r *companyMappingRepo) UpdateCompanyChannelMappingStatusByCompanyIdAndChannelId(ctx context.Context, companyId, channelId, capability string, reqStatus bool) (err error) {
	err = r.DB.WithContext(ctx).Model(&domain.CompanyPaymentProviderChannelMappings{}).Where("company_id = ? AND payment_channel_id = ? AND capability = ?", companyId, channelId, capability).Updates(map[string]interface{}{"status": reqStatus}).Error
	return
}
func (r *companyMappingRepo) UpdateCompanyChannelMappingStatusById(ctx context.Context, id int64, reqStatus bool) (err error) {
	err = r.DB.WithContext(ctx).Model(&domain.CompanyPaymentProviderChannelMappings{}).Where("id = ? ", id).Updates(map[string]interface{}{"status": reqStatus}).Error
	return
}

func (r *companyMappingRepo) GetCompanyProviderMapping(ctx context.Context, companyId, paymentProviderId, paymentChannelId, capability int) (resp *entity.CompanyMappingShow, err error) {
	err = r.DB.WithContext(ctx).Table("company_payment_provider_channel_mappings def").
		Where(`def.company_id = ? AND def.payment_provider_id = ? AND def.payment_channel_id = ?
		AND def.capability = ? AND def.deleted_at IS NULL AND def.status = true`, companyId,
			paymentProviderId, paymentChannelId, capability).Find(&resp).Error
	return
}
