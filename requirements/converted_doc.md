# [cite\_start]OttoPay API Virtual Account For Merchant Specification [cite: 3, 4, 5]

[cite\_start]**Version**: v.1.1.2 [cite: 6]
[cite\_start]**Revision Date**: 26 Maret 2024 [cite: 7]

-----

### [cite\_start]Copyright [cite: 8]

Copyright 2020 OttoPay. All rights reserved. No part of this publication may be reproduced or distributed without the prior consent of OttoPay, Suite 2103, JI. DR. Ide Anak Agung Gde Agung Kav. [cite\_start]E.1.2 No. 1&2 (d/h. Jl. Lingkar Mega Kuningan) Jakarta 12950. [cite: 9, 10]

### [cite\_start]Disclaimer [cite: 11]

[cite\_start]OttoPay provides this publication as is without warranty of any kind, either expressed or implied. [cite: 12] [cite\_start]This publication could include technical inaccuracies or typographical errors. [cite: 13] Changes are periodically made to the information herein. [cite\_start]These changes will be incorporated in new editions of the publication. [cite: 14] [cite\_start]OttoPay may make improvement and/or changes in the product(s) and/or programs(s) described in this publication at any time. [cite: 15]

### [cite\_start]Trademarks [cite: 16]

[cite\_start]OttoPay is a registered trademark of OttoPay. [cite: 17] [cite\_start]All other brand and product names are trademarks or registered trademarks of their respective companies. [cite: 17]

### [cite\_start]Contact Information [cite: 18]

[cite\_start]**Website**: [http://www.ottopay.id](http://www.ottopay.id) [cite: 19]

-----

## [cite\_start]Preface [cite: 20]

### [cite\_start]Abstract [cite: 21]

[cite\_start]This document describes the steps to use the OttoPay Virtual Account APIs. [cite: 22]

### [cite\_start]Audience [cite: 23]

[cite\_start]This document is intended for OttoPay API Platform system integrator. [cite: 24]

### [cite\_start]Change History [cite: 25]

| Versi | Tanggal | Diubah Oleh | Penjelasan Perubahan |
| :--- | :--- | :--- | :--- |
| 1.0.0 | 14 July 2020 | Antonius Cherdy | Initial Version |
| 1.0.1 | 10 June 2021 | Antonius Cherdy | Update Description |
| 1.0.2 | 10 August 2021 | Antonius Cherdy | Add Token Authorization |
| 1.0.3 | 01 September 2021 | Evi Agustina Br Dongoran | Update Flow diagram |
| 1.0.4 | 10 January 2022 | Antonius Cherdy | Subprefix moved to Customer Number |
| 1.1.0 | 26 Desember 2022 | Antonius Cherdy | Enhancement RC Code |
| 1.1.1 | 19 Oktober 2023 | Johanes | Information RC Code and Get Token |
| 1.1.2 | 26 Maret 2024 | Ahmad Abidin | Review Spec as Tech |
[cite\_start][cite: 26]

-----

## [cite\_start]Contents [cite: 27]

1.  [cite\_start]Process Flow [cite: 28]
2.  [cite\_start]OttoPay VA to Merchant [cite: 28]
    [cite\_start]2.1 Get Token [cite: 28]
    [cite\_start]2.2 Inquiry [cite: 28]
    [cite\_start]2.3 Payment [cite: 28]

-----

## [cite\_start]1. Process Flow [cite: 30]

```mermaid
sequenceDiagram
    participant Issuer VA
    participant OttoPay
    participant Merchant

    Issuer VA->>OttoPay: request inquiry
    OttoPay->>Merchant: get token
    Merchant-->>OttoPay: response token
    OttoPay->>Merchant: inquiry
    Merchant-->>OttoPay: response inquiry
    OttoPay-->>Issuer VA: response inquiry

    Issuer VA->>OttoPay: payment
    OttoPay->>Merchant: token
    Merchant->>OttoPay: token
    OttoPay->>Merchant: payment
    Merchant-->>OttoPay: response payment
    OttoPay-->>Issuer VA: response payment
```

-----

## [cite\_start]2. OttoPay VA to Merchant [cite: 46]

[cite\_start]This API is used for OttoPay to connect to Merchant Host VA. [cite: 47]

### [cite\_start]2.1 Get Token [cite: 48]

#### [cite\_start]Header [cite: 49]

| Name | Type | Description |
| :--- | :--- | :--- |
| x-api-key | String | API KEY from Merchant Host |
[cite\_start][cite: 50]

[cite\_start]The header information must always be included in any messages sent to Merchant Host. [cite: 52]

#### [cite\_start]Request [cite: 51]

| Name | Type | Description |
| :--- | :--- | :--- |
| Username | String | User Name from Merchant Host |
| Password | String | Password from Merchant Host |
[cite\_start][cite: 52]

**Example:**

```json
{
    [cite_start]"username": "ottopay", [cite: 54]
    [cite_start]"password": "ac710qf!" [cite: 55]
}
```

#### [cite\_start]Response [cite: 56]

| Name | Sub-Key | Type | Description |
| :--- | :--- | :--- | :--- |
| Meta | | | |
| | status | Boolean | optional |
| | code | Number | 200 are successes, apart from 200 are failures |
| | message | String | |
| data | | | |
| | id | String | Response ID from Merchant Host |
| | username | String | User Name from Merchant Host |
| | id\_token | String | This token will be used when OttoPay do Inquiry and Payment VA |
[cite\_start][cite: 57]

**Example:**

```json
{
    [cite_start]"meta": { [cite: 59]
        [cite_start]"status": true, [cite: 60]
        [cite_start]"code": 200, [cite: 61]
        [cite_start]"message": "success" [cite: 62]
    },
    [cite_start]"data": { [cite: 64]
        [cite_start]"id": "45c14915-f731-43ac-b25a-2182e13c98f7", [cite: 65]
        [cite_start]"username": "ottopay", [cite: 66]
        [cite_start]"id_token": "eyJhbGciOiJlUzI1NilsInR5cCl6lkpXVCJ9.eyJ1c2VyX2lkIjoiNDVjMTQ5MTUtZjczMS00M2FjLWIyNWEtMjE4MmUxM2M5OGY3liwiYXBwX25hbWUiOiJPVFRPUEFZIiwidXNlcl9uYW1lljoib3R0b3BheSIsImV4cCl6MTYzMDAzNzE5Mn0.hvziePJPrnXwDU2ed6kU_yNXWJIYvjKySaABkMCeFvs" [cite: 67, 68]
    }
}
```

### [cite\_start]2.2 Inquiry [cite: 70]

[cite\_start]OttoPay host call this endpoint for inquiry customer to Merchant Host. [cite: 71]

[cite\_start]`POST https://<<Merchant Host endpoint>>` [cite: 72]

#### [cite\_start]Header [cite: 73]

| Key | Data Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| Authorization | Bearer Token | Yes | Value from id\_token in Get Token Response |
[cite\_start][cite: 74]

#### [cite\_start]Request [cite: 75]

| Key | Data Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| CompanyCode | String(max 5) | Yes | VA CompanyCode. Must be numeric in string format. |
| CustomerNumber | String(max 11) | Yes | SubPrefix (optional) + CustomerNumber / BillID. Must be numeric in string format. |
| RequestID | String(max 255) | Yes | generated by Bank |
| Channel Type | String (max 50) | Yes | channel type from bank |
| TransactionDate | String(19) | No | YYYY/MM/DD HH:MM:SS or YYYY-MM-DD HH:MM:SS |
| Additional Data | String(max 255) | No | Fill with empty string |
[cite\_start][cite: 76]

**Example:**

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 77]
    [cite_start]"CustomerNumber": "***********", [cite: 78]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 79]
    [cite_start]"ChannelType": "BINA", [cite: 80]
    [cite_start]"Transaction Date": "15/03/2014 22:07:40", [cite: 81]
    [cite_start]"Additional Data": "" [cite: 82]
}
```

#### [cite\_start]Response [cite: 83]

| Key | Data Type | Mandatory | Value |
| :--- | :--- | :--- | :--- |
| CompanyCode | String(max 5) | Yes | VA CompanyCode. Must be numeric in string format. |
| CustomerNumber | String(max 11) | Yes | SubPrefix (optional) + CustomerNumber / BillID. Must be numeric in string format. |
| RequestID | String(max 255) | Yes | Can be generated by Bank or internally by the system (hybrid approach). If generated externally, it must follow the format specified by the bank. Internal generation uses prefixes like INQ, PAY, TOK with timestamps. |
| CustomerName | String(max 30) | Yes | |
| CurrencyCode | String(3) | Yes | IDR |
| TotalAmount | String(max 15) | Yes | |
| SubCompany | String(max 5) | Yes | Fill with "00000" |
| Detail Bills | List(N) | No | Fill with null |
| Additional Data | String (max 255) | No | |
[cite\_start][cite: 84]

**Success Example:**

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 86]
    [cite_start]"CustomerNumber": "***********", [cite: 87]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 88]
    [cite_start]"InquiryStatus": "00", [cite: 89]
    [cite_start]"InquiryReason": { "Indonesian": "Sukses", "English": "Success" }, [cite: 90]
    [cite_start]"CustomerName": "Customer Name Virtual Account ", [cite: 91]
    [cite_start]"CurrencyCode": "IDR", [cite: 92]
    [cite_start]"TotalAmount": "150000.00", [cite: 93]
    [cite_start]"SubCompany": "00000", [cite: 94]
    [cite_start]"DetailBills": null, [cite: 95]
    [cite_start]"Additional Data": "" [cite: 96]
}
```

[cite\_start]**Failure Example:** [cite: 98]

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 100]
    [cite_start]"CustomerNumber": "***********", [cite: 101]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 102]
    [cite_start]"InquiryStatus": "01", [cite: 103]
    [cite_start]"InquiryReason": { [cite: 104]
        [cite_start]"Indonesian": "Keterangan error disini", [cite: 105]
        [cite_start]"English": "The error reason goes here" [cite: 107]
    },
    [cite_start]"CustomerName": "Customer Name Virtual Account", [cite: 109]
    [cite_start]"CurrencyCode": "IDR", [cite: 110]
    [cite_start]"TotalAmount": "0.00", [cite: 111]
    [cite_start]"SubCompany": "00000", [cite: 112]
    [cite_start]"DetailBills": null, [cite: 113]
    [cite_start]"FreeTexts": null, [cite: 114]
    [cite_start]"Additional Data": "" [cite: 115]
}
```

### [cite\_start]2.3 Payment [cite: 116]

[cite\_start]OttoPay host call this endpoint for payment to Merchant Host. [cite: 117]

[cite\_start]`POST https://<<Merchant Host Endpoint>>` [cite: 118]

#### [cite\_start]Header [cite: 119]

| Key | Data Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| Token | Bearer Token | Yes | Value from id\_token in Get Token Response |
[cite\_start][cite: 120]

#### [cite\_start]Request [cite: 121]

| Key | Data Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| CompanyCode | String(max 5) | Yes | VA CompanyCode |
| CustomerNumber | String(max 11) | Yes | SubPrefix (optional) + CustomerNumber / BillID. Must be numeric in string format. |
| RequestID | String(max 255) | Yes | |
| Channel Type | String (max 50) | Yes | channel type from bank |
| CustomerName | String(max 30) | Yes | generated by Bank |
| CurrencyCode | String(3) | Yes | IDR |
| PaidAmount | String(max 15) | Yes | Payment Amount |
| TotalAmount | String(max 15) | Yes | Billed Amount |
| SubCompany | String(max 5) | No | 00000 |
| TransactionDate | String(19) | No | YYYY/MM/DD HH:MM:SS or YYYY-MM-DD HH:MM:SS |
| Reference | String(max 255) | Yes | |
| DetailBills | List(N) | No | |
| Additional Data | String (max 255) | No | |
[cite\_start][cite: 122, 123]

**Example:**

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 125]
    [cite_start]"CustomerNumber": " ***************", [cite: 126]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 127]
    [cite_start]"ChannelType": "BINA", [cite: 128]
    [cite_start]"CustomerName": "Customer Virtual Account", [cite: 129]
    [cite_start]"CurrencyCode": "IDR", [cite: 130]
    [cite_start]"PaidAmount": "150000.00", [cite: 131]
    [cite_start]"TotalAmount": "150000.00", [cite: 132]
    [cite_start]"SubCompany": "00000", [cite: 133]
    [cite_start]"TransactionDate": "15/03/2014 22:07:40", [cite: 134]
    [cite_start]"Reference": "**********", [cite: 135]
    [cite_start]"DetailBills": [], [cite: 136]
    [cite_start]"Additionaldata": "" [cite: 137]
}
```

#### [cite\_start]Response [cite: 139]

| Key | Data Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| CompanyCode | String(max 5) | Yes | VA CompanyCode |
| CustomerNumber | String(max 11) | Yes | SubPrefix (optional) + CustomerNumber / BillID. Must be numeric in string format. |
| RequestID | String(max 255) | Yes | generated by bank |
| PaymentFlagStatus | String (2) | Yes | 00 If Success, 01 if Failed, 02 If Timeout |
| PaymentFlagReason | Object | Yes | See sample below |
| Indonesian | String(max 64) | Yes | Sample: "Sukses" |
| English | String(max 64) | Yes | Sample: "Success" |
| CustomerName | String(max 30) | Yes | |
| CurrencyCode | String(3) | Yes | IDR |
| PaidAmount | String(max 15) | Yes | Payment Amount |
| TotalAmount | String(max 15) | Yes | Billed Amount |
| TransactionDate | String(19) | No | YYYY/MM/DD HH:MM:SS or YYYY-MM-DD HH:MM:SS |
| DetailBills | List(N) | No | Fill with null |
| Freetext | String | No | Fill with null |
| Additional Data | String (max 255) | No | Fill empty string |
[cite\_start][cite: 140, 141]

**Success Example:**

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 143]
    [cite_start]"CustomerNumber": " ***************", [cite: 144]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 145]
    [cite_start]"PaymentFlagStatus": "00", [cite: 146]
    [cite_start]"PaymentFlagReason": { [cite: 147]
        [cite_start]"Indonesian": "Sukses", [cite: 149]
        [cite_start]"English": "Success" [cite: 150]
    },
    [cite_start]"CustomerName": "Customer Virtual Account", [cite: 151]
    [cite_start]"CurrencyCode": "IDR", [cite: 152]
    [cite_start]"PaidAmount": "150000.00", [cite: 153]
    [cite_start]"TotalAmount": "150000.00", [cite: 154]
    [cite_start]"Transaction Date": "15/03/2014 22:07:40", [cite: 155]
    [cite_start]"DetailBills": null, [cite: 156]
    [cite_start]"FreeTexts": null, [cite: 157]
    [cite_start]"Additional Data": "" [cite: 158]
}
```

[cite\_start]**Failure Example:** [cite: 160]

```json
{
    [cite_start]"CompanyCode": "12173", [cite: 162]
    [cite_start]"CustomerNumber": "************", [cite: 163]
    [cite_start]"RequestID": "201507131507262221400000001975", [cite: 164]
    [cite_start]"PaymentFlagStatus": "01", [cite: 165]
    [cite_start]"PaymentFlagReason": { [cite: 166]
        [cite_start]"Indonesian": "Jumlah pembayaran salah", [cite: 167]
        [cite_start]"English": "Invalid Amount" [cite: 168]
    },
    [cite_start]"CustomerName": "Customer Virtual Account", [cite: 170]
    [cite_start]"CurrencyCode": "IDR", [cite: 171]
    [cite_start]"PaidAmount": "150000.00", [cite: 172]
    [cite_start]"TotalAmount": "150000.00", [cite: 173]
    [cite_start]"Transaction Date": "15/03/2014 22:07:40", [cite: 174]
    [cite_start]"DetailBills": null, [cite: 176]
    [cite_start]"FreeTexts": null, [cite: 177]
    [cite_start]"Additional Data": "" [cite: 178]
}
```

[cite\_start]**Notes:** [cite: 179]

  * [cite\_start]The last 2 digits for TotalAmount and PaidAmount fields should be a decimal point. [cite: 180]
  * [cite\_start]The RequestID is unique from the Bank for each transaction. [cite: 183]
  * [cite\_start]The PaidAmount field value will be the total amount paid by the customer through the Bank. [cite: 184]
  * [cite\_start]The CurrencyCode must be the same for TotalAmount and PaidAmount. [cite: 184]
  * [cite\_start]CurrencyCode may vary with these values: IDR, USD. [cite: 185, 186, 187]
  * PaymentFlagStatus field indicates the status for payment flag:
      * [cite\_start]`00`: Success payment flag. [cite: 189]
      * `01`: Reject payment flag by co-partner. [cite\_start]The co-partner must define the reason in the PaymentFlagReason field in both Indonesian and English. [cite: 190] [cite\_start]Only for this response will the customer's payment be reversed by the bank. [cite: 191]
      * [cite\_start]`02`: Payment Timeout. [cite: 191]
  * [cite\_start]Payment flag status other than 00, 01, and 02 will be considered as 01. [cite: 192]

-----

### [cite\_start]Appendix A: Response Code [cite: 193]

| NO | Service Code | Service Name | RC Code | RC Description |
| :--- | :--- | :--- | :--- | :--- |
| 1 | 24 | Inquiry VA | 2002400 | Success Inquiry |
| 2 | 24 | Inquiry VA | 4042412 | Invalid Bill |
| 3 | 24 | Inquiry VA | 4012401 | Invalid Token |
| 4 | 24 | Inquiry VA | 4092401 | Duplicate Partner Reference Number |
| 5 | 24 | Inquiry VA | 5002400 | Bad request |
| 6 | 24 | Inquiry VA | 4002400 | Internal Server Error |
| 7 | 25 | Payment VA | 2002500 | Success Payment |
| 8 | 25 | Payment VA | 4042512 | Invalid Bill |
| 9 | 25 | Payment VA | 4032500 | Transaction Expired |
| 10 | 25 | Payment VA | 4012501 | Invalid Token |
| 11 | 25 | Payment VA | 5002500 | Bad request |
| 12 | 25 | Payment VA | 4002500 | Internal Server Error |
| 13 | 25 | Payment VA | 4092501 | Duplicate Partner Reference Number (payment 2x) |
| 14 | 25 | Payment VA | 4042513 | Invalid Amount |
| 15 | 25 | Payment VA | 4042514 | Paid Bill |
[cite\_start][cite: 194]