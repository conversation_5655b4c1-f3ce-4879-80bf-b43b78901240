package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultPaymentProvider) CreatePaymentProvider(ctx context.Context, req *entity.PaymentProvidersShow) (err error) {
	reqProvider := req.Conver()
	err = s.paymentProviderRepo.CreatePaymentProvider(ctx, &reqProvider)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	if err = s.setCachePaymentProvider(ctx, &reqProvider); err != nil {
		logger.Error(ctx, err.Error())
	}

	req.ID = reqProvider.ID
	return
}

func (s *defaultPaymentProvider) UpdatePaymentProvider(ctx context.Context, req *entity.PaymentProvidersShow, id string) (err error) {
	payment, err := s.paymentProviderRepo.GetPaymentProviderById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	req.ID = payment.ID
	reqProvider := req.Conver()
	reqProvider.CreatedAt = payment.CreatedAt
	err = s.paymentProviderRepo.UpdatePaymentProvider(ctx, &reqProvider)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	if err = s.deleteCachePaymentProvider(ctx, id); err != nil {
		logger.Error(ctx, err.Error())
	}

	if err = s.setCachePaymentProvider(ctx, &reqProvider); err != nil {
		logger.Error(ctx, err.Error())
	}

	return
}

func (s *defaultPaymentProvider) GetPaymentProviderById(ctx context.Context, id string) (resp *domain.PaymentProviders, err error) {
	resp, err = s.getCachePaymentProvider(ctx, id)
	return
}

func (s *defaultPaymentProvider) GetPaymentProviderByIdShow(ctx context.Context, id string) (resp entity.PaymentProvidersShow, err error) {
	cacheProvider, err := s.getCachePaymentProvider(ctx, id)
	if err != nil {
		return
	}
	resp = entity.ConverPaymentProviderToProviderShow(cacheProvider)
	return
}

func (s *defaultPaymentProvider) DeletePaymentProvider(ctx context.Context, id string) (err error) {
	payment, err := s.paymentProviderRepo.GetPaymentProviderById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var req domain.PaymentProviders
	req.ID = payment.ID
	err = s.paymentProviderRepo.DeletePaymentProvider(ctx, &req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if err = s.deleteCachePaymentProvider(ctx, id); err != nil {
		logger.Error(ctx, err.Error())
	}

	return
}

func (s *defaultPaymentProvider) SearchPaymentProvider(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProvidersShow, totalData int64, err error) {
	switch true {
	case paginate.Key != "" && paginate.Value != "":
		payment, total, errRes := s.paymentProviderRepo.SearchPaymentProviderFilterCabability(ctx, paginate)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total

	case paginate.Key == "" && paginate.Value != "":
		payment, total, errRes := s.paymentProviderRepo.SearchPaymentProvider(ctx, paginate)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		totalData = total
		if totalData == 0 {
			payment, total, errRes = s.paymentProviderRepo.SearchPaymentProviderWithChannelMapping(ctx, paginate)
			if errRes != nil {
				err = errors.SetError(http.StatusInternalServerError, errRes.Error())
				logger.Error(ctx, err.Error())
				return
			}
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total

	case paginate.Key != "" && paginate.Value == "":
		payment, total, errRes := s.paymentProviderRepo.GetPaymentProviderListFilterCapability(ctx, paginate)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total

	default:
		payment, total, errRes := s.paymentProviderRepo.GetPaymentProviderList(ctx, paginate)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total
	}
	return
}

func (s *defaultPaymentProvider) GetPaymentProviderListAll(ctx context.Context, status string) (resp *[]entity.PaymentProvidersShow, totalData int64, err error) {
	switch status {
	case "true":
		payment, total, errRes := s.paymentProviderRepo.GetPaymentProviderListAllActive(ctx)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total

	case "false":
		payment, total, errRes := s.paymentProviderRepo.GetPaymentProviderListAllNotActive(ctx)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total

	default:
		payment, total, errRes := s.paymentProviderRepo.GetPaymentProviderListAll(ctx)
		if errRes != nil {
			err = errors.SetError(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = entity.ConverPaymentProviderListToShow(payment)
		totalData = total
	}

	return
}

func (s *defaultPaymentProvider) getCachePaymentProvider(ctx context.Context, providerId string) (resp *domain.PaymentProviders, err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePaymentProvider, providerId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		// only return error if not nil or not redis.Nil
		if errRes.Error() != "redis: nil" || errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	switch true {
	case cache == "":
		prod, errRes := s.paymentProviderRepo.GetPaymentProviderById(ctx, providerId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "payment provider not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCachePaymentProvider(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.PaymentProviders
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultPaymentProvider) setCachePaymentProvider(ctx context.Context, req *domain.PaymentProviders) error {
	var err error

	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	key := fmt.Sprintf("%v-%v", constants.CachePaymentProvider, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	return nil
}

func (s *defaultPaymentProvider) deleteCachePaymentProvider(ctx context.Context, providerId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePaymentProvider, providerId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultPaymentProvider) DefaultPaymentProvider(ctx context.Context) *[]entity.DefaultPaymentProvider {
	res, _ := s.paymentProviderRepo.GetAvailableProviders(ctx)
	data := []entity.DefaultPaymentProvider{}
	for _, v := range res {
		data = append(data, entity.DefaultPaymentProvider{
			Name: v,
		})
	}

	return &data
}

func (s *defaultPaymentProvider) UpdateStatus(ctx context.Context, id string, status bool) error {
	provider, err := s.paymentProviderRepo.GetPaymentProviderById(ctx, id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusNotFound, "payment provider not found")
	}
	provider.Status = status

	if err = s.deleteCachePaymentProvider(ctx, id); err != nil {
		logger.Error(ctx, err.Error())
	}

	if err = s.setCachePaymentProvider(ctx, provider); err != nil {
		logger.Error(ctx, err.Error())
	}

	return s.paymentProviderRepo.UpdatePaymentProvider(ctx, provider)
}
