package usecase

import (
	company "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	companyMapping "repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	channel "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"

	"github.com/go-redis/redis/v8"
)

type partnerUseCase struct {
	partnerRepository PartnerRepository
	cache             *redis.Client
	channelRepo       channel.PaymentChannelRepository
	companyService    company.CompanyManagementUsecase
	companyMappingRepo companyMapping.CompanyMappingRepository
}

func Setup() *partnerUseCase {
	return &partnerUseCase{}
}

func (s *partnerUseCase) SetPartnerRepo(t PartnerRepository) *partnerUseCase {
	s.partnerRepository = t
	return s
}

func (s *partnerUseCase) SetRedisClient(t *redis.Client) *partnerUseCase {
	s.cache = t
	return s
}

func (s *partnerUseCase) SetChannelRepo(t channel.PaymentChannelRepository) *partnerUseCase {
	s.channelRepo = t
	return s
}

func (s *partnerUseCase) SetCompanyService(t company.CompanyManagementUsecase) *partnerUseCase {
	s.companyService = t
	return s
}

func (s *partnerUseCase) SetCompanyMappingRepo(t companyMapping.CompanyMappingRepository) *partnerUseCase {
	s.companyMappingRepo = t
	return s
}

func (s *partnerUseCase) Validate() PartnerUseCase {
	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.partnerRepository == nil {
		panic("partner repo is nil")
	}

	if s.channelRepo == nil {
		panic("channel repo is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	if s.companyMappingRepo == nil {
		panic("company mapping repo is nil")
	}

	return s
}
