package entity

type CompanyMappingShow struct {
	ID                int64   `json:"id" gorm:"column:id;<-false"`
	PaymentProviderID int64   `json:"payment_provider_id" gorm:"column:payment_provider_id;<-false"`
	PaymentChannelID  int64   `json:"payment_channel_id" gorm:"column:payment_channel_id;<-false"`
	CompanyID         int64   `json:"company_id" gorm:"column:company_id;<-false"`
	ProviderName      string  `json:"provider_name" gorm:"column:provider_name;<-false"`
	ChannelName       string  `json:"channel_name" gorm:"column:channel_name;<-false"`
	CompanyName       string  `json:"company_name" gorm:"column:company_name;<-false"`
	Code              string  `json:"code" gorm:"column:code;<-false"`
	MaxTransaction    float64 `json:"max_transaction" gorm:"column:max_transaction;<-false"`
	FeeFixValue       float64 `json:"fee_fix_value" gorm:"column:fee_fix_value;<-false"`
	FeePercentage     float64 `json:"fee_percentage" gorm:"column:fee_percentage;<-false"`
	Sla               int     `json:"sla" gorm:"column:sla;<-false"`
	CashIn            bool    `json:"cash_in" gorm:"column:cash_in;<-false"`
	CashOut           bool    `json:"cash_out" gorm:"column:cash_out;<-false"`
	ExpiredTime       int64   `json:"expired_time" gorm:"column:expired_time;<-false"`
	Status            bool    `json:"status" gorm:"column:status;<-false"`
}

type CompanyProviderMappingList struct {
	ID                int64  `json:"id"`
	CompanyID         int64  `json:"company_id"`
	PaymentProviderId int64  `json:"payment_provider_id"`
	ProviderName      string `json:"provider_name"`
	CompanyName       string `json:"company_name"`
	ProviderSecrets   string `json:"provider_secrets"`
	Status            bool   `json:"status"`
}

type CompanyMappingProvider struct {
	ID                int
	PaymentProviderId int
	ProviderName      string
	Status            bool
}

type ListCompanyMappingProviderChannel struct {
	Code           string `json:"code"`
	ProviderName   string `json:"provider_name"`
	ChannelName    string `json:"channel_name"`
	PaymentType    string `json:"payment_type"`
	ChannelLogoURL string `json:"channel_logo_url"`
}

type ListCompanyMappingChannel struct {
	ID         int    `json:"id"`
	Name       string `json:"name"`
	Capability int    `json:"capability"`
}

type ListCompanyMappingChannelAPI struct {
	ID   int    `json:"channel_id"`
	Name string `json:"name"`
}
type ListCompanyMappingChannelCashOut struct {
	ID           int    `json:"id"`
	ProviderName string `json:"provider_name"`
	Capability   int    `json:"capability"`
}

type ListChannelMappingOpenAPI struct {
	ChannelName       string `json:"channel_name"`
	ProviderName      string `json:"provider_name"`
	PaymentType       string `json:"payment_type"`
	PaymentMethodCode string `json:"payment_method_code"`
}
