package domain

import (
	"time"

	"gorm.io/gorm"
)

type Files struct {
	ID         int            `gorm:"primaryKey" json:"id"`
	Type       string         `json:"type" validate:"required"`
	URL        string         `json:"url" validate:"required"`
	UserUpload string         `json:"user_upload"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
