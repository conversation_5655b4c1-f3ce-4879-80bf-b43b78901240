package emailclient

import (
	"context"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email"
)

func (w *wrapper) SendMsg(ctx context.Context, req SendMsgReq) error {
	attachments := make([]email.MailgunAttachment, 0)

	for _, attch := range req.Attachs {
		attachments = append(attachments, email.MailgunAttachment{
			Filename: attch.Filename,
			Buffer:   attch.Buffer,
		})
	}

	resp, err := w.client.Send(ctx, req.Sender, req.Subject, req.Body, []string{req.Recipient}, email.WithAttachments(attachments), email.WithParameters(req.Data), email.WithCC(req.CCs))
	if err != nil {
		logger.Error(ctx, "send error", logger.Err(err))
		return err
	}

	logger.Info(ctx, "send success", logger.String("resp", resp.Message), logger.String("id", resp.ToID()))

	return nil
}
