package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCashOutUsecase) getCredentialCashOutXfersCallback(ctx context.Context, batchNumber string) (resp xfers.Credential, cashout *domain.CashOutTransactions, err error) {
	cashout, secret, err := s.getProviderSecret(ctx, batchNumber)
	if err != nil {
		return
	}
	credential, errRes := xfers.GetXfersCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashOutUsecase) getCacheCashOutTransaction(ctx context.Context, batchNumber string) (resp *domain.CashOutTransactions, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCashOutTransaction, batchNumber)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
	}

	switch true {
	case cache == "":
		prod, errRes := s.cashOutRepository.GetCashOutTransactionByBatchNumber(ctx, batchNumber)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "cash out transaction not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCashOutTransaction(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.CashOutTransactions
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCashOutUsecase) setCacheCashOutTransaction(ctx context.Context, req *domain.CashOutTransactions) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCashOutTransaction, req.BatchNumber)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCashOutUsecase) deleteCacheCashOutTransaction(ctx context.Context, batchNumber string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCashOutTransaction, batchNumber)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (c *defaultCashOutUsecase) getSecretGoogleAuthorization(ctx context.Context, userId int) (secret string, user *domain.Users, err error) {
	userResp, err := c.userService.GetUserById(ctx, strconv.Itoa(userId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "unauthorizer")
		logger.Error(ctx, err.Error())
		return
	}

	secret = userResp.Password
	if len(userResp.Password) > 17 {
		secret = userResp.Password[:17]
	}

	user = userResp
	return
}

func (s *defaultCashOutUsecase) validateOTPCode(ctx context.Context, otpCode string, userId int) (resp *domain.Users, err error) {
	secret, userRes, err := s.getSecretGoogleAuthorization(ctx, userId)
	if err != nil {
		return
	}
	isValid, err := utils.SetupGoogleAutorization(secret).AuthentificationGoogleAuth(otpCode)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	if !isValid {
		err = errors.SetErrorMessage(http.StatusBadRequest, "OTP not valid")
		logger.Error(ctx, err.Error())
		return
	}

	resp = userRes
	return
}

func (s *defaultCashOutUsecase) getProviderSecret(ctx context.Context, batchNumber string) (resp *domain.CashOutTransactions, secret string, err error) {
	cashOut, err := s.getCacheCashOutTransaction(ctx, batchNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, "[cashOut] callback BatchNumber not found", logger.Err(err))
		return
	}

	companyId := strconv.Itoa(cashOut.CompanyID)
	providerId := fmt.Sprintf("%v", cashOut.PaymentProviderID)
	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, "[cashOut] payment provider not found in company", logger.Err(err))
		return
	}

	resp = cashOut
	secret = providerMapping.ProviderSecrets
	return
}

func (s *defaultCashOutUsecase) getCredentialCashOutXenditCallback(ctx context.Context, invoiceNumber string) (resp xendit.Credential, cashout *domain.CashOutTransactions, err error) {
	cashout, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	credential, errRes := xendit.GetXenditCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

// TODO: func not used, comment or remove to fix error from golangci
// func (cu *defaultCashOutUsecase) composeViewCashoutResponse(ctx context.Context, req interface{}) (resp interface{}, err error) {
// 	var data map[string]interface{}
// 	resJson, _ := json.Marshal(req)
// 	err = json.Unmarshal(resJson, &data)
// 	if err != nil {
// 		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
// 		logger.Error(ctx, err.Error())
// 		return
// 	}

// 	requestedBy := fmt.Sprintf("%v", data["requested_by"])
// 	approvedBy := fmt.Sprintf("%v", data["approved_by"])
// 	switch true {
// 	case requestedBy != "":
// 		if requestedBy != "0" {
// 			userRes, errRes := cu.userService.GetUserById(ctx, requestedBy)
// 			if errRes != nil {
// 				err = errors.SetErrorMessage(http.StatusNotFound, errRes.Error())
// 				logger.Error(ctx, err.Error())
// 				return
// 			}
// 			data["requested_by"] = userRes.Name
// 		} else {
// 			data["requested_by"] = ""
// 		}

// 	default:
// 		data["requested_by"] = ""
// 	}

// 	switch true {
// 	case approvedBy != "":
// 		if approvedBy != "0" {
// 			userRes, errRes := cu.userService.GetUserById(ctx, requestedBy)
// 			if errRes != nil {
// 				err = errors.SetErrorMessage(http.StatusNotFound, errRes.Error())
// 				logger.Error(ctx, err.Error())
// 				return
// 			}

// 			data["approved_by"] = userRes.Name
// 		} else {
// 			data["approved_by"] = ""
// 		}

// 	default:
// 		data["approved_by"] = ""
// 	}

// 	resp = data
// 	return
// }
