package snapclient

import (
	"context"
	"net/http"

	"github.com/go-resty/resty/v2"
	"repo.nusatek.id/moaja/backend/libraries/logger"
)

type TransferVaCreateVaReq struct {
	PartnerServiceID    string `json:"partnerServiceId"`
	CustomerNo          string `json:"customerNo"`
	VirtualAccountNo    string `json:"virtualAccountNo"`
	VirtualAccountName  string `json:"virtualAccountName"`
	VirtualAccountEmail string `json:"virtualAccountEmail"`
	VirtualAccountPhone string `json:"virtualAccountPhone"`
	TrxID               string `json:"trxId"`
	TotalAmount         Money  `json:"totalAmount"`
	AdditionalInfo
	Raw *resty.Request `json:"-"`
}

type TransferVaCreateVaResp struct {
	BaseResp
	VirtualAccountData struct {
		PartnerServiceID   string `json:"partnerServiceId"`
		CustomerNo         string `json:"customerNo"`
		VirtualAccountNo   string `json:"virtualAccountNo"`
		VirtualAccountName string `json:"virtualAccountName"`
		TrxID              string `json:"trxId"`
		TotalAmount        Money  `json:"totalAmount"`
		AdditionalInfo
	} `json:"virtualAccountData"`
}

func (w *Wrapper) TransferVaCreateVA(ctx context.Context, cred iCredential, hreq HTrxReq, req *TransferVaCreateVaReq) (resp TransferVaCreateVaResp, err error) {
	accessToken, err := w.GetB2BToken(ctx, cred)
	if err != nil {
		logger.Error(ctx, "GetB2BToken error", logger.Err(err))
		return
	}

	doReq := DoReq{
		Method:  http.MethodPost,
		Path:    w.RPaths.TransferVaCreateVa,
		ReqBody: req,
		Header: map[string]string{
			HeaderChannelId: hreq.ChannelId,
			HeaderPartnerId: hreq.XPartnerId,
		},
	}
	doReq.Header, err = w.transactionHeaders(ctx, accessToken, cred.GetCred().ClientSecret, doReq)
	if err != nil {
		logger.Error(ctx, "transactionHeaders error", logger.Err(err))
		return
	}

	req.Raw, _, err = w.do(ctx, doReq, &resp)
	if err != nil {
		return
	}

	return
}

type TransferVaStatusReq struct {
	PartnerServiceID string `json:"partnerServiceId"`
	CustomerNo       string `json:"customerNo"`
	VirtualAccountNo string `json:"virtualAccountNo"`
	InquiryRequestID string `json:"inquiryRequestId"`
	// PaymentRequestID string `json:"paymentRequestId"`
	AdditionalInfo
}

type TransferVaStatusResp struct {
	BaseResp
	VirtualAccountData struct {
		PartnerServiceID string `json:"partnerServiceId"`
		CustomerNo       string `json:"customerNo"`
		VirtualAccountNo string `json:"virtualAccountNo"`
		InquiryRequestID string `json:"inquiryRequestId"`
		TotalAmount      Money  `json:"totalAmount"`
		AdditionalInfo
	} `json:"virtualAccountData"`
}

func (w *Wrapper) TransferVaStatus(ctx context.Context, cred iCredential, hreq HTrxReq, req *TransferVaStatusReq) (resp TransferVaStatusResp, err error) {
	accessToken, err := w.GetB2BToken(ctx, cred)
	if err != nil {
		logger.Error(ctx, "GetB2BToken error", logger.Err(err))
		return
	}

	doReq := DoReq{
		Method:  http.MethodPost,
		Path:    w.RPaths.TransferVaStatus,
		ReqBody: req,
		Header: map[string]string{
			HeaderChannelId: hreq.ChannelId,
			HeaderPartnerId: hreq.XPartnerId,
		},
	}
	doReq.Header, err = w.transactionHeaders(ctx, accessToken, cred.GetCred().ClientSecret, doReq)
	if err != nil {
		logger.Error(ctx, "transactionHeaders error", logger.Err(err))
		return
	}

	_, _, err = w.do(ctx, doReq, &resp)
	if err != nil {
		return
	}

	return
}
