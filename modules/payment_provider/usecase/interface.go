package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type PaymentProviderRepository interface {
	CreatePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error)
	UpdatePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error)
	GetPaymentProviderById(ctx context.Context, id string) (resp *domain.PaymentProviders, err error)
	DeletePaymentProvider(ctx context.Context, req *domain.PaymentProviders) (err error)
	SearchPaymentProvider(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetPaymentProviderList(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetPaymentProviderListFilterCapability(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	SearchPaymentProviderFilterCabability(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetPaymentProviderListAll(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	SearchPaymentProviderWithChannelMapping(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetPaymentProviderListAllActive(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetPaymentProviderListAllNotActive(ctx context.Context) (resp *[]entity.PaymentProviderList, totalData int64, err error)
	GetAvailableProviders(ctx context.Context) (res []string, err error)
}

type PaymentProviderUsecase interface {
	CreatePaymentProvider(ctx context.Context, req *entity.PaymentProvidersShow) (err error)
	UpdatePaymentProvider(ctx context.Context, req *entity.PaymentProvidersShow, id string) (err error)
	GetPaymentProviderById(ctx context.Context, id string) (resp *domain.PaymentProviders, err error)
	DeletePaymentProvider(ctx context.Context, id string) (err error)
	SearchPaymentProvider(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentProvidersShow, totalData int64, err error)
	GetPaymentProviderListAll(ctx context.Context, status string) (resp *[]entity.PaymentProvidersShow, totalData int64, err error)
	GetPaymentProviderByIdShow(ctx context.Context, id string) (resp entity.PaymentProvidersShow, err error)
	DefaultPaymentProvider(ctx context.Context) *[]entity.DefaultPaymentProvider
	UpdateStatus(ctx context.Context, id string, status bool) error
}
