package usecase

import (
	"context"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/logutil"
)

type usecase struct {
	repo TrxRequestLogRepository
}

func Setup() *usecase {
	return &usecase{}
}

func (u *usecase) SetRepo(repo TrxRequestLogRepository) *usecase {
	u.repo = repo
	return u
}

func (u *usecase) Validate() TrxRequestLogUseCase {
	if u.repo == nil {
		panic("trx request log repository is nil")
	}
	return u
}

func (u *usecase) Insert(ctx context.Context, data *domain.TrxRequestLog) (err error) {
	err = u.repo.Insert(ctx, data)
	if err != nil {
		return err
	}

	return
}

func (u *usecase) UpdateResponse(ctx context.Context, data *domain.TrxRequestLog) (err error) {
	err = u.repo.UpdateResponse(ctx, data)
	if err != nil {
		return err
	}

	return
}

func (u *usecase) UpdateResourceCtx(ctx context.Context, rId, rType string) (err error) {
	id := logutil.GetTrxLogId(ctx)
	if len(id) == 0 {
		logger.Warn(ctx, "trx log id not found")
		return
	}
	err = u.repo.UpdateResource(ctx, &domain.TrxRequestLog{
		Id:           id,
		ResourceType: rType,
		ResourceId:   rId,
	})
	if err != nil {
		return err
	}

	return
}
