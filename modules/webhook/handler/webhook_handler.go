package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/modules/webhook/entity"
	"repo.nusatek.id/nusatek/payment/modules/webhook/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type WebhookHandler struct {
	WebhookUseCase usecase.WebhookService
}

func NewHandler(webhookUsecase usecase.WebhookService) *WebhookHandler {
	return &WebhookHandler{webhookUsecase}
}

func (h *WebhookHandler) GetWebhookDeliveryLogs(c *fiber.Ctx) error {
	req := new(entity.WebhookLogPaginate)

	if err := c.QueryParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	logs, total, err := h.WebhookUseCase.GetWebhookDeliveryLogs(c.Context(), req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccessWithPagination(c, float64(total), *req.Pagination, logs)
}

func (h *WebhookHandler) GetWebhookDeliveryLogHistory(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	req := new(entity.WebhookDeliveryLogHistoryPaginate)

	if err := c.QueryParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if id <= 0 {
		return errors.SetErrorMessage(http.StatusBadRequest, "id must be greater than 0")
	}

	logs, total, err := h.WebhookUseCase.GetWebhookDeliveryLogHistory(c.Context(), int64(id), req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccessWithPagination(c, float64(total), *req.Pagination, logs)
}

func (h *WebhookHandler) GetWebhookDeliveryLogDetail(c *fiber.Ctx) error {
	id, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if id <= 0 {
		return errors.SetErrorMessage(http.StatusBadRequest, "id must be greater than 0")
	}

	log, err := h.WebhookUseCase.GetWebhookDeliveryLogById(c.Context(), int64(id))
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}

	return response.HandleSuccess(c, log)
}

// Verify Webhook
func (h *WebhookHandler) VerifyAndSave(c *fiber.Ctx) error {
	companyId, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	req := new(entity.WebhookRequest)
	if err := c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err := utils.ValidateStruct(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if req.EventType != entity.CashInStatusWebhook && req.EventType != entity.CashOutStatusWebhook {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid webhook type")
	}

	req.CompanyId = int64(companyId)

	result, err := h.WebhookUseCase.VerifyEndpoint(c.UserContext(), req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, result)
}
