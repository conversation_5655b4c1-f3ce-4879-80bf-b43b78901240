package nicepay

import (
	"context"
)

type RestNicePay interface {
	Registration(ctx context.Context, req *RegistrationRequest, credential Credential) (resp RegistrationResponse, err error)
	Inquiry(ctx context.Context, req *InquiryRequest, credential Credential) (resp InquiryResponse, err error)
	Cancel(ctx context.Context, req *CancelRequest, credential Credential) (resp CancelResponse, err error)
}
