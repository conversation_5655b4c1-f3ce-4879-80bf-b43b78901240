package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	messagebrokerConfig "repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	messagebrokerMessage "repo.nusatek.id/moaja/backend/libraries/message-broker/message"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	infrastructure "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
)

func (s *defaultCashinTransaction) updateStatusXfersCashinTransaction(ctx context.Context, req xfers.PaymentResponse, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string

	switch req.Data.PaymentDataResp.Status {
	case constants.XfersPending:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.StatusPending

	case constants.XfersProcessing:
		cashIn.PaymentStatus = constants.PaymentProcessing
		cashIn.Status = constants.StatusOnProgress

	case constants.XfersPaid:
		if cashIn.Status != constants.CashInStatusDone {
			cashIn.PaymentStatus = constants.PaymentPaid
			cashIn.PaymentAt = int(time.Now().Unix())
			cashIn.Status = constants.CashInStatusPaid
			cashinItemsStatus = constants.PaymentProcessing
		}

	case constants.XfersCompleted:
		csIn, errRes := s.getCacheCashInTransaction(ctx, cashIn.InvoiceNumber)
		if errRes != nil {
			return errRes
		}

		if csIn.PaymentAt == 0 {
			cashIn.PaymentAt = int(time.Now().Unix())
		}

		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid

	case constants.XfersExpired:
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.PaymentExpired

	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	return nil
}

func (s *defaultCashinTransaction) updateStatusXenditVACashinTransaction(ctx context.Context, req xendit.VaResponse, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string

	// Parse expiration date for define transaction success or expired
	expirationDate, err := time.Parse(time.RFC3339, req.ExpirationDate)
	if err != nil {
		return err
	}

	switch true {
	case req.Status == constants.XenditVAStatusActive:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.CashInStatusPending

	case req.Status == constants.XenditVAStatusPending:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.CashInStatusPending

	case req.Status == constants.XenditVAStatusInactive && time.Now().Before(expirationDate):
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())

	case req.Status == constants.XenditVAStatusInactive:
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusExpired

	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err = s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Xendit")
	return nil
}

func (s *defaultCashinTransaction) updateStatusXenditEwalletCashinTransaction(ctx context.Context, req xendit.CreateEwalletResponse, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string

	switch req.Status {
	case constants.XenditPending:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.StatusPending

	// case constants.XenditVoided:
	// 	cashIn.PaymentStatus = constants.PaymentPaid
	// 	cashIn.PaymentAt = int(time.Now().Unix())
	// 	cashIn.Status = constants.StatusOnDelivery
	// 	cashinItemsStatus = constants.PaymentProcessing

	case constants.XenditSucess:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())
		cashinItemsStatus = constants.CashInItemStatusPaid

	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Xendit")
	return nil
}

func (s *defaultCashinTransaction) updateStatusXenditInvoiceCashinTransaction(ctx context.Context, req xendit.CreateInvoiceResponse, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string

	switch req.Status {
	case constants.XenditInvoiceStatusPaid, constants.XenditInvoiceStatusSettled:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())
		cashinItemsStatus = constants.CashInItemStatusPaid

	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Xendit")
	return nil
}

func (s *defaultCashinTransaction) updateStatusXenditFixedPaymentCodeCashinTransaction(ctx context.Context, req xendit.GetFixedPaymentCodeResponse, payments xendit.GetFixedPaymentCodePaymentsResponse, cashIn *domain.CashInTransactions) error {
	var cashinItemsStatus string
	var paidAt int = int(time.Now().Unix())

	// it is comes from callback?
	if len(payments.Data) > 0 {
		if payments.Data[0].Status == "SETTLING" && payments.Data[0].TransactionTimestamp != nil {
			req.Status = constants.XenditPaymentFixedCodeStatusInactive

			// set payment based on the callback
			paidAt = int(payments.Data[0].TransactionTimestamp.Unix())
		}
	}

	switch req.Status {
	case constants.XenditPaymentFixedCodeStatusActive:
		// status ACTIVE: Status is ACTIVE if fixed payment code has not paid or has not expired yet
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.CashInStatusPending

	case constants.XenditPaymentFixedCodeStatusInactive:
		// status INACTIVE: Status is INACTIVE if fixed payment code is already paid.
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid

		if cashIn.PaymentAt <= 0 { // not set from callback set the payment time and retry will not replace the time
			cashIn.PaymentAt = paidAt
		}

	case constants.XenditPaymentFixedCodeStatusExpired:
		// status EXPIRED: Status is EXPIRED if fixed payment code that has expiration date and been updated to the date less than now.
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusExpired
	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	respByte, err := json.Marshal(req)
	if err != nil {
		logger.Error(ctx, err.Error(), logger.String("message", "[cashin callback] Callback Transcation Xendit"), logger.String("request", string(respByte)))
	}

	err = s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		logger.Error(ctx, err.Error(), logger.String("message", "[cashin callback] Callback Transcation Xendit"), logger.Any("cashIn", cashIn))
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Xendit")
	return nil
}

func (s *defaultCashinTransaction) updateStatusOttocashCashinTransaction(ctx context.Context, responseCode string, cashIn *domain.CashInTransactions, paymentProviderPayload []byte) error {
	var cashinItemsStatus string

	switch true {
	case responseCode == constants.OttocashStatusCodePending && (cashIn.ExpiredAt != nil && cashIn.ExpiredAt.Before(time.Now())):
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusFailed
	case responseCode == constants.OttocashStatusCodePending:
		return nil

	case responseCode == constants.OttocashStatusCodeSuccess:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())

	default:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, paymentProviderPayload, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Xendit")
	return nil
}

func (s *defaultCashinTransaction) updateStatusNicePayVACashinTransaction(ctx context.Context, req nicepay.InquiryResponse, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string

	switch req.Status {
	case nicepay.PaymentStatusVaUnpaid:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.StatusPending
	case nicepay.PaymentStatusVaPaid:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())
	case nicepay.PaymentStatusVaExpired:
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.PaymentExpired
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Nicepay")
	return nil
}

func (s *defaultCashinTransaction) updateStatusSnapNicePayVACashinTransaction(ctx context.Context, req snapclient.TransferVaStatusResp, cashIn *domain.CashInTransactions) error {
	respByte, _ := json.Marshal(req)
	var cashinItemsStatus string
	status, _ := req.VirtualAccountData.AdditionalInfo.Get("latestTransactionStatus").(string)
	//"vacctValidDt": "********",
	vacctValidDt, _ := req.VirtualAccountData.AdditionalInfo.Get("vacctValidDt").(string)
	//"vacctValidTm": "232505",
	vacctValidTm, _ := req.VirtualAccountData.AdditionalInfo.Get("vacctValidTm").(string)
	timestring := vacctValidDt + vacctValidTm
	timeparse, _ := time.Parse("**************", timestring)
	expiredTime := time.Date(timeparse.Year(), timeparse.Month(), timeparse.Day(), timeparse.Hour(), timeparse.Minute(), timeparse.Second(), timeparse.Nanosecond(), utils.TimeJakartaTZ)
	isExpired := !timeparse.IsZero() && !expiredTime.Before(time.Now().In(utils.TimeJakartaTZ))

	switch status {
	case snapclient.NicepayTranscStatusInitiated, snapclient.NicepayTranscStatusPending:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.StatusPending
	case snapclient.NicepayTranscStatusSuccess:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())
	default:
		if isExpired {
			cashIn.PaymentStatus = constants.PaymentExpired
			cashIn.Status = constants.PaymentExpired
		} else {
			// NicepayTranscStatusRefunded,NicepayTranscStatusCancelled,NicepayTranscStatusFailed,NicepayTranscStatusNotFound
			cashIn.PaymentStatus = constants.PaymentFailed
			cashIn.Status = constants.CashInStatusFailed
		}
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, respByte, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	logger.Info(ctx, "[cashin callback] Callback Transcation Nicepay")
	return nil
}

func (s *defaultCashinTransaction) updateStatusDokuVACashinTransaction(ctx context.Context, req doku.OrderStatusRes, rawReq []byte, cashIn *domain.CashInTransactions) error {
	var cashinItemsStatus string
	status := req.Transaction.Status

	switch true {
	case status == doku.VA_STATUS_PENDING:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.CashInStatusPending

	case status == doku.VA_STATUS_SUCCESS:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())

	case status == doku.VA_STATUS_EXPIRED:
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusExpired
	case status == doku.VA_STATUS_FAILED:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed

	default:
		logger.Info(ctx, "default case", logger.String("status", status))
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, rawReq, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	return nil
}

func (s *defaultCashinTransaction) updateStatusCashlezOfflinePgCashinTransaction(ctx context.Context, cashlezData cashlez.TransactionAdvanceSearchRespData, rawReq []byte, cashIn *domain.CashInTransactions) error {
	var cashinItemsStatus string
	status := fmt.Sprint(cashlezData.Status)
	logger.Info(ctx, fmt.Sprintf("cashlezData %+v", cashlezData))
	isPaid := status == cashlez.APPROVAL_STATUS_APPROVED || // when get callback approved
		(cashIn.Status == constants.CashInStatusPending && status == cashlez.APPROVAL_STATUS_SETTLED) // or cashless status settled but current status is not paid

	switch true {
	case isPaid:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		cashIn.PaymentAt = int(time.Now().Unix())

	case status == cashlez.APPROVAL_STATUS_SETTLED && cashIn.Status == constants.CashInStatusPaid:
		// this is edge case
		// normaly status already handled to paid when get approval status "APPROVED"
		logger.Info(ctx, "cashles status already paid, skip process") // do nothing

	case status == cashlez.APPROVAL_STATUS_PENDING && cashIn.ExpiredAt != nil && time.Now().Before(*cashIn.ExpiredAt):
		logger.Info(ctx, "cashles status still pending and not expired, skip process") // do nothing

	// case status == cashlez.OFFLINE_PG_STATUS_CANCEL:
	// 	cashIn.PaymentStatus = constants.PaymentFailed
	// 	cashIn.Status = constants.CashInStatusFailed

	case cashIn.ExpiredAt != nil && cashIn.ExpiredAt.Before(time.Now()):
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusExpired

	default:
		logger.Info(ctx, "default case", logger.String("status", status))
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	err := s.updateWachingStatusCashin(ctx, cashIn, rawReq, cashinItemsStatus, domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		return err
	}

	return nil
}

// for now only used for VA
func (s *defaultCashinTransaction) UpdateStatusSnapCashinTransaction(ctx context.Context, cashIn *domain.CashInTransactions, cbData domain.SnapCbCashInTransaction, opts domain.UpdateWachingStatusCashinOpts) error {
	var cashinItemsStatus string
	toStatus := cbData.Status
	switch true {
	case toStatus == constants.CashInStatusPending:
		cashIn.PaymentStatus = constants.PaymentDraft
		cashIn.Status = constants.CashInStatusPending

	case toStatus == constants.CashInStatusPaid:
		cashIn.PaymentStatus = constants.PaymentSettled
		cashIn.Status = constants.CashInStatusPaid
		cashinItemsStatus = constants.CashInItemStatusPaid
		if cashIn.ManualPaymentAt == nil { // if manual paid, doesnt need to overide payment_at to current time
			cashIn.PaymentAt = int(time.Now().Unix())
		}
		if opts.PaymentAt != nil { // case for scheduler pending transaction
			cashIn.PaymentAt = int(opts.PaymentAt.Unix())
		}

	case toStatus == constants.CashInStatusFailed:
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed

	case toStatus == constants.CashInStatusExpired:
		cashIn.PaymentStatus = constants.PaymentExpired
		cashIn.Status = constants.CashInStatusExpired

	default:
		logger.Info(ctx, "default case", logger.String("status", toStatus))
		cashIn.PaymentStatus = constants.PaymentFailed
		cashIn.Status = constants.CashInStatusFailed
	}

	rawReq, _ := json.Marshal(cbData)
	err := s.updateWachingStatusCashin(ctx, cashIn, rawReq, cashinItemsStatus, opts)
	if err != nil {
		return err
	}

	return nil
}

func (s *defaultCashinTransaction) updateWachingStatusCashin(ctx context.Context, cashIn *domain.CashInTransactions, paymentProviderPayload []byte, cashinItemsStatus string, opt domain.UpdateWachingStatusCashinOpts) error {
	company, err := s.companyService.GetCompanyById(ctx, strconv.Itoa(cashIn.CompanyID))
	if err != nil {
		return err
	}

	productID := strconv.Itoa(cashIn.CompanyProductID)
	product, err := s.companyProductService.GetCompanyProductById(ctx, productID)
	if err != nil {
		return err
	}
	cashIn.CompanyCode = company.Code

	tx := s.cashinRepo.BeginTrans().WithContext(ctx)
	defer tx.Rollback()

	err = s.cashinRepo.UpdateCashinTransaction(ctx, tx, cashIn)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "failed update data cashin")
		logger.Error(ctx, err.Error())
		return err
	}

	if cashIn.Status == constants.CashInStatusPaid {
		providerId := strconv.Itoa(cashIn.PaymentProviderID)
		channelId := strconv.Itoa(cashIn.PaymentChannelID)
		cashInCapability := strconv.Itoa(constants.CashInCapability)
		chMapping, err := s.channelMappingRepo.GetChannelMappingByProviderChannelIdAndCapability(ctx, providerId, channelId, cashInCapability)
		if err != nil {
			chMapping.Sla = 1
			logger.Error(ctx, "[cashin callback] channel mapping not found")
		}
		if chMapping.Sla == 0 {
			chMapping.Sla = 1
		}

		slaDate := time.Now().Add(time.Hour * (time.Duration(chMapping.Sla) * 24))
		items, errRes := s.cashinRepo.GetCashinTranscationItemByCashinId(ctx, strconv.Itoa(cashIn.ID))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, errRes.Error())
			logger.Error(ctx, err.Error())
		}
		for i := 0; i < len(*items); i++ {
			item := (*items)[i]
			item.SlaDate = &slaDate
			err = s.cashinRepo.UpdateCashinTransactionItems(ctx, &item, tx)
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, "failed update data cash in item")
				logger.Error(ctx, err.Error())
				return err
			}
		}
	}

	if err := s.setCacheCashinTransaction(ctx, cashIn); err != nil {
		logger.Error(ctx, err.Error())
	}

	respHistory, err := s.cashinRepo.GetCashinTransactionHistoryFromCallback(ctx, cashIn.ID, cashIn.PaymentProviderID, cashIn.PaymentChannelID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return nil
	}

	histDesc := respHistory.Description
	if len(opt.HistoryDescription) > 0 {
		histDesc = opt.HistoryDescription
	}

	// check jika kondisi status history cashin tidak sama dengan
	// status cashin skrg
	if respHistory.Status != cashIn.Status {
		reqHistory := domain.CashInTransactionHistories{
			CashInTransactionID:    respHistory.CashInTransactionID,
			Description:            histDesc,
			PaymentStatus:          cashIn.PaymentStatus,
			Status:                 cashIn.Status,
			VirtualAccount:         respHistory.VirtualAccount,
			InvoiceNumber:          respHistory.InvoiceNumber,
			ProviderId:             respHistory.ProviderId,
			ChannelId:              respHistory.ChannelId,
			PgReferenceInformation: string(paymentProviderPayload),
		}
		err = s.cashinRepo.CreateCashinTransactionHistory(ctx, tx, &reqHistory)
		if err != nil {
			logger.Error(ctx, err.Error())
		}
	}

	err = tx.Commit().Error
	if err != nil {
		logger.Error(ctx, "[cashin callback] Failed to commit transaction", logger.Err(err))
		return err
	}

	// if cashinItemsStatus not empty ,update item status
	if len(cashinItemsStatus) > 0 {
		err = s.cashinRepo.UpdateStatusCashinTransactionItemsByCashinTranscationId(ctx, cashIn.ID, cashinItemsStatus)
		if err != nil {
			logger.Error(ctx, err.Error())
		}
	}

	keyCache := fmt.Sprintf("trx-%v-%v-%v", cashIn.ID, cashIn.PaymentProviderID, cashIn.PaymentChannelID)
	s.cache.Del(ctx, keyCache)

	// callBackResp := &domain.CashinTransactionCallBack{
	// 	CashInTransactions: *cashIn,
	// 	ProductCode:        product.Code,
	// 	CompanyCode:        company.Code,
	// }

	// msq := messagebrokerMessage.Message{
	// 	Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
	// 	Content: []byte(interfacepkg.Marshal(callBackResp)),
	// }
	// infrastructure.PublishMessage(strings.Replace(constants.CallbackClient, "*", fmt.Sprintf("#%s.*", strings.ToLower(company.Code)), 1), &msq)

	if err := s.PublishCallbackToClient(ctx, "", cashIn, product, company); err != nil {
		logger.Error(ctx, err.Error())
	}

	return nil
}

func (s *defaultCashinTransaction) PublishCallbackToClient(ctx context.Context, invNumber string, cashIn *domain.CashInTransactions, product *domain.CompanyProducts, company *domain.Companies) (err error) {
	var productCode, companyCode string

	if cashIn == nil {
		cashIn, err = s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, invNumber)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
	}

	if product == nil {
		productID := strconv.Itoa(cashIn.CompanyProductID)
		product, err = s.companyProductService.GetCompanyProductById(ctx, productID)
		if err != nil {
			return err
		}
	}
	productCode = product.Code

	if company == nil {
		company, err = s.companyService.GetCompanyById(ctx, strconv.Itoa(cashIn.CompanyID))
		if err != nil {
			return err
		}
	}
	companyCode = company.Code

	callBackResp := &domain.CashinTransactionCallBack{
		CashInTransactions: *cashIn,
		ProductCode:        productCode,
		CompanyCode:        companyCode,
	}

	msgString := interfacepkg.Marshal(callBackResp)
	logger.Info(ctx, fmt.Sprintf("callback msg %s", msgString))

	msq := messagebrokerMessage.Message{
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: []byte(msgString),
	}
	infrastructure.PublishMessage(strings.Replace(constants.CallbackClient, "*", fmt.Sprintf("#%s.*", strings.ToLower(company.Code)), 1), &msq)

	return
}

func (s *defaultCashinTransaction) PublishCashOut(ctx context.Context, data domain.CashOutCallback) (err error) {
	if _, exist := s.cashOutCallbackWhitelist[data.CompanyCode]; !exist {
		logger.Info(ctx, fmt.Sprintf("company %s not in cash in item callback whitelist", data.CompanyCode))
		return
	}

	msgString := interfacepkg.Marshal(data)
	logger.Info(ctx, fmt.Sprintf("callback item msg %s", msgString))

	msq := messagebrokerMessage.Message{
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: []byte(msgString),
	}
	infrastructure.PublishMessage(strings.Replace(constants.CallbackCashOutClient, "*", fmt.Sprintf("#%s.*", strings.ToLower(data.CompanyCode)), 1), &msq)
	return
}
