package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type HttpHandler struct {
	uc usecase.Usecase
}

func NewHandler(uc usecase.Usecase) *HttpHandler {
	return &HttpHandler{uc: uc}
}

func (h *HttpHandler) CreateBulk(c *fiber.Ctx) (err error) {
	productID, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid product id")
	}

	var req entity.HttpCreateBulkReq
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	_, err = h.uc.CreateBulk(c.UserContext(), productID, req.ToDomains())
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, req)
}

func (h *HttpHandler) GetAllWithDetail(c *fiber.Ctx) (err error) {
	productID, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid product id")
	}

	res, err := h.uc.GetAllWithDetail(c.UserContext(), productID)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, entity.FromDomainsGetListWithDetail(res))
}

func (h *HttpHandler) PatchDataBulk(c *fiber.Ctx) (err error) {
	productID, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid product id")
	}

	var req entity.HttpPatchDataBulkReq
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	_, err = h.uc.PatchDataBulk(c.UserContext(), productID, req.ToDomains())
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, req)
}

func (h *HttpHandler) UpdateBulk(c *fiber.Ctx) (err error) {
	productID, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid product id")
	}

	var req entity.HttpUpdateBulkReq
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	_, err = h.uc.UpdateBulk(c.UserContext(), productID, req.ToDomains())
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, req)
}

func (h *HttpHandler) DeleteBulk(c *fiber.Ctx) (err error) {
	productID, err := c.ParamsInt("id")
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid product id")
	}

	var req entity.HttpDeleteBulkReq
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	_, err = h.uc.DeleteBulk(c.UserContext(), productID, req.ToDomains())
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	return response.HandleSuccess(c, req)
}

// OPEN API
func (h *HttpHandler) GetOneWithDetailOpenAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	productCode := c.Params("code")
	channelCode := c.Params("channelCode")

	res, err := h.uc.GetOneWithDetailOpenAPI(c.UserContext(), string(auth), productCode, channelCode)
	if err != nil {
		return
	}

	resp := entity.HttpOpenAPIGetOneWithDetailResp{
		Status:        res.Status,
		FeeFixValue:   res.FeeFixValue,
		FeePercentage: res.FeePercentage,
	}
	return response.HandleSuccess(c, resp)
}

func (h *HttpHandler) CalculateOpenAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	productCode := c.Params("code")
	channelCode := c.Params("channelCode")

	var req entity.HttpOpenAPICalculateReq
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	res, err := h.uc.CalculateOpenAPI(c.UserContext(), string(auth), productCode, channelCode, req.Amount)
	if err != nil {
		if (errors.GetErrorCode(err) == http.StatusNotFound) || (!res.CompanyProductProviderChannelMapping.Status) {
			return response.HandleSuccess(c, nil)
		}
		return
	}

	resp := entity.HttpOpenAPICalculateResp{
		Status:           res.CompanyProductProviderChannelMapping.Status,
		FeeFixValue:      res.CompanyProductProviderChannelMapping.FeeFixValue,
		FeePercentage:    res.CompanyProductProviderChannelMapping.FeePercentage,
		Amount:           req.Amount,
		CalculatedAmount: res.CalculatedAmount,
		AdminFee:         res.AdminFee,
	}
	return response.HandleSuccess(c, resp)
}
