package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

func marshalNoErr(any interface{}) string {
	res, _ := json.Marshal(any)
	return string(res)
}

// simple script used for client simulator

func TestVirtualAccount(t *testing.T) {
	config.SetupWithPath("../../../config.json")
	logger.Init("TestVirtualAccount")
	jwtConf := SnapSecretJWT{
		Secret:         "3P1AmbKBlejWvptb2wziCr6CCI93gFKg",
		ExpiredSeconds: 900,
	}
	ctx := context.TODO()
	uc := defaultUC{
		snapSecretJWT: jwtConf,
	}

	// generate token
	exp := time.Duration(uc.snapSecretJWT.ExpiredSeconds) * time.Second
	clientId := "b66925de-d8ec-476e-a170-6cf06c863b78"
	clientSecret := "efc71ced-b0e7-4b47-8270-3c24829764aa"
	token, err := uc.generateToken(ctx, clientId, jwtConf.Secret, exp)
	if err != nil {
		t.Fatalf("error generate token %v", err)
	}
	fmt.Println("JWT token: ", token)
	reqId := uuid.NewString()
	// generate payload for inquiry
	TimeLocAsiaJakarta, _ := time.LoadLocation("Asia/Jakarta")
	now := time.Now().In(TimeLocAsiaJakarta).Format(time.RFC3339)

	type args struct {
		accessToken   string
		httpMethod    string
		path          string
		reqBody       string
		xtimestampStr string
		clientSecret  string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "inquiry payload",
			args: args{
				accessToken: token,
				httpMethod:  http.MethodPost,
				path:        "/openapi/v1.0/transfer-va/inquiry",
				reqBody: marshalNoErr(domain.SnapTransferVaInquiryReq{
					PartnerServiceId:      snaputil.AddLeftPaddingSpace("10687", 8),
					CustomerNo:            "************",
					VirtualAccountNo:      snaputil.AddLeftPaddingSpace("*****************", 8),
					TrxDateInit:           domain.SnapStrTimestamp(now),
					ChannelCode:           6011,
					Language:              "",
					Amount:                nil,
					HashedSourceAccountNo: "",
					SourceBankCode:        "",
					InquiryRequestId:      reqId,
				}),
				xtimestampStr: now,
				clientSecret:  clientSecret,
			},
		},
		{
			name: "payment payload",
			args: args{
				accessToken: token,
				httpMethod:  http.MethodPost,
				path:        "/openapi/v1.0/transfer-va/payment",
				reqBody: marshalNoErr(domain.SnapTransferVaPaymentReq{
					PartnerServiceId: snaputil.AddLeftPaddingSpace("10687", 8),
					CustomerNo:       "************",
					VirtualAccountNo: snaputil.AddLeftPaddingSpace("*****************", 8),
					ChannelCode:      6011,
					PaymentRequestId: reqId,
					PaidAmount:       domain.DefaultSnapAmount(11100.00),
					TrxDateTime:      domain.SnapStrTimestamp(now),
				}),
				xtimestampStr: now,
				clientSecret:  clientSecret,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSign, err := snaputil.GenerateSymetricSignature(tt.args.accessToken, tt.args.httpMethod, tt.args.path, tt.args.reqBody, tt.args.xtimestampStr, tt.args.clientSecret)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateSymetricSignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println("=====================================")
			fmt.Println("case ", tt.name)
			fmt.Println("path: ", tt.args.path)
			fmt.Println("external-id: ", uuid.NewString())
			fmt.Println("body: ", tt.args.reqBody)
			fmt.Println("timestamp: ", tt.args.xtimestampStr)
			fmt.Println("signature: ", gotSign)
		})
	}
}
