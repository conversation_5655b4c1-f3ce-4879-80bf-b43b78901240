package snapclient

import (
	"context"
	"encoding/base64"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

var privKeyStr string = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

var privateKeyBase64 = base64.StdEncoding.EncodeToString([]byte(privKeyStr))

var cred = Credential{
	ClientID:         "MANYOPT001",
	ClientSecret:     "nSVO0eVw7vsfbS6Sa/ThgkHvxHfyiWpH6vE64a+AuJhSmnQyA/Hd1SpS5ceGS6dF9PaaTgOUepJp/Y82feUamQ==",
	PrivateKeyBase64: privateKeyBase64,
}

// testing purpose
func TestNicepayVA(t *testing.T) {
	return
	config.SetupWithPath("../../../config.json")
	logger.Init("TestNicepayVA")
	ctx := context.Background()
	rpaths := DefaultPaths()
	rpaths.AccessTokenB2B = "/v1.0/access-token/b2b"
	w := NewWrapper(WithBaseUrl("https://dev.nicepay.co.id/nicepay"), WithDebug(), WithRegisterPaths(rpaths))
	hreq := HTrxReq{
		XPartnerId: cred.ClientID,
		ChannelId:  "MANYOPT001",
	}
	expiredAt := time.Now().Add(5 * 24 * time.Hour)
	createVaReq := TransferVaCreateVaReq{
		PartnerServiceID:   "",
		CustomerNo:         "",
		VirtualAccountNo:   "",
		VirtualAccountName: "testing",
		TrxID:              uuid.NewString(),
		TotalAmount: Money{
			Currency: "IDR",
			Value:    fmt.Sprintf("%.2f", 100000.0),
		},
		AdditionalInfo: AdditionalInfo{
			Data: map[string]interface{}{
				"bankCd":       "BMRI",
				"goodsNm":      "John Doe",
				"dbProcessUrl": "https://testing.co.id/",
				"vacctValidDt": expiredAt.Format(nicepay.DateFormat),
				"vacctValidTm": expiredAt.Format(nicepay.TimeFormat),
				"msId":         "",
				"msFee":        "",
				"msFeeType":    "",
				"mbFee":        "",
				"mbFeeType":    "",
			},
		},
	}
	createVaResp, err := w.TransferVaCreateVA(ctx, cred, hreq, &createVaReq)
	if err != nil {
		t.Errorf("TransferVaCreateVA error: %v", err)
	}

	// untuk parameter partnerServiceId bisa diambil dari 8 angka pertama dari parameter virtualAccountNo
	partnerServiceId := createVaResp.VirtualAccountData.VirtualAccountNo[:8]
	// untuk parameter customerNo bisa di ambil dari sisa angka setelah 8 angka pertama dari parameter virtualAccountNo
	cutomerNo := createVaResp.VirtualAccountData.VirtualAccountNo[8:]
	reqStatus := TransferVaStatusReq{
		PartnerServiceID: partnerServiceId,
		CustomerNo:       cutomerNo,
		VirtualAccountNo: createVaResp.VirtualAccountData.VirtualAccountNo,
		InquiryRequestID: createVaReq.Raw.Header.Get(HeaderExternalId),
		AdditionalInfo: AdditionalInfo{
			Data: map[string]interface{}{
				"totalAmount": createVaResp.VirtualAccountData.TotalAmount,
				"trxId":       createVaResp.VirtualAccountData.TrxID,
				"tXidVA":      createVaResp.VirtualAccountData.AdditionalInfo.Get("tXidVA"),
			},
		},
	}
	_, err = w.TransferVaStatus(ctx, cred, hreq, &reqStatus)
	if err != nil {
		t.Errorf("TransferVaCreateVA error: %v", err)
	}
}

func TestNicePayCred(t *testing.T) {
	config.SetupWithPath("../../config.json")
	secrets := "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"

	var cred NicepayCred
	GetSnapCredentialCB(secrets, &cred)
	fmt.Printf("cred: %+v", cred)
}
