package doku

import "errors"

const (
	VA_BILLING_TYPE_FIX_BILL             = "FIX_BILL"
	VA_BILLING_TYPE_NO_BILL              = "NO_BILL"
	VA_BILLING_TYPE_BILL_VARIABLE_AMOUNT = "BIL<PERSON>_VARIABLE_AMOUNT"

	//BNI have a special rules, billing type for BNI are OPEN, FIXED,INSTALLMENT,MIN,OPEN MIN and OPEN MAX.
	VA_BNI_BILLING_TYPE_OPEN        = "OPEN"
	VA_BNI_BILLING_TYPE_FIXED       = "FIXED"
	VA_BNI_BILLING_TYPE_INSTALLMENT = "INSTALLMENT"
	VA_BNI_BILLING_TYPE_MIN         = "MIN"
	VA_BNI_BILLING_TYPE_OPEN_MIN    = "OPEN MIN"
	VA_BNI_BILLING_TYPE_OPEN_MAX    = "OPEN MAX"

	VA_BANK_CHANNEL_BCA     = "bank bca"
	VA_BANK_CHANNEL_MANDIRI = "bank mandiri"
	VA_BANK_CHANNEL_BANK    = "bank bank"
	VA_BANK_CHANNEL_DOKU    = "bank doku"
	VA_BANK_CHANNEL_BRI     = "bank bri"
	VA_BANK_CHANNEL_CIMB    = "bank cimb"
	VA_BANK_CHANNEL_PERMATA = "bank permata"
	VA_BANK_CHANNEL_BNI     = "bank bni"
	VA_BANK_CHANNEL_DANAMON = "bank danamon"

	VA_BANK_CHANNEL_BCA_API_PATH     = "/bca-virtual-account"
	VA_BANK_CHANNEL_MANDIRI_API_PATH = "/mandiri-virtual-account"
	VA_BANK_CHANNEL_BSM_API_PATH     = "/bsm-virtual-account"
	VA_BANK_CHANNEL_DOKU_API_PATH    = "/doku-virtual-account"
	VA_BANK_CHANNEL_BRI_API_PATH     = "/bri-virtual-account"
	VA_BANK_CHANNEL_CIMB_API_PATH    = "/cimb-virtual-account"
	VA_BANK_CHANNEL_PERMATA_API_PATH = "/permata-virtual-account"
	VA_BANK_CHANNEL_BNI_API_PATH     = "/bni-virtual-account"
	VA_BANK_CHANNEL_DANAMON_API_PATH = "/danamon-virtual-account"

	VA_STATUS_PENDING = "PENDING"
	VA_STATUS_SUCCESS = "SUCCESS"
	VA_STATUS_EXPIRED = "EXPIRED"
	VA_STATUS_FAILED  = "FAILED"

	CONFIG_CALLBACK_URL_VA             = "doku.callback_url_va"
	CONFIG_VA_CHANNEL_MAPPING_API_PATH = "doku.va_channel_mapping_api_path"
)

var (
	// already moved in config
	// VA_BANK_API_PATH = map[string]string{
	// 	VA_BANK_CHANNEL_BCA:     "/bca-virtual-account",
	// 	VA_BANK_CHANNEL_MANDIRI: "/mandiri-virtual-account",
	// 	VA_BANK_CHANNEL_BANK:    "/bsm-virtual-account",
	// 	VA_BANK_CHANNEL_DOKU:    "/doku-virtual-account",
	// 	VA_BANK_CHANNEL_BRI:     "/bri-virtual-account",
	// 	VA_BANK_CHANNEL_CIMB:    "/cimb-virtual-account",
	// 	VA_BANK_CHANNEL_PERMATA: "/permata-virtual-account",
	// 	VA_BANK_CHANNEL_BNI:     "/bni-virtual-account",
	// 	VA_BANK_CHANNEL_DANAMON: "/danamon-virtual-account",
	// }

	ErrSignature = errors.New("Doku: Signature Invalid")
)
