package usecase

import (
	"context"
	"mime/multipart"

	"repo.nusatek.id/nusatek/payment/domain"
)

type FileRepository interface {
	CreateFile(ctx context.Context, req *domain.Files) (err error)
	UpdateFile(ctx context.Context, req *domain.Files) (err error)
	GetFileById(ctx context.Context, id string) (resp *domain.Files, err error)
	DeleteFile(ctx context.Context, req *domain.Files) (err error)
	GetFileListAll(ctx context.Context) (resp *[]domain.Files, totalData int64, err error)
	GetFileListAllUnassignedByUser(ctx context.Context, types, userUpload string) (resp *[]domain.Files, totalData int64, err error)
}

type FileUsecase interface {
	Upload(ctx context.Context, userID, fileType string, file *multipart.FileHeader) (resp *domain.Files, err error)
	CreateFile(ctx context.Context, req *domain.Files) (err error)
	UpdateFile(ctx context.Context, req *domain.Files, id string) (err error)
	DeleteFile(ctx context.Context, id string) (err error)
	GetFileById(ctx context.Context, id string) (resp *domain.Files, err error)
	GetFileListAll(ctx context.Context) (resp *[]domain.Files, totalData int64, err error)
	GetFileListAllUnassignedByUser(ctx context.Context, types, userUpload string) (resp *[]domain.Files, totalData int64, err error)
}
