package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
)

type TrxRequestLogRepository interface {
	Insert(ctx context.Context, data *domain.TrxRequestLog) (err error)
	UpdateResource(ctx context.Context, data *domain.TrxRequestLog) (err error)
	UpdateResponse(ctx context.Context, data *domain.TrxRequestLog) (err error)
}

type TrxRequestLogUseCase interface {
	Insert(ctx context.Context, data *domain.TrxRequestLog) (err error)
	UpdateResponse(ctx context.Context, data *domain.TrxRequestLog) (err error)
	UpdateResourceCtx(ctx context.Context, rId, rType string) (err error)
}
