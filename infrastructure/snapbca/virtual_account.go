package snapbca

import (
	"context"
	"net/http"

	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

func (w *Wrapper) TransferVaStatus(ctx context.Context, req TransferVaStatusReq, cred Credential) (resp TransferVaStatusResp, err error) {
	partnerServiceID := snaputil.AddLeftPaddingSpace(req.PartnerServiceID, 8)
	req.PartnerServiceID = partnerServiceID
	if len(req.VirtualAccountNo) == 0 {
		req.VirtualAccountNo = partnerServiceID + req.CustomerNo
	}

	doReq := DoReq{
		Method:  http.MethodPost,
		Path:    "/openapi/v1.0/transfer-va/status",
		ReqBody: req,
		Header: map[string]string{
			headerChannelID: "95231",
			headerPartnerID: req.PartnerServiceID, // "10687" for bca
		},
	}

	err = w.doWithSignature(ctx, doReq, &resp, cred)
	if err != nil {
		return
	}

	return
}
