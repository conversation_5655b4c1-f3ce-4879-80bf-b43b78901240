package ottocash

type Credential struct {
	AppID             string `json:"app_id"`
	SecretKey         string `json:"secret_key"`
	CallbackSecretKey string `json:"callback_secret_key"`
}

type ErrorResponse struct {
	ErrorCode   string      `json:"errorCode"`
	FullMessage string      `json:"fullMessage"`
	Message     string      `json:"message"`
	Suppressed  interface{} `json:"suppressed"`
}

type InquiryRequest struct {
	MerchantID   string  `json:"merchantId"`
	MerchantName string  `json:"merchantName"`
	PhoneNumber  string  `json:"phoneNumber"`
	TrxID        string  `json:"trxId"`
	Amount       float64 `json:"amount"`
	MerchantURL  string  `json:"merchantURL"`
	SuccessURL   string  `json:"successURL"`
	FailedURL    string  `json:"failedURL"`
}

type InquiryResponse struct {
	ReferenceNumber string `json:"referenceNumber"`
	SecurePage      string `json:"securePage"`
}

type InquiryCallback struct {
	MerchantID          string  `json:"merchantId"`
	MerchantName        string  `json:"merchantName"`
	PhoneNumber         string  `json:"phoneNumber"`
	TrxID               string  `json:"trxId"`
	ReferenceNumber     string  `json:"referenceNumber"`
	Amount              float64 `json:"amount"`
	ResponseCode        string  `json:"responseCode"`
	ResponseDescription string  `json:"responseDescription"`
}

type CheckStatusRequest struct {
	ReferenceNumber string `json:"referenceNumber"`
}

type CheckStatusResponse struct {
	ErrorCode   string `json:"errorCode"`
	FullMessage string `json:"fullMessage"`
	Message     string `json:"message"`
}
