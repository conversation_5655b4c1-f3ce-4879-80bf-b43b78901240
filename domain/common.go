package domain

import (
	"database/sql/driver"
	"encoding/json"
	"strings"
	"time"
)

type UnixTime int64

func (t UnixTime) Time(loc ...*time.Location) time.Time {
	var l *time.Location
	if len(loc) > 0 {
		l = loc[0]
	}

	if l != nil {
		return time.Unix(int64(t), 0).In(l)
	}
	return time.Unix(int64(t), 0)
}

func (t UnixTime) NullTime() *time.Time {
	if t == 0 {
		return nil
	}

	res := time.Unix(int64(t), 0)
	return &res
}

func (t UnixTime) String(format string, loc ...*time.Location) string {
	if t == 0 {
		return ""
	}

	return t.Time(loc...).Format(format)
}

func (t UnixTime) NullString(format string) *string {
	if t == 0 {
		return nil
	}

	res := t.String(format)
	return &res
}

type StringCommaSeparated string

func (s StringCommaSeparated) Strings() []string {
	if len(s) > 0 {
		strings := strings.Split(string(s), ",")
		return strings
	}
	return []string{}
}

type JSONB map[string]interface{}

func (a JSONB) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *JSONB) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		// return errors.New("type assertion to []byte failed")
		return nil
	}

	return json.Unmarshal(b, &a)
}
