package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/gofiber/fiber/v2"
	logging "github.com/gofiber/fiber/v2/middleware/logger"
	dbConfig "repo.nusatek.id/moaja/backend/libraries/database-handler/config"
	redis "repo.nusatek.id/moaja/backend/libraries/database-handler/redis"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	appconfig "repo.nusatek.id/moaja/backend/libraries/service-config"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/app/rest"
	"repo.nusatek.id/nusatek/payment/app/rest/routers"
	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/database"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	emailclient "repo.nusatek.id/nusatek/payment/infrastructure/email_client"
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapbca"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	cashinHandler "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/handler"
	cashinRepo "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/repository"
	cashinService "repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/usecase"
	cashOutHandler "repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/handler"
	cashOutRepo "repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/repository"
	cashOutService "repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/usecase"
	channelMappingHandler "repo.nusatek.id/nusatek/payment/modules/channel_mapping/handler"
	channelMappingRepo "repo.nusatek.id/nusatek/payment/modules/channel_mapping/repository"
	channelMappingService "repo.nusatek.id/nusatek/payment/modules/channel_mapping/usecase"
	companyHandler "repo.nusatek.id/nusatek/payment/modules/company_management/handler"
	companyRepo "repo.nusatek.id/nusatek/payment/modules/company_management/repository"
	companyService "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	companyMappingHandler "repo.nusatek.id/nusatek/payment/modules/company_mapping/handler"
	companyMappingRepo "repo.nusatek.id/nusatek/payment/modules/company_mapping/repository"
	companyMappingService "repo.nusatek.id/nusatek/payment/modules/company_mapping/usecase"
	companyProductHandler "repo.nusatek.id/nusatek/payment/modules/company_product/handler"
	companyProductRepo "repo.nusatek.id/nusatek/payment/modules/company_product/repository"
	companyProductService "repo.nusatek.id/nusatek/payment/modules/company_product/usecase"
	CompanyProductProviderChannelMappingHandler "repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/handler"
	CompanyProductProviderChannelMappingRepo "repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/repository"
	CompanyProductProviderChannelMappingService "repo.nusatek.id/nusatek/payment/modules/company_product_provider_channel_mapping/usecase"
	configurationEmailHandler "repo.nusatek.id/nusatek/payment/modules/configuration/handler"
	configurationEmailRepo "repo.nusatek.id/nusatek/payment/modules/configuration/repository"
	configurationEmailService "repo.nusatek.id/nusatek/payment/modules/configuration/usecase"
	fileHandler "repo.nusatek.id/nusatek/payment/modules/file/handler"
	fileRepo "repo.nusatek.id/nusatek/payment/modules/file/repository"
	fileService "repo.nusatek.id/nusatek/payment/modules/file/usecase"
	healtyHandler "repo.nusatek.id/nusatek/payment/modules/healty/handler"
	healtyRepo "repo.nusatek.id/nusatek/payment/modules/healty/repository"
	healtyService "repo.nusatek.id/nusatek/payment/modules/healty/usecase"
	partnerHandler "repo.nusatek.id/nusatek/payment/modules/partner/handler"
	partnerRepo "repo.nusatek.id/nusatek/payment/modules/partner/repository"
	partnerService "repo.nusatek.id/nusatek/payment/modules/partner/usecase"
	paymentChannelHandler "repo.nusatek.id/nusatek/payment/modules/payment_channel/handler"
	paymentChannelRepo "repo.nusatek.id/nusatek/payment/modules/payment_channel/repository"
	paymentChannelService "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	paymentProviderHandler "repo.nusatek.id/nusatek/payment/modules/payment_provider/handler"
	paymentProviderRepo "repo.nusatek.id/nusatek/payment/modules/payment_provider/repository"
	paymentProviderService "repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	snapHandler "repo.nusatek.id/nusatek/payment/modules/snap/handler"
	snapRepo "repo.nusatek.id/nusatek/payment/modules/snap/repository"
	snapService "repo.nusatek.id/nusatek/payment/modules/snap/usecase"
	trxReqLogRepo "repo.nusatek.id/nusatek/payment/modules/trx_request_log/repository"
	trxReqLogService "repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
	userHandler "repo.nusatek.id/nusatek/payment/modules/user_management/handler"
	userRepo "repo.nusatek.id/nusatek/payment/modules/user_management/repository"
	userService "repo.nusatek.id/nusatek/payment/modules/user_management/usecase"
	webhookHandler "repo.nusatek.id/nusatek/payment/modules/webhook/handler"
	redisretry "repo.nusatek.id/nusatek/payment/modules/webhook/pkg"
	webhookRepo "repo.nusatek.id/nusatek/payment/modules/webhook/repository"
	webhookSvc "repo.nusatek.id/nusatek/payment/modules/webhook/usecase"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/s3"
)

func main() {
	// Setup Config
	config.Setup()

	// Setup Rabbit MQ Publisher
	// Setup Database
	db := database.NewDatabase()

	// Setup Logger
	opts := []logger.Opt{
		logger.WithEnv(config.GetString("env")),
		logger.WithCaller(1),
		logger.InfoLevel(),
	}
	if config.GetBool("logger.stdout") {
		opts = append(opts, logger.WithWriterStdOut())
	}
	if config.GetBool("logger.file") {
		opts = append(opts, logger.WithWriterFile(logger.FileOpt{
			Paths:    []string{config.GetString("logger.fileLocation")},
			MaxSize:  1024,
			MaxAge:   config.GetInt("logger.fileMaxAge"),
			Compress: true,
		}))
	}
	logger.Init(config.GetString("appName"), opts...)

	// Setup Redis
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*20)
	defer cancel()

	redisClient := redis.NewRedis(ctx, &dbConfig.DBConfig{
		Host: config.GetString("redis.host"),
		Port: config.GetInt("redis.port"),
		User: config.GetString("redis.user"),
		Pass: config.GetString("redis.password"),
	})

	s3Credential := s3.Credential{
		URL:       config.GetString("s3.host"),
		AccessKey: config.GetString("s3.access_key"),
		SecretKey: config.GetString("s3.secret_key"),
		Bucket:    config.GetString("s3.bucket"),
		Region:    config.GetString("s3.region"),
	}

	// Setup Scheduler
	loc, _ := time.LoadLocation(config.GetString("tz"))
	sch := gocron.NewScheduler(loc)

	/* ------------ Init Main App for REST -------------- */
	appVersion := config.GetString("version")
	appHost := config.GetString("host")
	appPort := config.GetString("port")
	appEnv := config.GetString("env")

	retryConfig := redisretry.Config{
		RedisAddr:       fmt.Sprintf("%s:%d", config.GetString("redis.host"), config.GetInt("redis.port")),
		Password:        config.GetString("redis.password"),
		Database:        0,
		MaxConcurrency:  10,
		DefaultTTL:      10 * time.Second,
		HandlerTimeout:  5 * time.Second,
		ShutdownTimeout: 10 * time.Second,
	}

	// Create a new RedisRetry instance
	retryService, err := redisretry.NewRedisRetry(retryConfig)
	if err != nil {
		log.Fatalf("Failed to create RedisRetry instance: %v", err)
	}

	log.Printf("%v %v %v %v", appVersion, appHost, appPort, appEnv)

	// Repository
	healtyRepo := healtyRepo.NewRepository(db)
	companyRepo := companyRepo.NewRepository(db)
	companyProductRepo := companyProductRepo.NewRepository(db)
	paymentChannelRepo := paymentChannelRepo.NewRepository(db)
	paymentProviderRepo := paymentProviderRepo.NewRepository(db)
	channelMappingRepo := channelMappingRepo.NewRepository(db)
	partnerRepo := partnerRepo.NewRepository(db)
	companyMappingCounterRepo := companyMappingRepo.NewCounterRepository(db)
	companyMappingCustomerFVARepo := companyMappingRepo.NewCustomerFVARepository(db)
	companyMappingRepo := companyMappingRepo.NewRepository(db)
	cashinRepo := cashinRepo.NewRepository(db)
	cashOutRepo := cashOutRepo.NewRepository(db)
	userRepo := userRepo.NewRepository(db)
	fileRepo := fileRepo.NewRepository(db)
	CompanyProductProviderChannelMappingRepo := CompanyProductProviderChannelMappingRepo.NewGORMRepository(db)
	snapRepo := snapRepo.NewRepository(db, snapService.RepoFuncWrapper{
		GetCashinTransactionById:            cashinRepo.GetCashinTransactionById,
		GetCashinTranscationItemByCashinId:  cashinRepo.GetCashinTranscationItemByCashinId,
		GetChannelMappingByCode:             channelMappingRepo.GetChannelMappingByCode,
		GetLastCashinHistoryByVaAndStatus:   cashinRepo.GetLastCashinHistoryByVaAndStatus,
		GetCashinTransactionByInvoiceNumber: cashinRepo.GetCashinTransactionByInvoiceNumber,
	})
	trxReqLogRepo := trxReqLogRepo.NewRepository(db)
	webhookRepo := webhookRepo.NewWebhookDeliveryLogRepository(db)

	configurationEmailTemplateRepo := configurationEmailRepo.NewEmailTemplateRepository(db)
	configurationEmailLogRepo := configurationEmailRepo.NewEmailLogRepository(db)

	// Service Wrapper
	xpersService := xfers.NewWrapper().Setup()
	nicePayService := nicepay.NewWrapper().Setup()
	xenditService := xendit.NewWrapper().Setup()
	ottocashService := ottocash.NewWrapper().Setup()
	dokuService := doku.NewWrapper().Setup()
	cashlezService := cashlez.NewWrapper().Setup().WithRedis(redisClient)
	snapBcaService := snapbca.NewWrapper().Setup().WithRedis(redisClient)
	emailClient := emailclient.NewWrapper().Setup()
	// snap nicepay
	snapNicepaypaths := snapclient.DefaultPaths()
	snapNicepaypaths.AccessTokenB2B = "/v1.0/access-token/b2b"
	snapNicepayConfs := []snapclient.Opt{
		snapclient.WithBaseUrl(config.GetString("snap_nicepay.host")),
		snapclient.WithRegisterPaths(snapNicepaypaths),
	}
	if config.GetBool("snap_nicepay.debug") {
		snapNicepayConfs = append(snapNicepayConfs, snapclient.WithDebug())
	}
	snapNicepay := snapclient.NewWrapper(snapNicepayConfs...)
	// Service Usecase
	healtyService := healtyService.Setup().
		SetHealtyRepo(healtyRepo).Validate()

	companyService := companyService.Setup().
		SetCompanyRepo(companyRepo).
		SetRedisClient(redisClient).Validate()

	webhookService := webhookSvc.Setup().
		SetCompanyRepo(companyRepo).
		SetWebhookRepo(webhookRepo).
		SetRetryService(retryService).
		SetWebhookService().
		Validate()

	userService := userService.Setup().
		SetUserManagementRepo(userRepo).
		SetRedisClient(redisClient).
		SetCompanyService(companyService).Validate()

	companyProductService := companyProductService.Setup().
		SetCompanyProductRepo(companyProductRepo).
		SetCompanyRepo(companyRepo).
		SetRedisClient(redisClient).
		SetCompanyService(companyService).Validate()

	paymentChannelService := paymentChannelService.Setup().
		SetPaymentChannelRepo(paymentChannelRepo).
		SetRedisClient(redisClient).
		SetS3(&s3Credential).Validate()

	paymentProviderService := paymentProviderService.Setup().
		SetPaymentProviderRepo(paymentProviderRepo).
		SetRedisClient(redisClient).Validate()

	parnerService := partnerService.Setup().
		SetChannelRepo(paymentChannelRepo).
		SetRedisClient(redisClient).
		SetPartnerRepo(partnerRepo).
		SetCompanyService(companyService).
		SetCompanyMappingRepo(companyMappingRepo).Validate()

	channelMappingService := channelMappingService.Setup().
		SetChannelMappingRepo(channelMappingRepo).
		SetPaymentChannelRepo(paymentChannelRepo).
		SetPaymentProviderRepo(paymentProviderRepo).Validate()

	companyMappingService := companyMappingService.Setup().
		SetCounterRepo(companyMappingCounterRepo).
		SetCustomerFVARepo(companyMappingCustomerFVARepo).
		SetChannelMappingRepo(channelMappingRepo).
		SetChannelService(paymentChannelService).
		SetCompanyMappingRepo(companyMappingRepo).
		SetCompanyService(companyService).
		SetPaymentProviderService(paymentProviderService).
		SetRedisClient(redisClient).
		SetS3(&s3Credential).Validate()

	CompanyProductProviderChannelMappingService := CompanyProductProviderChannelMappingService.NewUsecase(CompanyProductProviderChannelMappingRepo, companyService)

	trxReqLogService := trxReqLogService.Setup().SetRepo(trxReqLogRepo).Validate()

	cashinService := cashinService.Setup().
		SetCashinRepo(cashinRepo).
		SetChannelMappingRepo(channelMappingRepo).
		SetChannelService(paymentChannelService).
		SetCompanyMappingRepo(companyMappingRepo).
		SetCompanyMappingService(companyMappingService).
		SetCompanyProductService(companyProductService).
		SetCompanyProductMappingService(CompanyProductProviderChannelMappingService).
		SetCompanyService(companyService).
		SetNicePayRest(nicePayService).
		SetPartnerService(parnerService).
		SetProviderService(paymentProviderService).
		SetRedisClient(redisClient).
		SetXfersRest(xpersService).
		SetXenditRest(xenditService).
		SetOttocashRest(ottocashService).
		SetDokuRest(dokuService).
		SetCashlezRest(cashlezService).
		SetSnapBcaRest(snapBcaService).
		SetUserService(userService).
		SetSnapRepo(snapRepo).
		SetSnapNicepay(snapNicepay).
		SetTrxReqLogService(trxReqLogService).
		SetCashOutCallbackWhitelist().
		SetS3(&s3Credential).
		Validate()

	cashOutService := cashOutService.Setup().
		SetCashOutRepo(cashOutRepo).
		SetCashinRepo(cashinRepo).
		SetChannelRepo(paymentChannelRepo).
		SetCompanyMappingRepo(companyMappingRepo).
		SetCompanyMappingService(companyMappingService).
		SetPaymentProviderService(paymentProviderService).
		SetRedisClient(redisClient).
		SetUserManagementService(userService).
		SetXfersRest(xpersService).
		SetPartnerRepo(partnerRepo).
		SetComProductRepo(companyProductRepo).
		SetXenditRest(xenditService).
		SetChannelMappingRepo(channelMappingRepo).
		SetCompanyService(companyService).
		SetUserManagementRepo(userRepo).
		SetEmailClient(emailClient).
		SetCashinUc(cashinService).
		SetTrxReqLogService(trxReqLogService).
		SetEmailTemplateRepo(configurationEmailTemplateRepo).
		Validate()

	fileService := fileService.Setup().
		SetFileRepo(fileRepo).
		SetRedisClient(redisClient).
		SetS3(&s3Credential).Validate()

	snapService := snapService.Setup().SetRepo(snapRepo).SetSnapSecretJWT(snapService.SnapSecretJWT{
		Secret:         config.GetString("snap.jwt.secret"),
		ExpiredSeconds: config.GetInt("snap.jwt.expired_seconds"),
	}).SetRedisClient(redisClient).SetFnWrapper(snapService.UcFuncWrapper{
		UpdateStatusSnapCashinTransaction: cashinService.UpdateStatusSnapCashinTransaction,
	}).SetTrxReqLogService(trxReqLogService)

	e := email.NewMailgunEmailProvider(
		config.GetString("mailgun.domain"),
		config.GetString("mailgun.api_key"),
		config.GetString("mailgun.webhook_key"),
	)

	configurationEmailTemplateService := configurationEmailService.NewEmailTemplateUsecase(configurationEmailTemplateRepo)
	configurationEmailLogService := configurationEmailService.NewEmailLogUsecase(configurationEmailLogRepo, e, emailClient)

	// Handler
	healtyHandler := healtyHandler.NewHandler(healtyService)
	companyHandler := companyHandler.NewHandler(companyService)
	userHandler := userHandler.NewHandler(userService)
	companyProductHandler := companyProductHandler.NewHandler(companyProductService)
	paymentChannelHandler := paymentChannelHandler.NewHandler(paymentChannelService)
	paymentProviderHandler := paymentProviderHandler.NewHandler(paymentProviderService)
	channelMappingHandler := channelMappingHandler.NewHandler(channelMappingService)
	partnerHandler := partnerHandler.NewHandler(parnerService)
	companyMappingHandler := companyMappingHandler.NewHandler(companyMappingService)
	companyProductMappingHandler := CompanyProductProviderChannelMappingHandler.NewHandler(CompanyProductProviderChannelMappingService)
	cashinHandler := cashinHandler.NewHandler(cashinService, cashOutService)
	cashOutHandler := cashOutHandler.NewHandler(cashOutService)
	fileHandler := fileHandler.NewHandler(fileService)
	snapHandler := snapHandler.NewHandler(snapService)
	webhookHandler := webhookHandler.NewHandler(webhookService)
	configurationEmailTemplateHandler := configurationEmailHandler.NewEmailTemplateHandler(configurationEmailTemplateService)
	configurationEmailLogHandler := configurationEmailHandler.NewEmailLogHandler(configurationEmailLogService)

	// Router setting
	router := routers.SetupRest().
		SetupHealtyHandler(healtyHandler).
		SetupCompanyHandler(companyHandler).
		SetupCompanyProductHandler(companyProductHandler).
		SetupPaymentChannelHandler(paymentChannelHandler).
		SetupPaymentProviderHandler(paymentProviderHandler).
		SetupChannelMappingHandler(channelMappingHandler).
		SetupPartnerHandler(partnerHandler).
		SetupCompanyMappingHandler(companyMappingHandler).
		SetupCompanyProductProviderChannelMappingHandler(companyProductMappingHandler).
		SetupCashInHandler(cashinHandler).
		SetupCashOutHandler(cashOutHandler).
		SetupUserManagementHandler(userHandler).
		SetupWebhook(webhookHandler).
		SetupFileHandler(fileHandler).
		SetupSnapHandler(snapHandler).
		SetupConfigurationEmailHandler(configurationEmailTemplateHandler).
		SetupConfigurationEmailLogHandler(configurationEmailLogHandler).
		Validate()

	fiberApp := fiber.New(fiber.Config{
		Prefork:               false,
		DisableStartupMessage: false,
		ReadTimeout:           30 * time.Second,
		ErrorHandler:          errors.ErrorHandle,
	})

	rest.SetupMiddleware(fiberApp, trxReqLogService)
	route := fiberApp.Group(appVersion)
	if appEnv != appconfig.ENV_PRODUCTION {
		route.Use(logging.New())
	}

	snapGroup := fiberApp.Group("/openapi")
	if appEnv != appconfig.ENV_PRODUCTION {
		snapGroup.Use(logging.New())
	}

	router.SetupOpenAPI(route)
	router.SetupRouters(route, redisClient)
	router.SetupCallback(fiberApp, trxReqLogService)
	router.SetupScheduler(sch)
	router.SetupSubcriber()
	router.SetupSnapAPI(snapGroup)

	sch.StartAsync()
	go RunRetryService(retryService)

	err = fiberApp.Listen(fmt.Sprintf("%v:%v", appHost, appPort))
	if err != nil {
		log.Fatalf("Failed to start server: %v", err.Error())
	}
}

func RunRetryService(rr *redisretry.RedisRetry) {
	// Wait for expiration events in a goroutine
	go func() {
		err := rr.WaitForExpiration()
		if err != nil {
			log.Fatalf("Error waiting for expiration: %v", err)
		}
	}()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	rr.Shutdown()
	log.Println("Shutdown signal received, initiating graceful shutdown...")
}
