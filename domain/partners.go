package domain

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type Partners struct {
	ID               int            `gorm:"primaryKey" json:"id"`
	CompanyID        int            `json:"company_id"`
	Name             string         `json:"name"`
	Phone            string         `json:"phone"`
	ArAccount        string         `json:"ar_account"`
	ApAccount        string         `json:"ap_account"`
	FeePercentage    float64        `json:"fee_percentage"`
	FeeFixValue      float64        `json:"fee_fix_value"`
	Sla              int            `json:"sla"`
	Status           bool           `json:"status"`
	Code             string         `json:"code" validate:"min=4,alphanum"`
	Email            string         `json:"email" validate:"required,email"`
	Emails           pq.StringArray `gorm:"type:text[]" json:"emails"`
	IsSendEmail      bool           `json:"is_send_email"`
	PaymentChannelId int            `json:"payment_channel_id"`
	FeeBehind        bool           `json:"fee_behind"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (p *Partners) TableName() string {
	return "partners"
}
