package usecase

import (
	"context"
	"net/http"
	"strconv"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
)

func (s *defaultUserManagement) CreateRole(ctx context.Context, req *domain.Roles) (err error) {
	tx := s.userRepo.BeginTrans()
	defer tx.Rollback()

	err = s.userRepo.CreateRole(ctx, req, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	menus, err := s.userRepo.GetListUserMenuAll(ctx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var rolePermission []domain.RolePermissions
	_, err = s.createRolePermissionMenu(ctx, &rolePermission, menus, strconv.Itoa(req.ID), tx)
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (s *defaultUserManagement) UpdateRole(ctx context.Context, req *domain.Roles, id string) (err error) {
	role, err := s.userRepo.GetRoleById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}
	req.ID = role.ID
	req.CreatedAt = role.CreatedAt
	err = s.userRepo.UpdateRole(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}
func (s *defaultUserManagement) UpdateStatusRole(ctx context.Context, id int, status bool) (err error) {
	role, err := s.userRepo.GetRoleById(ctx, strconv.Itoa(id))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}

	role.Status = status
	err = s.userRepo.UpdateRole(ctx, role)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultUserManagement) GetRoleById(ctx context.Context, id string) (resp *domain.Roles, err error) {
	resp, err = s.userRepo.GetRoleById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultUserManagement) DeleteRole(ctx context.Context, id string) (err error) {
	role, err := s.userRepo.GetRoleById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "role not found")
		logger.Error(ctx, err.Error())
		return
	}
	err = s.userRepo.DeleteRole(ctx, role)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultUserManagement) SearchRoles(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Roles, totalData int64, err error) {
	switch true {
	case paginate.Key != "":
		resp, totalData, err = s.userRepo.SearchRole(ctx, paginate)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	default:
		resp, totalData, err = s.userRepo.GetListRole(ctx, paginate)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}
	return
}

func (s *defaultUserManagement) GetListAllRole(ctx context.Context, status string) (resp *[]domain.Roles, err error) {
	switch status {
	case "true":
		resp, err = s.userRepo.GetListRoleAllStatusTrue(ctx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	case "false":
		resp, err = s.userRepo.GetListRoleAllStatusFalse(ctx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

	default:
		resp, err = s.userRepo.GetListRoleAll(ctx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}
	return
}
