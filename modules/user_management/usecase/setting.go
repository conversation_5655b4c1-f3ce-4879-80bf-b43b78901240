package usecase

import (
	company "repo.nusatek.id/nusatek/payment/modules/company_management/usecase"

	"github.com/go-redis/redis/v8"
)

type defaultUserManagement struct {
	userRepo       UserManagementRepository
	cache          *redis.Client
	companyService company.CompanyManagementUsecase
}

func Setup() *defaultUserManagement {
	return &defaultUserManagement{}
}

func (s *defaultUserManagement) SetUserManagementRepo(t UserManagementRepository) *defaultUserManagement {
	s.userRepo = t
	return s
}

func (s *defaultUserManagement) SetRedisClient(t *redis.Client) *defaultUserManagement {
	s.cache = t
	return s
}

func (s *defaultUserManagement) SetCompanyService(t company.CompanyManagementUsecase) *defaultUserManagement {
	s.companyService = t
	return s
}

func (s *defaultUserManagement) Validate() UserManagementUsecase {
	if s.userRepo == nil {
		panic("user repo is nil")
	}

	if s.cache == nil {
		panic("cache client is nil")
	}

	if s.companyService == nil {
		panic("company service is nil")
	}

	return s
}