package usecase

import (
	"context"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
)

func (uc defaultUC) GenerateSignatureAuth(ctx context.Context, req domain.SnapGenerateSignatureAuthReq) (resp domain.SnapGenerateSignatureAuthResp, err error) {
	if !req.Timestamp.Valid() {
		logger.Error(ctx, "invalid timestamp", logger.Err(err))
		err = resp.Set(http.StatusBadRequest, "00", "invalid timestamp")
		return
	}

	resp.SnapBaseResp = snaputil.NewBaseRespCtx(ctx)
	privPem := req.PrivateKey.Decode()
	if privPem == "" {
		logger.Error(ctx, "invalid private key", logger.Err(err))
		err = resp.Set(http.StatusBadRequest, "00", "invalid private key")
		return
	}
	resp.Signature, err = snaputil.GenerateAsymetricSignature(req.PrivateKey.Decode(), req.ClientId, req.Timestamp.String())
	if err != nil {
		logger.Error(ctx, "GenerateSignatureAuth", logger.Err(err))
		err = resp.Set(http.StatusInternalServerError, "00", "Generate Signature Error")
		return
	}
	return
}

func (uc defaultUC) GenerateSignatureService(ctx context.Context, req domain.SnapGenerateSignatureServiceReq) (resp domain.SnapGenerateSignatureServiceResp, err error) {
	if !req.Timestamp.Valid() {
		logger.Error(ctx, "invalid timestamp", logger.Err(err))
		err = resp.Set(http.StatusBadRequest, "00", "invalid timestamp")
		return
	}

	resp.SnapBaseResp = snaputil.NewBaseRespCtx(ctx)
	resp.Signature, err = snaputil.GenerateSymetricSignature(req.AccessToken, req.HttpMethod, req.EndpoinUrl, string(req.ReqBody), req.Timestamp.String(), req.ClientSecret)
	if err != nil {
		logger.Error(ctx, "GenerateSignatureAuth", logger.Err(err))
		err = resp.Set(http.StatusInternalServerError, "00", "Generate Signature Error")
	}
	return
}
