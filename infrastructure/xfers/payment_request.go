package xfers

import (
	"time"
)

type PaymentRequest struct {
	PaymentData PaymentDataReq `json:"data"`
}

type PaymentDataReq struct {
	Attributes AttributeValueRequest `json:"attributes"`
}

type AttributeValueRequest struct {
	PaymentMethodType    string      `json:"paymentMethodType"`
	Amount               float64     `json:"amount"`
	ReferenceID          string      `json:"referenceId"`
	ExpiredAt            time.Time   `json:"expiredAt,omitempty"`
	Description          string      `json:"description"`
	PaymentMethodOptions interface{} `json:"paymentMethodOptions"`
}

type PaymentOptionVA struct {
	BankShortCode string `json:"bankShortCode"`
	DisplayName   string `json:"displayName"`
	SuffixNo      string `json:"suffixNo"`
}

type PaymentOptionEWallet struct {
	ProviderCode       string `json:"providerCode"`
	AfterSettlementUrl string `json:"afterSettlementUrl,omitempty"`
}

type PaymentOptionToko struct {
	RetailOutletName string `json:"retailOutletName"`
	DisplayName      string `json:"displayName"`
}

type PaymentResponse struct {
	Data PaymentResponseData `json:"data"`
}

type PaymentResponseData struct {
	ID              string                  `json:"id"`
	Type            string                  `json:"type"`
	PaymentDataResp PaymentDataAtributsResp `json:"attributes"`
}

type PaymentDataAtributsResp struct {
	Status            string            `json:"status"`
	Amount            string            `json:"amount"`
	CreatedAt         time.Time         `json:"createdAt"`
	Description       string            `json:"description"`
	ExpiredAt         time.Time         `json:"expiredAt"`
	ReferenceId       string            `json:"referenceId"`
	Fees              string            `json:"fees"`
	PaymentMethodResp PaymentMethodResp `json:"paymentMethod"`
}

type PaymentMethodResp struct {
	ID               string           `json:"id"`
	Type             string           `json:"type"`
	ReferenceId      string           `json:"referenceId"`
	InstructionsResp InstructionsResp `json:"instructions"`
}

type InstructionsResp struct {
	BankShortCode string `json:"bankShortCode"`
	AccountNo     string `json:"accountNo"`
	DisplayName   string `json:"displayName"`
}

type ErrorResponse struct {
	Data []ErrorsData `json:"errors"`
}

type ErrorsData struct {
	Code   string `json:"code"`
	Title  string `json:"title"`
	Detail string `json:"detail"`
}

type DisbursementResponse struct {
	Data DisbursementData `json:"data"`
}

type DisbursementData struct {
	ID         string               `json:"id"`
	Type       string               `json:"type"`
	Attributes DisbursementAtribute `json:"attributes"`
}

type DisbursementAtribute struct {
	ReferenceId        string             `json:"referenceId"`
	Description        string             `json:"description"`
	Amount             string             `json:"amount"`
	Status             string             `json:"status"`
	CreatedAt          string             `json:"createdAt"`
	Fees               string             `json:"fees"`
	DisbursementMethod DisbursementMethod `json:"disbursementMethod"`
}

type DisbursementMethod struct {
	BankAccountHolderName       string      `json:"bankAccountHolderName"`
	BankAccountNo               string      `json:"bankAccountNo"`
	BankName                    string      `json:"bankName"`
	BankReferenceNumber         string      `json:"bankReferenceNumber"`
	BankShortCode               string      `json:"bankShortCode"`
	FailureReason               interface{} `json:"failureReason"`
	ServerBankAccountHolderName string      `json:"serverBankAccountHolderName"`
	Type                        string      `json:"type"`
}
