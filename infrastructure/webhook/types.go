package webhook

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"
)

// WebhookEndpoint represents a registered webhook endpoint
type WebhookEndpoint struct {
	ID              string            // Unique identifier for the webhook
	ClientID        string            // Reference to the client that owns this endpoint
	URL             string            // The URL to send webhooks to
	Headers         map[string]string // Custom headers to include in the request
	Authentication  Authentication    // Authentication configuration
	RetryConfig     RetryConfig       // Retry configuration
	RateLimit       RateLimit         // Rate limiting configuration
	SignatureConfig SignatureConfig   // Signature verification configuration
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

// Authentication represents authentication configuration for a webhook
type Authentication interface {
	Apply(req *http.Request) error
}

// BasicAuth implements basic authentication
type BasicAuth struct {
	Username string
	Password string
}

// BearerAuth implements bearer token authentication
type BearerAuth struct {
	Token string
}

type BasicToken struct {
	Token string
}

// RetryConfig defines retry behavior
type RetryConfig struct {
	MaxAttempts       int
	InitialInterval   time.Duration
	MaxInterval       time.Duration
	BackoffMultiplier float64
}

// RateLimit defines rate limiting configuration
type RateLimit struct {
	RequestsPerSecond float64
	Burst             int
}

// Publisher defines the interface for publishing messages
type Publisher interface {
	Publish(ctx context.Context, topic string, message []byte) error
}

// Subscriber defines the interface for subscribing to messages
type Subscriber interface {
	Subscribe(ctx context.Context, topic string, handler MessageHandler) error
	Unsubscribe(ctx context.Context, topic string) error
}

// MessageHandler is the callback function for handling messages
type MessageHandler func(ctx context.Context, message []byte) error

// HTTPClient defines the interface for making HTTP requests
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// CircuitBreakerConfig defines circuit breaker configuration
type CircuitBreakerConfig struct {
	MaxFailures      int           // Number of failures before opening circuit
	ResetTimeout     time.Duration // How long to wait before attempting reset
	FailureThreshold time.Duration // Time window for counting failures
}

// HTTPClientConfig contains configuration for the HTTP client
type HTTPClientConfig struct {
	Timeout           time.Duration
	MaxIdleConns      int
	IdleConnTimeout   time.Duration
	DisableKeepAlives bool
}

type RateLimiter interface {
	Allow() bool
	Wait(ctx context.Context) error
}

// DefaultHTTPClientConfig returns default HTTP client configuration
func DefaultHTTPClientConfig() HTTPClientConfig {
	return HTTPClientConfig{
		Timeout:           30 * time.Second,
		MaxIdleConns:      100,
		IdleConnTimeout:   90 * time.Second,
		DisableKeepAlives: false,
	}
}

// Client represents a client that can register webhook endpoints
type Client struct {
	ID         string   // Unique identifier for the client
	Name       string   // Client name
	SigningKey string   // Secret key for signing webhooks
	Endpoints  []string // List of endpoint IDs registered by this client
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

// Add these types
type WebhookStatus string

const (
	StatusPending   WebhookStatus = "pending"
	StatusDelivered WebhookStatus = "delivered"
	StatusFailed    WebhookStatus = "failed"
	StatusRetrying  WebhookStatus = "retrying"
)

// WebhookDelivery represents a single webhook delivery attempt
type WebhookDelivery struct {
	ID          string        // Unique identifier for the delivery
	ParentID    string        // Reference to parent delivery for status updates
	EndpointID  string        // Reference to the webhook endpoint
	ClientID    string        // Reference to the client
	Payload     []byte        // The payload being delivered
	Status      WebhookStatus // Current status of the delivery
	StatusCode  int           // HTTP status code from the last attempt
	Error       string        // Error message if failed
	RetryCount  int           // Number of retry attempts made
	NextRetryAt *time.Time    // Time of next retry attempt
	CreatedAt   time.Time     // When the delivery was created
	UpdatedAt   time.Time     // Last update time
}

// DeliveryFilter defines filters for listing deliveries
type DeliveryFilter struct {
	ClientID   string
	EndpointID string
	Status     WebhookStatus
	StartTime  time.Time
	EndTime    time.Time
	Limit      int
	Offset     int
}

// WebhookResponse contains the response details from a webhook delivery
type WebhookResponse struct {
	StatusCode int           // HTTP status code from the response
	Headers    http.Header   // Response headers
	Body       []byte        // Response body
	Duration   time.Duration // Time taken for the request
}

// WebhookDeliveryResponse contains the full response details from a webhook delivery
type WebhookDeliveryResponse struct {
	ID          string
	StatusCode  int
	Headers     http.Header
	Response    *http.Response
	Duration    time.Duration
	Error       string
	Body        []byte
	EndpointID  string
	EndpointURL string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

func (w *WebhookDeliveryResponse) ResponseToJSON() ([]byte, error) {
	if w.Response == nil {
		return nil, nil
	}

	type response struct {
		StatusCode int           `json:"status_code"`
		Headers    http.Header   `json:"headers"`
		Body       string        `json:"body"`
		Duration   time.Duration `json:"duration"`
	}

	resp := response{
		StatusCode: w.StatusCode,
		Headers:    w.Headers,
		Body:       string(w.Body),
		Duration:   w.Duration,
	}

	return json.Marshal(resp)
}

func (w *WebhookDeliveryResponse) IsJSON() bool {
	return strings.Contains(w.Headers.Get("Content-Type"), "application/json")
}

func (w *WebhookDeliveryResponse) StatusCodeMessage() string {
	return http.StatusText(w.StatusCode)
}

func (w *WebhookDeliveryResponse) IsSuccess() bool {
	return w.StatusCode >= 200 && w.StatusCode < 300
}

func (w *WebhookDeliveryResponse) IsFailure() bool {
	return w.StatusCode >= 400
}

func (w *WebhookDeliveryResponse) IsTimeout() bool {
	return w.StatusCode == http.StatusRequestTimeout
}
