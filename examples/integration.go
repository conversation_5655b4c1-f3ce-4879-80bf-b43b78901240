package main

// This file is a runnable example (Category A).
// Build normally: `go run ./examples/integration.go`

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"

	"repo.nusatek.id/nusatek/ottopay"
)

// ExampleDatabaseIntegration shows how to implement the database integration interface
type ExampleDatabaseIntegration struct {
	// In real implementation, this would contain database connections
	// e.g., *sql.DB, *gorm.DB, etc.
}

// ValidateUserCredentials validates user credentials against database
func (db *ExampleDatabaseIntegration) ValidateUserCredentials(ctx context.Context, username, password string) (*ottopay.UserInfo, error) {
	// Example implementation - in real scenario, query your database
	if username == "ottopay" && password == "ac710qf!" {
		return &ottopay.UserInfo{
			ID:       "45c14915-f731-43ac-b25a-2182e13c98f7",
			Username: username,
			IsActive: true,
			Company:  "OTTOPAY",
		}, nil
	}
	return nil, fmt.Errorf("invalid credentials")
}

// GetUserByID retrieves user by ID
func (db *ExampleDatabaseIntegration) GetUserByID(ctx context.Context, userID string) (*ottopay.UserInfo, error) {
	// Example implementation
	return &ottopay.UserInfo{
		ID:       userID,
		Username: "ottopay",
		IsActive: true,
		Company:  "OTTOPAY",
	}, nil
}

// CreateVirtualAccount creates a new virtual account record
func (db *ExampleDatabaseIntegration) CreateVirtualAccount(ctx context.Context, va *ottopay.VirtualAccount) error {
	// Example implementation - save to database
	log.Printf("Creating VA: %+v", va)
	return nil
}

// GetVirtualAccount retrieves virtual account by VA number
func (db *ExampleDatabaseIntegration) GetVirtualAccount(ctx context.Context, vaNumber string) (*ottopay.VirtualAccount, error) {
	// Example implementation - query from database
	return &ottopay.VirtualAccount{
		ID:             "va-123",
		VANumber:       vaNumber,
		CompanyCode:    "71101",
		CustomerNumber: "***********",
		ChannelType:    ottopay.ChannelMandiri,
		CustomerName:   "Customer Name Virtual Account",
		Amount:         150000.00,
		Status:         "ACTIVE",
		CreatedAt:      time.Now().Add(-24 * time.Hour),
		UpdatedAt:      time.Now(),
		ExpiresAt:      time.Now().Add(24 * time.Hour),
	}, nil
}

// UpdateVirtualAccountStatus updates VA status
func (db *ExampleDatabaseIntegration) UpdateVirtualAccountStatus(ctx context.Context, vaNumber, status string) error {
	log.Printf("Updating VA %s status to %s", vaNumber, status)
	return nil
}

// CreateInquiryLog creates inquiry log entry
func (db *ExampleDatabaseIntegration) CreateInquiryLog(ctx context.Context, log *ottopay.InquiryLog) error {
	fmt.Printf("Inquiry Log: %+v\n", log)
	return nil
}

// CreatePaymentLog creates payment log entry
func (db *ExampleDatabaseIntegration) CreatePaymentLog(ctx context.Context, log *ottopay.PaymentLog) error {
	fmt.Printf("Payment Log: %+v\n", log)
	return nil
}

// GetTransactionByRequestID retrieves transaction by request ID
func (db *ExampleDatabaseIntegration) GetTransactionByRequestID(ctx context.Context, requestID string) (*ottopay.Transaction, error) {
	// Return nil if not found (no duplicate)
	return nil, fmt.Errorf("transaction not found")
}

// UpdateTransactionStatus updates transaction status
func (db *ExampleDatabaseIntegration) UpdateTransactionStatus(ctx context.Context, requestID, status string) error {
	log.Printf("Updating transaction %s status to %s", requestID, status)
	return nil
}

// GetCompanyByCode retrieves company by code
func (db *ExampleDatabaseIntegration) GetCompanyByCode(ctx context.Context, companyCode string) (*ottopay.Company, error) {
	return &ottopay.Company{
		ID:          "company-1",
		Code:        companyCode,
		Name:        "Test Company",
		IsActive:    true,
		CallbackURL: "https://example.com/callback",
	}, nil
}

// GetProviderConfig retrieves provider configuration
func (db *ExampleDatabaseIntegration) GetProviderConfig(ctx context.Context, providerID string) (*ottopay.ProviderConfig, error) {
	return &ottopay.ProviderConfig{
		ID:           providerID,
		ProviderName: "OTTOPAY",
		IsActive:     true,
		Config: map[string]string{
			"jwt_secret": "your-jwt-secret",
			"timeout":    "3s",
		},
	}, nil
}

// Example 1: Basic Integration
func ExampleBasicIntegration() {
	// Create configuration
	config := ottopay.NewConfigBuilder().
		WithJWTSecret("your-jwt-secret-key").
		WithTokenDuration(24 * time.Hour).
		WithPort("8080").
		WithAllowedIPs([]string{"************", "*************"}).
		WithIPAuth(true).
		WithRequestTimeout(3 * time.Second).
		Build()

	// Create database integration
	dbIntegration := &ExampleDatabaseIntegration{}

	// Create business handler
	businessHandler := ottopay.NewDefaultBusinessHandler(dbIntegration, config)
	config.BusinessHandler = businessHandler

	// Create OttoPay service
	service := ottopay.NewService().Setup(config)

	// Create Fiber app
	app := ottopay.CreateFiberApp(service, config)

	// Start server
	log.Printf("Starting OttoPay server on port %s", config.Port)
	log.Fatal(app.Listen(":" + config.Port))
}

// Example 2: Custom Business Handler
type CustomBusinessHandler struct {
	db ottopay.DatabaseIntegration
}

func (h *CustomBusinessHandler) ValidateCredentials(ctx context.Context, username, password string) (ottopay.UserData, error) {
	// Custom credential validation logic
	userInfo, err := h.db.ValidateUserCredentials(ctx, username, password)
	if err != nil {
		return ottopay.UserData{}, err
	}

	return ottopay.UserData{
		ID:       userInfo.ID,
		Username: userInfo.Username,
		IsValid:  userInfo.IsActive,
	}, nil
}

func (h *CustomBusinessHandler) HandleInquiry(ctx context.Context, req ottopay.InquiryRequest) (ottopay.InquiryData, error) {
	// Custom inquiry logic
	// You can add your own business rules here

	// Example: Check if customer exists in your system
	vaNumber := fmt.Sprintf("%s%s", "71101", req.CustomerNumber) // Assuming Mandiri prefix
	va, err := h.db.GetVirtualAccount(ctx, vaNumber)
	if err != nil {
		return ottopay.InquiryData{
			IsValid:      false,
			ErrorMessage: "Virtual Account not found",
		}, nil
	}

	// Custom validation rules
	if va.Status != "ACTIVE" {
		return ottopay.InquiryData{
			IsValid:      false,
			ErrorMessage: "Virtual Account is not active",
		}, nil
	}

	return ottopay.InquiryData{
		CustomerName:   va.CustomerName,
		TotalAmount:    va.Amount,
		CurrencyCode:   ottopay.CurrencyIDR,
		SubCompany:     ottopay.DefaultSubCompany,
		AdditionalData: "",
		IsValid:        true,
	}, nil
}

func (h *CustomBusinessHandler) HandlePayment(ctx context.Context, req ottopay.PaymentRequest) (ottopay.PaymentData, error) {
	// Custom payment logic
	// Add your payment processing business rules here

	return ottopay.PaymentData{
		IsSuccess:      true,
		CustomerName:   req.CustomerName,
		TotalAmount:    150000.00,
		PaidAmount:     150000.00,
		CurrencyCode:   req.CurrencyCode,
		AdditionalData: "",
	}, nil
}

func ExampleCustomBusinessHandler() {
	dbIntegration := &ExampleDatabaseIntegration{}
	customHandler := &CustomBusinessHandler{db: dbIntegration}

	config := ottopay.NewConfigBuilder().
		WithJWTSecret("your-jwt-secret-key").
		WithBusinessHandler(customHandler).
		Build()

	service := ottopay.NewService().Setup(config)
	app := ottopay.CreateFiberApp(service, config)

	log.Fatal(app.Listen(":8080"))
}

// Example 3: Integration with Existing Fiber App
func ExampleIntegrateWithExistingApp() {
	// Your existing Fiber app
	app := fiber.New()

	// Add your existing routes
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("Hello, World!")
	})

	// Configure OttoPay
	config := ottopay.LoadConfigFromEnv()
	dbIntegration := &ExampleDatabaseIntegration{}
	businessHandler := ottopay.NewDefaultBusinessHandler(dbIntegration, config)
	config.BusinessHandler = businessHandler

	service := ottopay.NewService().Setup(config)

	// Add OttoPay routes to existing app
	ottopay.SetupRoutes(app, service, config)

	log.Fatal(app.Listen(":8080"))
}

// Example 4: Environment Configuration
func ExampleEnvironmentConfiguration() {
	// Set environment variables:
	// export OTTOPAY_JWT_SECRET="your-secret-key"
	// export OTTOPAY_TOKEN_DURATION="24h"
	// export OTTOPAY_PORT="8080"
	// export OTTOPAY_ALLOWED_IPS="************,*************"
	// export OTTOPAY_ENABLE_IP_AUTH="true"
	// export OTTOPAY_REQUEST_TIMEOUT="3s"

	config := ottopay.LoadConfigFromEnv()

	// Validate configuration
	if err := config.Validate(); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}

	dbIntegration := &ExampleDatabaseIntegration{}
	businessHandler := ottopay.NewDefaultBusinessHandler(dbIntegration, config)
	config.BusinessHandler = businessHandler

	service := ottopay.NewService().Setup(config)
	app := ottopay.CreateFiberApp(service, config)

	log.Fatal(app.Listen(":" + config.Port))
}

// Example 5: Testing Integration
func ExampleTestingIntegration() {
	// Create test configuration
	dbIntegration := &ExampleDatabaseIntegration{}
	config := ottopay.Config{
		JWTSecret:     "test-secret",
		TokenDuration: time.Hour,
		BankPrefixes: map[string]string{
			ottopay.ChannelMandiri: "71101",
		},
	}
	businessHandler := ottopay.NewDefaultBusinessHandler(dbIntegration, config)
	config.BusinessHandler = businessHandler

	service := ottopay.NewService().Setup(config)

	// Test token generation
	ctx := context.Background()
	tokenReq := ottopay.TokenRequest{
		Username: "ottopay",
		Password: "ac710qf!",
	}

	tokenResp, err := service.GenerateToken(ctx, tokenReq)
	if err != nil {
		log.Fatalf("Token generation failed: %v", err)
	}

	fmt.Printf("Token generated: %s\n", tokenResp.Data.IDToken)

	// Test inquiry
	inquiryReq := ottopay.InquiryRequest{
		CompanyCode:    "71101",
		CustomerNumber: "***********",
		RequestID:      "REQ123456789",
		ChannelType:    ottopay.ChannelMandiri,
	}

	inquiryResp, err := service.ProcessInquiry(ctx, inquiryReq)
	if err != nil {
		log.Fatalf("Inquiry failed: %v", err)
	}

	fmt.Printf("Inquiry status: %s\n", inquiryResp.InquiryStatus)
}

// Example 6: Monitoring and Logging
func ExampleMonitoringAndLogging() {
	// Create logger
	logger := ottopay.NewDefaultLogger(ottopay.LogLevelInfo)
	auditLogger := ottopay.NewAuditLogger(logger)
	perfMonitor := ottopay.NewPerformanceMonitor(logger)

	// Create configuration with monitoring
	config := ottopay.LoadConfigFromEnv()
	dbIntegration := &ExampleDatabaseIntegration{}
	businessHandler := ottopay.NewDefaultBusinessHandler(dbIntegration, config)
	config.BusinessHandler = businessHandler

	service := ottopay.NewService().Setup(config)

	// Create Fiber app with monitoring middleware
	app := fiber.New()

	// Add monitoring middleware
	app.Use(func(c *fiber.Ctx) error {
		start := time.Now()
		err := c.Next()
		duration := time.Since(start)

		// Log request
		auditLogger.LogSecurityEvent(
			c.Context(),
			"http_request",
			fmt.Sprintf("%s %s - Duration: %v", c.Method(), c.Path(), duration),
			c.IP(),
			"LOW",
		)

		// Record metrics
		perfMonitor.TrackRequest(c.Context(), c.Path())(c.Response().StatusCode())

		return err
	})

	// Setup OttoPay routes
	ottopay.SetupRoutes(app, service, config)

	log.Fatal(app.Listen(":" + config.Port))
}

func main() {
	fmt.Println("OttoPay Integration Examples")
	fmt.Println("Choose an example to run:")
	fmt.Println("1. Basic Integration")
	fmt.Println("2. Custom Business Handler")
	fmt.Println("3. Integrate with Existing App")
	fmt.Println("4. Environment Configuration")
	fmt.Println("5. Testing Integration")
	fmt.Println("6. Monitoring and Logging")

	// For demonstration, run the basic integration
	ExampleBasicIntegration()
}
