package domain

import (
	"time"

	"gorm.io/gorm"
)

type CashOutTransactionDetails struct {
	ID                      int `gorm:"primaryKey"`
	CashOutTransactionID    int
	CashInTransactionItemID int
	CreatedAt               time.Time
	UpdatedAt               time.Time
	DeletedAt               gorm.DeletedAt `gorm:"index"`
}

type CashOutTransactionDetailComplete struct {
	ID                      int       `gorm:"column:id;<-:false" json:"id"`
	CashOutTransactionID    int       `gorm:"column:cash_out_transaction_id;<-:false" json:"cash_out_transaction_id"`
	CashInTransactionItemID int       `gorm:"column:cash_in_transaction_item_id;<-:false" json:"cash_in_transaction_item_id"`
	CashInTransactionID     int       `gorm:"column:cash_in_transaction_id;<-:false" json:"cash_in_transaction_id"`
	CreatedAt               time.Time `gorm:"column:created_at;<-:false" json:"created_at"`
	UpdatedAt               time.Time `gorm:"column:updated_at;<-:false" json:"updated_at"`

	// PartnerId           int        `gorm:"column:partner_id;<-:false" json:"partner_id"`
	// ItemName            string     `gorm:"column:item_name;<-:false" json:"item_name"`
	Amount float64 `gorm:"column:amount;<-:false" json:"amount"`
	// Status              string     `gorm:"column:status;<-:false" json:"status"`
	// IsCashout           bool       `gorm:"column:is_cashout;<-:false" json:"is_cashout"`
	// SlaDate             *time.Time `gorm:"column:sla_date;<-:false" json:"sla_date"`
}
