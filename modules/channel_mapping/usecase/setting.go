package usecase

import (
	paymentChannel "repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	paymentProviderRepo "repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
)

type defaultChannelMapping struct {
	channelMappingRepo  ChannelMappingRepository
	paymentProviderRepo paymentProviderRepo.PaymentProviderRepository
	paymentChannelRepo  paymentChannel.PaymentChannelRepository
}

func Setup() *defaultChannelMapping {
	return &defaultChannelMapping{}
}

func (s *defaultChannelMapping) SetChannelMappingRepo(t ChannelMappingRepository) *defaultChannelMapping {
	s.channelMappingRepo = t
	return s
}

func (s *defaultChannelMapping) SetPaymentProviderRepo(t paymentProviderRepo.PaymentProviderRepository) *defaultChannelMapping {
	s.paymentProviderRepo = t
	return s
}

func (s *defaultChannelMapping) SetPaymentChannelRepo(t paymentChannel.PaymentChannelRepository) *defaultChannelMapping {
	s.paymentChannelRepo = t
	return s
}

func (s *defaultChannelMapping) Validate() ChannelMappingUsecase {
	if s.channelMappingRepo == nil {
		panic("channel mapping repo is nil")
	}

	if s.paymentProviderRepo == nil {
		panic("payment provider repo is nil")
	}

	if s.paymentChannelRepo == nil {
		panic("payment channel repo is nil")
	}

	return s
}