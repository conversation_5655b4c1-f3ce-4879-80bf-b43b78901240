package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *partnerUseCase) CreateTag(ctx context.Context, req *domain.Tags) (err error) {
	err = s.partnerRepository.CreateTag(ctx, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.setCacheTags(ctx, req)
	return
}

func (s *partnerUseCase) UpdateTag(ctx context.Context, req *domain.Tags, id string) (err error) {
	tag, err := s.partnerRepository.GetTagById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "tag not found")
		logger.Error(ctx, err.Error())
		return
	}

	tags, err := s.partnerRepository.GetPartnerTagByTagId(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := 0; i < len(*tags); i++ {
		_ = s.deleteCachePartnerTags(ctx, strconv.Itoa((*tags)[i].PartnerID))
	}

	req.CreatedAt = tag.CreatedAt
	req.ID = tag.ID
	err = s.partnerRepository.UpdateTag(ctx, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.setCacheTags(ctx, req)
	return
}

func (s *partnerUseCase) GetTagById(ctx context.Context, id string) (resp *domain.Tags, err error) {
	resp, err = s.getCacheTag(ctx, id)
	return
}

func (s *partnerUseCase) GetPartnerTagByPartnerId(ctx context.Context, id string) (resp *[]domain.PartnerTags, err error) {
	resp, err = s.getCachePartnerTag(ctx, id)
	return
}

func (s *partnerUseCase) DeleteTag(ctx context.Context, id string) (err error) {
	tag, err := s.partnerRepository.GetTagById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "tag not found")
		logger.Error(ctx, err.Error())
		return
	}
	tags, err := s.partnerRepository.GetPartnerTagByTagId(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := 0; i < len(*tags); i++ {
		_ = s.deleteCachePartnerTags(ctx, strconv.Itoa((*tags)[i].PartnerID))
	}

	err = s.partnerRepository.DeleteTag(ctx, tag)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = s.deleteCacheTags(ctx, id)
	return
}

func (s *partnerUseCase) GetListAllTag(ctx context.Context) (resp *[]domain.Tags, err error) {
	resp, err = s.partnerRepository.GetListAllTag(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) getCacheTag(ctx context.Context, tagId string) (resp *domain.Tags, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheTags, tagId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.partnerRepository.GetTagById(ctx, tagId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "tags not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheTags(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Tags
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *partnerUseCase) setCacheTags(ctx context.Context, req *domain.Tags) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheTags, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) deleteCacheTags(ctx context.Context, tagsId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheTags, tagsId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) deleteCachePartnerTags(ctx context.Context, partnerId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePartnerTags, partnerId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) setCachePartnerTags(ctx context.Context, partnerId string) (resp *[]domain.PartnerTags, err error) {
	_, errRes := s.partnerRepository.GetPartnerByID(ctx, partnerId)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
		logger.Error(ctx, err.Error())
		return
	}
	prod, errRes := s.partnerRepository.GetPartnerTagByPartnerId(ctx, partnerId)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	prodByte, errRes := json.Marshal(prod)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CachePartnerTags, partnerId)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = prod
	return
}

func (s *partnerUseCase) getCachePartnerTag(ctx context.Context, partnerId string) (resp *[]domain.PartnerTags, err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePartnerTags, partnerId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}
	switch true {
	case cache == "":
		resp, err = s.setCachePartnerTags(ctx, partnerId)

	default:
		var prod []domain.PartnerTags
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}
