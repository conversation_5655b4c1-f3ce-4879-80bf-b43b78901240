package usecase

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/partner/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

// PartnerRepository
type PartnerRepository interface {
	BeginsTrans() *gorm.DB
	List(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (resp *[]entity.PartnerShow, totalItem int64, err error)
	ListHistory(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error)
	View(ctx context.Context, param map[string]interface{}) (resp *entity.PartnerShow, err error)
	ViewHistory(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	GetPartnerByID(ctx context.Context, id string) (resp *domain.Partners, err error)
	DeletePartner(ctx context.Context, req *domain.Partners) (err error)
	CreatePartner(ctx context.Context, req *domain.Partners, tx *gorm.DB) (err error)
	UpdatePartner(ctx context.Context, req *domain.Partners, tx *gorm.DB) (err error)
	PatchStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status bool) (partners []domain.Partners, err error)
	GetByCompanyCode(ctx context.Context, code string) (domain.Companies, bool)
	GetByBatchNumber(ctx context.Context, batchNumber string) (domain.CashOutTransactions, bool)
	GetPartnerByCompanyId(ctx context.Context, companyId string) (resp *[]domain.Partners, err error)
	GetPartnerByCode(ctx context.Context, code string) (resp *domain.Partners, err error)
	UpdatePaymentChannelPartnerInCashOutTrx(ctx context.Context, req *domain.CashOutTransactions, partnerId, status string, tx *gorm.DB) (err error)

	//Partner Tag
	CreateTag(ctx context.Context, req *domain.Tags) (err error)
	UpdateTag(ctx context.Context, req *domain.Tags) (err error)
	GetTagById(ctx context.Context, id string) (resp *domain.Tags, err error)
	DeleteTag(ctx context.Context, req *domain.Tags) (err error)
	CreatePartnerTag(ctx context.Context, req *domain.PartnerTags, tx *gorm.DB) (err error)
	UpdatePartnerTag(ctx context.Context, req *domain.PartnerTags, tx *gorm.DB) (err error)
	GetPartnerTagById(ctx context.Context, id string) (resp *domain.PartnerTags, err error)
	DeletePartnerTag(ctx context.Context, req *domain.PartnerTags) (err error)
	DeletePartnerTagByPartnerId(ctx context.Context, partnerId string, tx *gorm.DB) (err error)
	GetListAllTag(ctx context.Context) (resp *[]domain.Tags, err error)
	GetPartnerTagByPartnerId(ctx context.Context, partnerId string) (resp *[]domain.PartnerTags, err error)
	GetPartnerTagByTagId(ctx context.Context, tagId string) (resp *[]domain.PartnerTags, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.Partners, err error)
}

// PartnerUseCase
type PartnerUseCase interface {
	List(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (resp *[]entity.PartnerShow, totalItem int64, err error)
	ListHistory(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error)
	View(ctx context.Context, param map[string]interface{}) (res *entity.PartnerShow, err error)
	ViewHistory(ctx context.Context, batchNumber string, orderParam map[string]interface{}) ([]interface{}, int64, error)
	DeletePartner(ctx context.Context, id string) (err error)
	CreatePartner(ctx context.Context, partner *domain.Partners, companyCode string, tagId []int) (err error)
	UpdatePartner(ctx context.Context, partner *domain.Partners, companyCode string, tagId []int) (err error)
	PatchStatusBulk(ctx context.Context, bools []int, status bool) (err error)
	GetPartnerByCompanyId(ctx context.Context, companyId string) (resp *[]domain.Partners, err error)
	GetPartnerByCode(ctx context.Context, code string) (resp *domain.Partners, err error)
	CreateTag(ctx context.Context, req *domain.Tags) (err error)
	UpdateTag(ctx context.Context, req *domain.Tags, id string) (err error)
	GetTagById(ctx context.Context, id string) (resp *domain.Tags, err error)
	DeleteTag(ctx context.Context, id string) (err error)
	GetListAllTag(ctx context.Context) (resp *[]domain.Tags, err error)
	GetPartnerTagByPartnerId(ctx context.Context, id string) (resp *[]domain.PartnerTags, err error)
	GetPartnerById(ctx context.Context, id string) (resp *domain.Partners, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.Partners, err error)
	UpdateStatus(ctx context.Context, id string, status bool) error

	// Open API
	CreatePartnerAPI(ctx context.Context, req *domain.Partners, auth string) (err error)
	UpdatePartnerAPI(ctx context.Context, req *domain.Partners, id, auth string) (err error)
	GetListPartnerAPI(ctx context.Context, paginate utils.Pagination, auth string) (resp *[]entity.PartnersResponseAPI, totalData int64, err error)
	GetPartnerCodeAPI(ctx context.Context, auth, code string) (resp *entity.PartnersResponseAPI, err error)
	DeletePartnerApi(ctx context.Context, id, auth string) (err error)
}
