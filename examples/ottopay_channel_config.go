//go:build examples_only
// +build examples_only

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"repo.nusatek.id/nusatek/payment/domain"

	"repo.nusatek.id/nusatek/ottopay"
)

// OttoPayChannelConfig represents the configuration for OttoPay channel mapping
type OttoPayChannelConfig struct {
	ChannelMappings map[string]string `json:"channel_mappings"`
	DefaultMappings map[string]string `json:"default_mappings"`
	FallbackEnabled bool              `json:"fallback_enabled"`
}

// OttoPayConfigManager manages OttoPay channel configuration
type OttoPayConfigManager struct {
	config *OttoPayChannelConfig
}

// NewOttoPayConfigManager creates a new configuration manager
func NewOttoPayConfigManager() *OttoPayConfigManager {
	return &OttoPayConfigManager{}
}

// LoadFromFile loads configuration from JSON file
func (m *OttoPayConfigManager) LoadFromFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config OttoPayChannelConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	m.config = &config
	return nil
}

// LoadFromEnvironment loads configuration from environment variables
func (m *OttoPayConfigManager) LoadFromEnvironment() error {
	config := &OttoPayChannelConfig{
		ChannelMappings: make(map[string]string),
		DefaultMappings: make(map[string]string),
		FallbackEnabled: true,
	}

	// Load channel mappings from environment variables
	// Format: OTTOPAY_CHANNEL_MAPPING_<CODE>=<CHANNEL_TYPE>
	for _, env := range os.Environ() {
		if strings.HasPrefix(env, "OTTOPAY_CHANNEL_MAPPING_") {
			parts := strings.SplitN(env, "=", 2)
			if len(parts) == 2 {
				code := strings.TrimPrefix(parts[0], "OTTOPAY_CHANNEL_MAPPING_")
				channelType := parts[1]
				config.ChannelMappings[code] = channelType
			}
		}
	}

	// Set default mappings if no custom mappings provided
	if len(config.ChannelMappings) == 0 {
		config.ChannelMappings = map[string]string{
			"OTTOPAY_MANDIRI": ottopay.ChannelMandiri,
			"OTTOPAY_BRI":     ottopay.ChannelBRI,
			"OTTOPAY_BINA":    ottopay.ChannelBINA,
			"OTTOPAY_BNI":     ottopay.ChannelBNI,
		}
	}

	// Load fallback setting
	if fallback := os.Getenv("OTTOPAY_FALLBACK_ENABLED"); fallback != "" {
		config.FallbackEnabled = fallback == "true"
	}

	m.config = config
	return nil
}

// LoadFromDatabase loads configuration from database settings
func (m *OttoPayConfigManager) LoadFromDatabase(ctx context.Context, settingsRepo SettingsRepository) error {
	// Get OttoPay channel mappings from database
	settings, err := settingsRepo.GetSettingsByCategory(ctx, "ottopay_channels")
	if err != nil {
		return fmt.Errorf("failed to load OttoPay settings from database: %w", err)
	}

	config := &OttoPayChannelConfig{
		ChannelMappings: make(map[string]string),
		FallbackEnabled: true,
	}

	// Parse settings into channel mappings
	for _, setting := range settings {
		if strings.HasPrefix(setting.Key, "channel_mapping_") {
			code := strings.TrimPrefix(setting.Key, "channel_mapping_")
			config.ChannelMappings[code] = setting.Value
		} else if setting.Key == "fallback_enabled" {
			config.FallbackEnabled = setting.Value == "true"
		}
	}

	m.config = config
	return nil
}

// GetChannelType returns the OttoPay channel type for a given payment channel code
func (m *OttoPayConfigManager) GetChannelType(paymentChannel *domain.PaymentChannels) (string, error) {
	if m.config == nil {
		return "", fmt.Errorf("configuration not loaded")
	}

	// First, try exact code mapping
	if channelType, exists := m.config.ChannelMappings[paymentChannel.Code]; exists {
		return channelType, nil
	}

	// If fallback is enabled, try name-based mapping
	if m.config.FallbackEnabled {
		return m.getChannelTypeFromName(paymentChannel.Name)
	}

	return "", fmt.Errorf("no mapping found for payment channel code: %s", paymentChannel.Code)
}

// getChannelTypeFromName provides fallback mapping based on channel name
func (m *OttoPayConfigManager) getChannelTypeFromName(channelName string) (string, error) {
	channelNameLower := strings.ToLower(channelName)

	switch {
	case strings.Contains(channelNameLower, "mandiri"):
		return ottopay.ChannelMandiri, nil
	case strings.Contains(channelNameLower, "bri"):
		return ottopay.ChannelBRI, nil
	case strings.Contains(channelNameLower, "bina"):
		return ottopay.ChannelBINA, nil
	case strings.Contains(channelNameLower, "bni"):
		return ottopay.ChannelBNI, nil
	default:
		return "", fmt.Errorf("unsupported channel name: %s", channelName)
	}
}

// AddChannelMapping adds a new channel mapping
func (m *OttoPayConfigManager) AddChannelMapping(code, channelType string) {
	if m.config == nil {
		m.config = &OttoPayChannelConfig{
			ChannelMappings: make(map[string]string),
			FallbackEnabled: true,
		}
	}
	m.config.ChannelMappings[code] = channelType
}

// GetAllMappings returns all configured channel mappings
func (m *OttoPayConfigManager) GetAllMappings() map[string]string {
	if m.config == nil {
		return make(map[string]string)
	}
	return m.config.ChannelMappings
}

// SaveToFile saves the current configuration to a JSON file
func (m *OttoPayConfigManager) SaveToFile(filePath string) error {
	if m.config == nil {
		return fmt.Errorf("no configuration to save")
	}

	data, err := json.MarshalIndent(m.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// SettingsRepository interface for database settings
type SettingsRepository interface {
	GetSettingsByCategory(ctx context.Context, category string) ([]Setting, error)
	UpdateSetting(ctx context.Context, key, value string) error
}

// Setting represents a configuration setting
type Setting struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// Example usage in your usecase
func ExampleUsageInUsecase() {
	// Initialize configuration manager
	configManager := NewOttoPayConfigManager()

	// Option 1: Load from file
	err := configManager.LoadFromFile("config/ottopay_channels.json")
	if err != nil {
		// Option 2: Load from environment variables
		err = configManager.LoadFromEnvironment()
		if err != nil {
			// Option 3: Use default configuration
			configManager.AddChannelMapping("OTTOPAY_MANDIRI", ottopay.ChannelMandiri)
			configManager.AddChannelMapping("OTTOPAY_BRI", ottopay.ChannelBRI)
			configManager.AddChannelMapping("OTTOPAY_BINA", ottopay.ChannelBINA)
			configManager.AddChannelMapping("OTTOPAY_BNI", ottopay.ChannelBNI)
		}
	}

	// Use in your usecase
	paymentChannel := &domain.PaymentChannels{
		Code: "CUSTOM_MANDIRI_CODE", // Your actual database code
		Name: "Custom Mandiri Channel",
	}

	channelType, err := configManager.GetChannelType(paymentChannel)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Channel type for %s: %s\n", paymentChannel.Code, channelType)
}

// Updated usecase method using configuration manager
func (u *cashinTransactionUsecase) getOttoPayChannelTypeWithConfig(ctx context.Context, paymentChannel *domain.PaymentChannels) (string, error) {
	// Initialize configuration manager (this should be done once during usecase initialization)
	if u.ottoPayConfigManager == nil {
		u.ottoPayConfigManager = NewOttoPayConfigManager()

		// Load configuration (choose one method based on your preference)
		if err := u.ottoPayConfigManager.LoadFromEnvironment(); err != nil {
			// Fallback to default configuration
			u.ottoPayConfigManager.AddChannelMapping("OTTOPAY_MANDIRI", ottopay.ChannelMandiri)
			u.ottoPayConfigManager.AddChannelMapping("OTTOPAY_BRI", ottopay.ChannelBRI)
			u.ottoPayConfigManager.AddChannelMapping("OTTOPAY_BINA", ottopay.ChannelBINA)
			u.ottoPayConfigManager.AddChannelMapping("OTTOPAY_BNI", ottopay.ChannelBNI)
		}
	}

	return u.ottoPayConfigManager.GetChannelType(paymentChannel)
}

// Add this field to your cashinTransactionUsecase struct
type cashinTransactionUsecaseWithConfig struct {
	// ... existing fields
	ottoPayConfigManager *OttoPayConfigManager
}

func main() {
	fmt.Println("OttoPay Channel Configuration Example")
	ExampleUsageInUsecase()
}
