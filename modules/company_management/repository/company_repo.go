package repository

import (
	"context"
	"encoding/json"
	"errors"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_management/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
)

type defaultCompanyManagement struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.CompanyManagementRepo {
	return &defaultCompanyManagement{db}
}

func (r *defaultCompanyManagement) CreateCompany(ctx context.Context, req *domain.Companies) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultCompanyManagement) UpdateCompany(ctx context.Context, req *domain.Companies) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultCompanyManagement) GetCompanyById(ctx context.Context, id string) (resp *domain.Companies, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultCompanyManagement) DeleteCompany(ctx context.Context, req *domain.Companies) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("company_id = ?", req.ID).Delete(&domain.CompanyPaymentProviderChannelMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("company_id = ?", req.ID).Delete(&domain.CompanyPaymentProviderMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("company_id = ?", req.ID).Delete(&domain.CompanyProducts{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("company_id = ?", req.ID).Delete(&domain.UserCompanies{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("company_id = ?", req.ID).Delete(&domain.CompanyCashFlows{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultCompanyManagement) SearchCompany(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error) {
	query := func(db *gorm.DB) *gorm.DB {
		condision := db
		value := "%" + paginate.Value + "%"
		switch true {
		case paginate.Key == "company_name":
			condision.Where("\"name\" ILIKE ?", value)
		case paginate.Key == "company_code":
			condision.Where("code ILIKE ?", value)
		case paginate.Key == "alias":
			condision.Where("alias ILIKE ?", value)
		case paginate.Key == "status":
			condision.Where("status = ?", value)
		default:
			condision.Where("CONCAT(alias, ' ', \"name\", ' ', code) ILIKE ?", value)
		}
		return condision
	}

	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).
		Scopes(query).Order(paginate.GetOrderBy("")).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Scopes(query).Find(&domain.Companies{}).Count(&totalData).Error
	return
}

func (r *defaultCompanyManagement) CompanyList(ctx context.Context, paginate utils.Pagination) (resp *[]domain.Companies, totalData int64, err error) {
	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).
		Order(paginate.GetOrderBy("")).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Find(&domain.Companies{}).Count(&totalData).Error
	return
}

func (r *defaultCompanyManagement) CompanyListAll(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(&domain.Companies{}).Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultCompanyManagement) CompanyListAllActive(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(&domain.Companies{}).Where("status = true").Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultCompanyManagement) CompanyListAllNotActive(ctx context.Context) (resp *[]domain.Companies, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(&domain.Companies{}).Where("status = false").Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultCompanyManagement) GetCompanyBySecrets(ctx context.Context, secret string) (resp *domain.Companies, err error) {
	err = r.db.WithContext(ctx).Where("secrets = ?", secret).Take(&resp).Error
	return
}

func (r *defaultCompanyManagement) GetCompanyByCode(ctx context.Context, code string) (resp *domain.Companies, err error) {
	err = r.db.WithContext(ctx).Where("code = ?", code).Take(&resp).Error
	return
}

func (r *defaultCompanyManagement) GetCompanyBySecret(ctx context.Context, secret string) (resp *domain.Companies, err error) {
	err = r.db.WithContext(ctx).Where("secrets = ?", secret).Take(&resp).Error
	return
}

func (r *defaultCompanyManagement) GetLastCompany(ctx context.Context) (resp *domain.Companies, err error) {
	err = r.db.WithContext(ctx).Raw("SELECT * FROM companies ORDER BY id DESC LIMIT 1").Scan(&resp).Error
	return
}

func (r *defaultCompanyManagement) SelectByIds(ctx context.Context, ids []int) (resp []domain.Companies, err error) {
	err = r.db.Where("id IN ?", ids).Table("companies").Order("created_at").Scan(&resp).Error

	return resp, err
}

func (r *defaultCompanyManagement) IsActive(ctx context.Context, companyID int64) (resp bool, err error) {
	err = r.db.Where("id = ? AND deleted_at IS NULL", companyID).Table("companies").Select("status").Limit(1).Order("id DESC").Scan(&resp).Error

	return resp, err
}

func (r *defaultCompanyManagement) ChekExistingRelationData(ctx context.Context, companyID int, isCheckStatus bool) (resp bool, err error) {
	var companyParentID int
	db := r.db.Debug()
	qParentCompany := db.WithContext(ctx).Table("companies").Select("id").
		Where("parent_company_id = ? AND deleted_at IS NULL", companyID).Order("id DESC").Limit(1)
	if isCheckStatus {
		qParentCompany = qParentCompany.Where("status IS true")
	}
	err = qParentCompany.Scan(&companyParentID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var companyPaymentProviderChannelMappingID int
	qCompanyPaymentProviderChannelMapping := db.Table("company_payment_provider_channel_mappings").Select("id").
		Where("company_id = ? AND deleted_at IS NULL", companyID).Order("id desc").Limit(1)
	if isCheckStatus {
		qCompanyPaymentProviderChannelMapping = qCompanyPaymentProviderChannelMapping.Where("status IS true")
	}
	err = qCompanyPaymentProviderChannelMapping.Scan(&companyPaymentProviderChannelMappingID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var companyPaymentProviderMappingID int
	qCompanyPaymentProviderMapping := db.Table("company_payment_provider_mappings").Select("id").
		Where("company_id = ? AND deleted_at IS NULL", companyID).Order("id DESC").Limit(1)
	if isCheckStatus {
		qCompanyPaymentProviderMapping = qCompanyPaymentProviderMapping.Where("status IS true")
	}
	err = qCompanyPaymentProviderMapping.Scan(&companyPaymentProviderMappingID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var companyProductID int
	qCompanyProduct := db.Table("company_products").Select("id").
		Where("company_id = ? AND deleted_at IS NULL", companyID).Order("id DESC").Limit(1)
	if isCheckStatus {
		qCompanyProduct = qCompanyProduct.Where("status IS true")
	}
	err = qCompanyProduct.Scan(&companyProductID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var partnerID int
	qPartner := db.Table("partners").Select("id").Where("company_id = ? AND deleted_at IS NULL", companyID).Order("id DESC").Limit(1)
	if isCheckStatus {
		qPartner = qPartner.Where("status IS true")
	}
	err = qPartner.Scan(&partnerID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var userCompanyID int
	qUserCompany := db.Table("user_companies uc").Select("uc.id").Joins("INNER JOIN users u ON u.id = uc.user_id AND u.deleted_at IS NULL").
		Where("uc.company_id = ? AND uc.deleted_at IS NULL", companyID)
	if isCheckStatus {
		qUserCompany = qUserCompany.Where("u.status IS true")
	}
	err = qUserCompany.Scan(&userCompanyID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	resp = companyParentID != 0 || companyPaymentProviderMappingID != 0 || companyPaymentProviderChannelMappingID != 0 || companyProductID != 0 || partnerID != 0 || userCompanyID != 0
	return resp, nil
}

func (r *defaultCompanyManagement) GetWebhookByCompanyIdAndEventType(ctx context.Context, companyId int, eventType string) (*domain.Companies, error) {
	var company domain.Companies

	// Query the database using both companyId and eventType
	err := r.db.WithContext(ctx).
		Where("id = ? AND webhook->>? IS NOT NULL", companyId, eventType).
		First(&company).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("company or event_type not found")
		}
		return nil, err
	}

	return &company, nil
}

func (r *defaultCompanyManagement) UpdateOrCreateWebhook(ctx context.Context, companyId int, webhook *domain.CompanyWebhook) error {
	var company domain.Companies

	err := r.db.WithContext(ctx).First(&company, companyId).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("company not found")
		}
		return err
	}

	var existingWebhook map[string]domain.WebhookData

	if company.Webhooks != nil {
		err = json.Unmarshal(company.Webhooks, &existingWebhook)
		if err != nil {
			return err
		}
	} else {
		existingWebhook = make(map[string]domain.WebhookData)
	}

	existingWebhook[webhook.EventType] = domain.WebhookData{URL: webhook.URL}

	updatedWebhook, err := json.Marshal(existingWebhook)
	if err != nil {
		return err
	}

	return r.db.Model(&company).WithContext(ctx).Update("webhooks", updatedWebhook).Error
}

func (r *defaultCompanyManagement) UpdateStatus(ctx context.Context, id string, status bool) error {
	return r.db.WithContext(ctx).Model(&domain.Companies{}).Where("id = ?", id).Update("status", status).Error
}

