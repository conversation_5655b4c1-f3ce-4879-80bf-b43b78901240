package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils/constants"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

func (s *defaultFile) Upload(ctx context.Context, userID, fileType string, file *multipart.FileHeader) (resp *domain.Files, err error) {
	fileKey, err := s.s3.Upload(fileType+"/"+userID, file)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	err = s.DeleteAllUnassigned(ctx, fileType, userID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	resp = &domain.Files{
		Type:       fileType,
		URL:        fileKey,
		UserUpload: userID,
	}

	err = s.CreateFile(ctx, resp)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	resp.URL, err = s.s3.GetURL(resp.URL)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultFile) DeleteAllUnassigned(ctx context.Context, fileType, userID string) (err error) {
	unassigned, _, err := s.GetFileListAllUnassignedByUser(ctx, fileType, userID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	for _, v := range *unassigned {
		_, err = s.s3.Delete(v.URL)
		if err != nil {
			logger.Error(ctx, err.Error())
			continue
		}

		err = s.DeleteFile(ctx, strconv.Itoa(v.ID))
		if err != nil {
			logger.Error(ctx, err.Error())
		}
	}

	return
}

func (s *defaultFile) CreateFile(ctx context.Context, req *domain.Files) (err error) {
	err = s.fileRepo.CreateFile(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in name field")
		}
		logger.Error(ctx, err.Error())
		return
	}
	s.setCacheFile(ctx, req)
	return
}

func (s *defaultFile) UpdateFile(ctx context.Context, req *domain.Files, id string) (err error) {
	file, err := s.fileRepo.GetFileById(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	req.ID = file.ID
	req.CreatedAt = file.CreatedAt
	err = s.fileRepo.UpdateFile(ctx, req)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in name field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheFile(ctx, id)
	s.setCacheFile(ctx, req)
	return
}

func (s *defaultFile) DeleteFile(ctx context.Context, id string) (err error) {
	file, err := s.fileRepo.GetFileById(ctx, id)
	if err != nil {
		err = errors.SetError(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var req domain.Files
	req.ID = file.ID
	err = s.fileRepo.DeleteFile(ctx, &req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	s.deleteCacheFile(ctx, id)
	return
}

func (s *defaultFile) GetFileListAll(ctx context.Context) (resp *[]domain.Files, totalData int64, err error) {
	resp, totalData, err = s.fileRepo.GetFileListAll(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultFile) GetFileListAllUnassignedByUser(ctx context.Context, types, userUpload string) (resp *[]domain.Files, totalData int64, err error) {
	resp, totalData, err = s.fileRepo.GetFileListAllUnassignedByUser(ctx, types, userUpload)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultFile) GetFileById(ctx context.Context, id string) (resp *domain.Files, err error) {
	resp, err = s.getCacheFile(ctx, id)
	return
}

func (s *defaultFile) getCacheFile(ctx context.Context, channelId string) (resp *domain.Files, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheFile, channelId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	switch true {
	case cache == "":
		prod, errRes := s.fileRepo.GetFileById(ctx, channelId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "file not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheFile(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Files
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultFile) setCacheFile(ctx context.Context, req *domain.Files) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheFile, req.ID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultFile) deleteCacheFile(ctx context.Context, channelId string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheFile, channelId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}
