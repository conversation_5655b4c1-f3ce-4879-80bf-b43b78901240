package entity

import (
	"fmt"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
)

type WebhookStatus string

const (
	WebhookStatusPending   WebhookStatus = "pending"
	WebhookStatusDelivered WebhookStatus = "delivered"
	WebhookStatusFailed    WebhookStatus = "failed"
	WebhookStatusRetrying  WebhookStatus = "retrying"
)

type WebhookDeliveryLog struct {
	Id            int64                `json:"id" gorm:"primaryKey"`
	ParentId      *int64               `json:"parent_id" gorm:"index"`
	Parent        *WebhookDeliveryLog  `json:"parent,omitempty" gorm:"foreignKey:ParentId"`
	Children      []WebhookDeliveryLog `json:"children,omitempty" gorm:"foreignKey:ParentId"`
	CompanyId     int64                `json:"company_id"`
	EventType     string               `json:"event_type"`
	Company       domain.Companies     `json:"company" gorm:"foreignKey:CompanyId;references:ID"`
	InvoiceNumber string               `json:"invoice_number"`
	Endpoint      string               `json:"endpoint"`
	Payload       string               `json:"payload" gorm:"type:text"`
	Attempt       int                  `json:"attempt"`
	Status        WebhookStatus        `json:"status"`
	ResponseCode  *int                 `json:"response_code"`
	ResponseBody  string               `json:"response_body" gorm:"type:text"`
	ErrorMessage  string               `json:"error_message"`
	CreatedAt     time.Time            `json:"created_at"`
	UpdatedAt     time.Time            `json:"updated_at"`
}

func (w *WebhookDeliveryLog) TableName() string {
	return "webhook_delivery_logs"
}

// ToResponse converts WebhookDeliveryLog to WebhookDeliveryRespose
func (w *WebhookDeliveryLog) ToResponse() *WebhookDeliveryRespose {
	var responseCode int
	if w.ResponseCode != nil {
		responseCode = *w.ResponseCode
	}

	totalRetries := 0
	if w.Children != nil {
		totalRetries = len(w.Children)
	}

	return &WebhookDeliveryRespose{
		ID:            int(w.Id),
		CompanyId:     int(w.CompanyId),
		CompanyName:   w.Company.Name,
		EventType:     w.EventType,
		InvoiceNumber: w.InvoiceNumber,
		Endpoint:      w.Endpoint,
		Status:        string(w.Status),
		TotalRetries:  totalRetries,
		ResponseCode:  responseCode,
		ResponseBody:  w.ResponseBody,
		Payload:       w.Payload,
		ErrorMessage:  w.ErrorMessage,
		CreatedAt:     w.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     w.UpdatedAt.Format(time.RFC3339),
	}
}

type WebhookLogPaginate struct {
	*utils.Pagination
	Status        string `query:"status" json:"status"`
	EventType     string `query:"event_type" json:"event_type"`
	InvoiceNumber string `query:"invoice_number" json:"invoice_number"`
	Endpoint      string `query:"endpoint" json:"endpoint"`
	Company       int64  `query:"company" json:"company"`
	StartDate     string `query:"start_date" json:"start_date"`
	EndDate       string `query:"end_date" json:"end_date"`
}

func (p *WebhookLogPaginate) DateFilter() (string, string) {
	parseDate := func(dateStr string) (*time.Time, error) {
		if dateStr == "" {
			return nil, nil
		}
		t, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			return nil, err
		}
		return &t, nil
	}

	start, startErr := parseDate(p.StartDate)
	end, endErr := parseDate(p.EndDate)

	if startErr != nil || endErr != nil {
		return "", ""
	}

	if start == nil && end == nil {
		return "", ""
	}

	now := time.Now()
	oneMonthAgo := now.AddDate(0, -1, 0)

	if start == nil {
		start = &oneMonthAgo
	}
	if end == nil {
		end = &now
	}

	return fmt.Sprintf("%s 00:00:00", start.Format("2006-01-02")), fmt.Sprintf("%s 23:59:59", end.Format("2006-01-02"))
}

type WebhookDeliveryLogHistoryPaginate struct {
	*utils.Pagination
	Status    string `query:"status" json:"status"`
	StartDate string `query:"start_date" json:"start_date"`
	EndDate   string `query:"end_date" json:"end_date"`
}

func (p *WebhookDeliveryLogHistoryPaginate) DateFilter() (string, string) {
	parseDate := func(dateStr string) (*time.Time, error) {
		if dateStr == "" {
			return nil, nil
		}
		t, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			return nil, err
		}
		return &t, nil
	}

	start, startErr := parseDate(p.StartDate)
	end, endErr := parseDate(p.EndDate)

	if startErr != nil || endErr != nil {
		return "", ""
	}

	if start == nil && end == nil {
		return "", ""
	}

	now := time.Now()
	oneMonthAgo := now.AddDate(0, -1, 0)

	if start == nil {
		start = &oneMonthAgo
	}
	if end == nil {
		end = &now
	}

	return fmt.Sprintf("%s 00:00:00", start.Format("2006-01-02")), fmt.Sprintf("%s 23:59:59", end.Format("2006-01-02"))
}

type WebhookDeliveryRespose struct {
	ID            int    `json:"id"`
	CompanyId     int    `json:"company_id"`
	CompanyName   string `json:"company_name"`
	EventType     string `json:"event_type"`
	InvoiceNumber string `json:"invoice_number"`
	Endpoint      string `json:"endpoint"`
	Status        string `json:"status"`
	TotalRetries  int    `json:"total_retries"`
	ResponseCode  int    `json:"response_code"`
	ResponseBody  string `json:"response_body,omitempty"`
	Payload       string `json:"payload,omitempty"`
	ErrorMessage  string `json:"error_message"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}
