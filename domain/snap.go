package domain

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/go-playground/validator"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

type SnapClient struct {
	Id        string `gorm:"column:id"`         // client_id generated by payment system
	Name      string `gorm:"column:name"`       // client_name generated by payment system
	Secret    string `gorm:"column:secret"`     // client_secret generated by payment system
	PublicKey string `gorm:"column:public_key"` // public key from client
}

func (sc SnapClient) Table() string {
	return "snap_clients"
}

type ISnapBaseResp interface {
	GetBaseResp() SnapBaseResp
}

type SnapBaseResp struct {
	HttpCode        int           `json:"-"`
	ServiceCode     string        `json:"-"`
	Code            string        `json:"-"`
	ResponseCode    string        `json:"responseCode"` //response code = HTTP status code + service code + case code
	ResponseMessage string        `json:"responseMessage"`
	Data            ISnapBaseResp `json:"-"`
}

func (br SnapBaseResp) GetBaseResp() SnapBaseResp {

	return br
}

func (br SnapBaseResp) GetData() ISnapBaseResp {

	if br.Data != nil {
		return br.Data
	}

	return br
}

func (br *SnapBaseResp) Set(httpCode int, code string, message string) SnapBaseResp {
	br.HttpCode = httpCode
	br.Code = code
	br.ResponseCode = fmt.Sprintf("%d%s%s", httpCode, br.ServiceCode, code)
	br.ResponseMessage = message

	return *br
}

func (br SnapBaseResp) Error() string {
	return br.ResponseMessage
}

type SnapHeaderReq struct {
	Authorization string           `reqHeader:"Authorization"`
	ContentType   string           `reqHeader:"Content-Type"`
	ChannelId     string           `reqHeader:"CHANNEL-ID"`
	XPartnerId    string           `reqHeader:"X-PARTNER-ID"`
	XClientKey    string           `reqHeader:"X-CLIENT-KEY"` // client id
	XTimestamp    SnapStrTimestamp `reqHeader:"X-TIMESTAMP"`
	XExternalId   string           `reqHeader:"X-EXTERNAL-ID"`
	XSignature    string           `reqHeader:"X-SIGNATURE"`
}

type SnapStrTimestamp string

func (t SnapStrTimestamp) String() string {

	return string(t)
}

func (t SnapStrTimestamp) Time() time.Time {
	xtime, _ := time.Parse(time.RFC3339, string(t))

	return xtime
}

func (t SnapStrTimestamp) Valid() bool {
	reqTime, err := time.Parse(time.RFC3339, string(t))
	if err != nil {
		return false
	}

	if reqTime.Add(10 * time.Minute).Before(time.Now()) { // valid timestamp 10 minutes
		return false
	}

	if reqTime.After(time.Now()) {
		return false
	}

	return true
}

func (t SnapStrTimestamp) ValidFormat() bool {
	_, err := time.Parse(time.RFC3339, string(t))

	return err == nil
}

func DefaultSnapAmount(amount float64) SnapAmount {
	return SnapAmount{
		Value:    fmt.Sprintf("%.2f", amount),
		Currency: "IDR",
	}
}

type SnapAmount struct {
	Value    string `json:"value"`
	Currency string `json:"currency"`
}

func (sa SnapAmount) Float64() float64 {
	f, _ := strconv.ParseFloat(sa.Value, 64)

	return f
}

type SnapLanguage struct {
	English   string `json:"english"`
	Indonesia string `json:"indonesia"`
}

type SnapAccesTokenB2BReq struct {
	GrantType string `json:"grantType"`
}

type SnapAccesTokenB2BResp struct {
	SnapBaseResp
	AccessToken string `json:"accessToken"`
	TokenType   string `json:"tokenType"`
	ExpiresIn   string `json:"expiresIn"`
}

func ValidateStructSnap(ctx context.Context, req interface{}, br *SnapBaseResp) error {

	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			fmt.Println("err validator.InvalidValidationError", err)
			return err
		}

		var message string
		for _, err := range err.(validator.ValidationErrors) {
			message = fmt.Sprintf("Invalid mandatory field [%v]", err.Field())
		}
		return br.Set(http.StatusBadRequest, "02", message)
	}
	return err
}

type SnapTransferVaInquiryReq struct {
	PartnerServiceId      string           `json:"partnerServiceId" validate:"required"`
	CustomerNo            string           `json:"customerNo" validate:"required"`
	VirtualAccountNo      string           `json:"virtualAccountNo" validate:"required"`
	TrxDateInit           SnapStrTimestamp `json:"trxDateInit"`
	ChannelCode           int              `json:"channelCode"`
	Language              string           `json:"language"`
	Amount                *SnapAmount      `json:"amount"`
	HashedSourceAccountNo string           `json:"hashedSourceAccountNo"`
	SourceBankCode        string           `json:"sourceBankCode"`
	AdditionalInfo        struct {
		Value string `json:"value"`
	} `json:"additionalInfo"`
	PassApp          string `json:"passApp"`
	InquiryRequestId string `json:"inquiryRequestId" validate:"required"`
}

func (r SnapTransferVaInquiryReq) Validate(ctx context.Context, resp *SnapTransferVaInquiryResp) (err error) {
	err = ValidateStructSnap(ctx, r, &resp.SnapBaseResp)
	if err != nil {
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = SnapLanguage{
			English:   "Invalid Mandatory field",
			Indonesia: "Ada field mandatory tidak diisi",
		}
		logger.Error(ctx, fmt.Sprintf("validate req %v", err))
		return
	}

	if !r.TrxDateInit.ValidFormat() {
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = SnapLanguage{
			English:   "Invalid Field Format",
			Indonesia: "Ada field tidak sesuai format ",
		}
		err = resp.Set(http.StatusBadRequest, "01", "Invalid Field Format [TrxDateInit]")
		return
	}

	return nil
}

func (r SnapTransferVaInquiryReq) ToResp(br *SnapBaseResp) SnapTransferVaInquiryResp {

	return SnapTransferVaInquiryResp{
		SnapBaseResp: *br,
		VirtualAccountData: &SnapTransferVaInquiryVaDataResp{
			InquiryRequestId: r.InquiryRequestId,
			SnapVirtualAccount: SnapVirtualAccount{
				PartnerServiceId: r.PartnerServiceId,
				CustomerNo:       r.CustomerNo,
				VirtualAccountNo: r.VirtualAccountNo,
				BillDetails:      []SnapVADataBillDetail{}, // make not null
				FreeTexts:        []SnapVADataFreeText{},   // make not null
			},
		},
	}
}

type SnapTransferVaInquiryResp struct {
	SnapBaseResp
	VirtualAccountData *SnapTransferVaInquiryVaDataResp `json:"virtualAccountData,omitempty"`
}

// safe get va since it is pointer in case pointer is null
func (r SnapTransferVaInquiryResp) VA() *SnapTransferVaInquiryVaDataResp {

	if r.VirtualAccountData != nil {
		return r.VirtualAccountData
	}

	return &SnapTransferVaInquiryVaDataResp{}
}

type SnapTransferVaInquiryVaDataResp struct {
	InquiryRequestId string       `json:"inquiryRequestId"`
	InquiryStatus    string       `json:"inquiryStatus"`
	InquiryReason    SnapLanguage `json:"inquiryReason"`
	SnapVirtualAccount
	AdditionalInfo struct{} `json:"additionalInfo"`
}

type SnapVirtualAccount struct {
	PartnerServiceId      string                 `json:"partnerServiceId"`
	CustomerNo            string                 `json:"customerNo"`
	VirtualAccountNo      string                 `json:"virtualAccountNo"`
	VirtualAccountName    string                 `json:"virtualAccountName"`
	VirtualAccountEmail   string                 `json:"virtualAccountEmail"`
	VirtualAccountPhone   string                 `json:"virtualAccountPhone"`
	TotalAmount           SnapAmount             `json:"totalAmount"`
	SubCompany            string                 `json:"subCompany,omitempty"`
	BillDetails           []SnapVADataBillDetail `json:"billDetails"`
	FreeTexts             []SnapVADataFreeText   `json:"freeTexts"`
	VirtualAccountTrxType string                 `json:"virtualAccountTrxType,omitempty"`
	FeeAmount             SnapAmount             `json:"feeAmount"`
}

// build virtual accoutn data from fixed va cust and cash in
func (s *SnapVirtualAccount) InjectCashIn(cashIn CashInTransactions) {

	s.VirtualAccountName = cashIn.CustomerName
	s.VirtualAccountEmail = cashIn.CustomerEmail
	s.VirtualAccountPhone = cashIn.CustomerPhone
	s.TotalAmount = DefaultSnapAmount(cashIn.Total + cashIn.ProductFee)
}

type SnapVADataBillDetail struct {
	BillCode        string       `json:"billCode"`
	BillNo          string       `json:"billNo"`
	BillName        string       `json:"billName"`
	BillShortName   string       `json:"billShortName"`
	BillDescription SnapLanguage `json:"billDescription"`
	BillSubCompany  string       `json:"billSubCompany"`
	BillAmount      SnapAmount   `json:"billAmount"`
	BillAmountLabel string       `json:"billAmountLabel"`
	BillAmountValue string       `json:"billAmountValue"`
	AdditionalInfo  struct {
	} `json:"additionalInfo"`
}

type SnapVADataFreeText struct {
	English   string `json:"english"`
	Indonesia string `json:"indonesia"`
}

type SnapTransferVaPaymentReq struct {
	PartnerServiceId        string                 `json:"partnerServiceId" validate:"required"`
	CustomerNo              string                 `json:"customerNo" validate:"required"`
	VirtualAccountNo        string                 `json:"virtualAccountNo" validate:"required"`
	VirtualAccountName      string                 `json:"virtualAccountName"`
	VirtualAccountEmail     string                 `json:"virtualAccountEmail"`
	VirtualAccountPhone     string                 `json:"virtualAccountPhone"`
	TrxID                   string                 `json:"trxId"`
	PaymentRequestId        string                 `json:"paymentRequestId" validate:"required"`
	ChannelCode             int                    `json:"channelCode"`
	HashedSourceAccountNo   string                 `json:"hashedSourceAccountNo"`
	SourceBankCode          string                 `json:"sourceBankCode"`
	PaidAmount              SnapAmount             `json:"paidAmount"`
	CumulativePaymentAmount *SnapAmount            `json:"cumulativePaymentAmount"`
	PaidBills               string                 `json:"paidBills"`
	TotalAmount             SnapAmount             `json:"totalAmount"`
	TrxDateTime             SnapStrTimestamp       `json:"trxDateTime"`
	ReferenceNo             string                 `json:"referenceNo"`
	JournalNum              string                 `json:"journalNum"`
	PaymentType             string                 `json:"paymentType"`
	FlagAdvise              string                 `json:"flagAdvise"`
	SubCompany              string                 `json:"subCompany"`
	BillDetails             []SnapVADataBillDetail `json:"billDetails"`
	FreeTexts               []interface{}          `json:"freeTexts"`
	AdditionalInfo          struct {
		Value string `json:"value"`
	} `json:"additionalInfo"`
}

func (r SnapTransferVaPaymentReq) Validate(ctx context.Context, resp *SnapTransferVaPaymentResp) (err error) {
	err = ValidateStructSnap(ctx, r, &resp.SnapBaseResp)
	if err != nil {
		resp.VA().PaymentFlagStatus = "01"
		resp.VA().PaymentFlagReason = SnapLanguage{
			English:   "Invalid Mandatory field",
			Indonesia: "Ada field mandatory tidak diisi",
		}
		logger.Error(ctx, fmt.Sprintf("validate req %v", err))
		return
	}

	if !r.TrxDateTime.ValidFormat() {
		resp.VA().PaymentFlagStatus = "01"
		resp.VA().PaymentFlagReason = SnapLanguage{
			English:   "Invalid Field Format",
			Indonesia: "Ada field tidak sesuai format ",
		}
		err = resp.Set(http.StatusBadRequest, "01", "Invalid Field Format [TrxDateInit]")
		return
	}

	return nil
}

func (r SnapTransferVaPaymentReq) ToResp(br *SnapBaseResp) SnapTransferVaPaymentResp {

	return SnapTransferVaPaymentResp{
		SnapBaseResp: *br,
		VirtualAccountData: &SnapTransferVaPaymentVaDataResp{
			PaymentRequestId: r.PaymentRequestId,
			SnapVirtualAccount: SnapVirtualAccount{
				PartnerServiceId: r.PartnerServiceId,
				CustomerNo:       r.CustomerNo,
				VirtualAccountNo: r.VirtualAccountNo,
				BillDetails:      []SnapVADataBillDetail{}, // make not null
				FreeTexts:        []SnapVADataFreeText{},   // make not null
			},
			PaidAmount:  r.PaidAmount,
			TrxDateTime: r.TrxDateTime,
			ReferenceNo: r.ReferenceNo,
		},
	}
}

type SnapTransferVaPaymentResp struct {
	SnapBaseResp
	VirtualAccountData *SnapTransferVaPaymentVaDataResp `json:"virtualAccountData,omitempty"`
	AdditionalInfo     struct{}                         `json:"additionalInfo"`
}

// safe get va since it is pointer in case pointer is null
func (r SnapTransferVaPaymentResp) VA() *SnapTransferVaPaymentVaDataResp {

	if r.VirtualAccountData != nil {
		return r.VirtualAccountData
	}

	return &SnapTransferVaPaymentVaDataResp{}
}

type SnapTransferVaPaymentVaDataResp struct {
	PaymentRequestId  string           `json:"paymentRequestId"`
	PaymentFlagStatus string           `json:"paymentFlagStatus"`
	PaymentFlagReason SnapLanguage     `json:"paymentFlagReason"`
	PaidAmount        SnapAmount       `json:"paidAmount"`
	TrxDateTime       SnapStrTimestamp `json:"trxDateTime"`
	ReferenceNo       string           `json:"referenceNo"`
	SnapVirtualAccount
}

type SnapVaRequestId struct {
	Id                  int       `gorm:"column:id;<-:false"`
	ClientId            string    `gorm:"column:client_id"`
	RequestId           string    `gorm:"column:request_id"`
	VaNumber            string    `gorm:"column:va_number"`
	CashInTransactionId int       `gorm:"column:cash_in_transaction_id"`
	CreatedAt           time.Time `gorm:"column:created_at;<-:false"`
}

func (SnapVaRequestId) TableName() string {
	return "snap_va_request_ids"
}

type SnapGenerateSignatureAuthReq struct {
	Timestamp  SnapStrTimestamp
	ClientId   string
	PrivateKey str.Base64str
}
type SnapGenerateSignatureAuthResp struct {
	SnapBaseResp
	Signature string `json:"signature"`
}

type SnapGenerateSignatureServiceReq struct {
	Timestamp    SnapStrTimestamp
	ClientSecret string
	HttpMethod   string
	EndpoinUrl   string
	AccessToken  string
	ReqBody      []byte
}
type SnapGenerateSignatureServiceResp struct {
	SnapBaseResp
	Signature string `json:"signature"`
}
