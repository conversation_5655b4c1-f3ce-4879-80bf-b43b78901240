package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	mg "github.com/mailgun/mailgun-go/v4"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	emailclient "repo.nusatek.id/nusatek/payment/infrastructure/email_client"
	"repo.nusatek.id/nusatek/payment/infrastructure/email_client/email"
	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/modules/configuration/repository"
)

type EmailLogUsecase interface {
	GetEmailLogs(ctx context.Context, payload *entity.EmailLogPaginate) ([]entity.EmailLog, int64, error)
	GetEmailLogDetail(ctx context.Context, id int64, param map[string]interface{}, orderParam map[string]interface{}) ([]entity.EmailLog, error)
	CreateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error)
	UpdateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error)
	DeleteEmailLog(ctx context.Context, id int64) error
	Webhook(ctx context.Context, payload mg.WebhookPayload) (*entity.EmailLog, error)
	ResendEmail(ctx context.Context, id int64) error
}

type emailLogUsecase struct {
	repo        repository.EmailLogRepository
	email       email.EmailProvider
	emailClient emailclient.IEmailClient
}

func NewEmailLogUsecase(repo repository.EmailLogRepository, email email.EmailProvider, emailClient emailclient.IEmailClient) EmailLogUsecase {
	return &emailLogUsecase{repo: repo, email: email, emailClient: emailClient}
}

type parameters struct {
	CompanyId   int64  `json:"company_id"`
	PartnerId   int64  `json:"partner_id"`
	BatchNumber string `json:"batch_number"`
}

func (uc *emailLogUsecase) Webhook(ctx context.Context, payload mg.WebhookPayload) (*entity.EmailLog, error) {
	resp, err := uc.email.Webhook(ctx, payload)
	if err != nil {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: %v", err)
	}

	partner, err := uc.repo.IsExistPartnerByEmail(ctx, resp.Email)
	if err != nil {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: %v", err)
	}

	if !partner {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: Partner email %s not registered", resp.Email)
	}

	var params parameters
	if resp.Parameters != nil {
		err = json.Unmarshal(resp.Parameters, &params)
		if err != nil {
			return nil, fmt.Errorf("[WEBHOOK FAILED]: %v", err)
		}
	}

	exists, err := uc.repo.GetEmailLogStatusExists(ctx, &entity.EmailLog{
		CompanyId:   params.CompanyId,
		PartnerId:   params.PartnerId,
		BatchNumber: params.BatchNumber,
		Email:       resp.Email,
	}, string(resp.Status))

	if err != nil {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: %v", err)
	}

	if exists {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: Email log already exists")
	}

	res, err := uc.repo.CreateEmailLog(ctx, &entity.EmailLog{
		Email:        resp.Email,
		CompanyId:    params.CompanyId,
		Status:       entity.EmailStatus(resp.Status),
		PartnerId:    params.PartnerId,
		BatchNumber:  params.BatchNumber,
		SendDate:     time.Now(),
		ErrorMessage: resp.ErrorMsg,
		CreatedAt:    time.Now(),
	})
	if err != nil {
		return nil, fmt.Errorf("[WEBHOOK FAILED]: %v", err)
	}

	return res, nil
}

func (uc *emailLogUsecase) GetEmailLogs(ctx context.Context, payload *entity.EmailLogPaginate) ([]entity.EmailLog, int64, error) {
	emailLogs, totalData, err := uc.repo.GetEmailLogs(ctx, payload)
	if err != nil {
		logger.Error(ctx, "Failed to get email logs", logger.Err(err))
		return nil, 0, errors.SetErrorMessage(500, "Failed to get email logs")
	}
	return emailLogs, totalData, nil
}

func (uc *emailLogUsecase) GetEmailLogDetail(ctx context.Context, id int64, param map[string]interface{}, orderParam map[string]interface{}) ([]entity.EmailLog, error) {
	emailLog, err := uc.repo.GetEmailLogDetail(ctx, id, param, orderParam)
	if err != nil {
		logger.Error(ctx, "Failed to get email log detail", logger.Err(err))
		return nil, errors.SetErrorMessage(404, "Email log not found")
	}
	return emailLog, nil
}

func (uc *emailLogUsecase) CreateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error) {
	createdLog, err := uc.repo.CreateEmailLog(ctx, emailLog)
	if err != nil {
		logger.Error(ctx, "Failed to create email log", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to create email log")
	}
	return createdLog, nil
}

func (uc *emailLogUsecase) UpdateEmailLog(ctx context.Context, emailLog *entity.EmailLog) (*entity.EmailLog, error) {
	updatedLog, err := uc.repo.UpdateEmailLog(ctx, emailLog)
	if err != nil {
		logger.Error(ctx, "Failed to update email log", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to update email log")
	}
	return updatedLog, nil
}

func (uc *emailLogUsecase) DeleteEmailLog(ctx context.Context, id int64) error {
	err := uc.repo.DeleteEmailLog(ctx, id)
	if err != nil {
		logger.Error(ctx, "Failed to delete email log", logger.Err(err))
		return errors.SetErrorMessage(500, "Failed to delete email log")
	}
	return nil
}

func (uc *emailLogUsecase) ResendEmail(ctx context.Context, id int64) error {
	emailLog, err := uc.repo.GetEmailLogById(ctx, id)
	if err != nil {
		return fmt.Errorf("[RESEND EMAIL FAILED]: %v", err)
	}

	// get cashout email parrent
	cashoutLog, err := uc.repo.GetCashoutEmailLogById(ctx, *emailLog.CashoutEmailLogId)
	if err != nil {
		return fmt.Errorf("[RESEND EMAIL FAILED]: %v", err)
	}

	// build the email
	emailClientMsg := emailclient.SendMsgReq{}

	err = json.Unmarshal([]byte(cashoutLog.MessageBody), &emailClientMsg)
	if err != nil {
		return fmt.Errorf("[RESEND EMAIL FAILED]: %v", err)
	}

	if err := uc.emailClient.SendMsg(ctx, emailClientMsg); err != nil {
		return fmt.Errorf("[RESEND EMAIL FAILED]: %v", err)
	}

	return nil
}
