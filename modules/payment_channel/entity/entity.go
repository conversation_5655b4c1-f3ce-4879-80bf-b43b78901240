package entity

import "time"

type PaymentChannelShow struct {
	ID                     int       `gorm:"primaryKey" json:"id"`
	Name                   string    `json:"name"`
	Status                 bool      `json:"status"`
	PaymentChannelTypeId   int       `json:"payment_channel_type_id"`
	PaymentChannelTypeName string    `json:"payment_channel_type_name"`
	LogoID                 int       `json:"logo_id"`
	LogoURL                string    `json:"logo_url"`
	CreatedAt              time.Time `json:"created_at"`
}
