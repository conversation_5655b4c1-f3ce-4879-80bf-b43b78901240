package entity

import (
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

// Command Transaction Cashin Process
const (
	CommandCreateTransaction = "CREATE_TRX"
	CommandUpdateTransaction = "UPDATE_TRX"
)

type CashinList struct {
	ID                                 int     `json:"id"`
	CompanyName                        string  `json:"company_name"`
	ProductName                        string  `json:"product_name"`
	InvoiceNumber                      string  `json:"invoice_number"`
	CustomerName                       string  `json:"customer_name"`
	PaymentProvider                    string  `json:"payment_provider"`
	PaymentChannel                     string  `json:"payment_channel"`
	AdminFee                           float64 `json:"admin_fee"`
	PgDeliveryFee                      float64 `json:"delivery_fee"`
	CreateDate                         string  `json:"create_date"`
	SettlementDate                     string  `json:"settlement_date"`
	PaymentStatus                      string  `json:"payment_status"`
	Amount                             float64 `json:"amount"`
	ProductFee                         float64 `json:"product_fee"`
	CompanyProductFee                  float64 `json:"company_product_fee"`
	GrandTotal                         float64 `json:"grand_total" gorm:"-"`
	CompanyProductId                   int     `json:"-"`
	CompanyId                          int     `json:"-"`
	Voucher                            float64 `json:"voucher" gorm:"column:voucher"`
	CompanyProductCashoutFeeFixValue   float64 `json:"company_product_cashout_fee_fix_value" gorm:"column:company_product_cashout_fee_fix_value"`
	CompanyProductCashoutFeePercentage float64 `json:"company_product_cashout_fee_percentage" gorm:"column:company_product_cashout_fee_percentage"`
	CompanyProductCashoutFee           float64 `json:"company_product_cashout_fee"` // cashout Product Fee = Product fee (Rp) + (Product Fee (%) * total sub item)
	LogoURL                            string  `json:"logo_url"`
}

type CashinRequest struct {
	CompanyProductCode string                      `json:"company_product_code"`
	InvoiceNumber      string                      `json:"invoice_number" validate:"min=6"`
	CustomerEmail      string                      `json:"customer_email" validate:"email"`
	CustomerName       string                      `json:"customer_name"`
	CustomerPhone      string                      `json:"customer_phone" validate:"number"`
	Discount           float64                     `json:"discount"`
	Total              float64                     `json:"total" validate:"required"`
	PaymentMethodCode  string                      `json:"payment_method_code"`
	Voucher            float64                     `json:"voucher"`
	Description        string                      `json:"description"`
	SuccessRedirectUrl string                      `json:"success_redirect_url"`
	FailureRedirectUrl string                      `json:"failure_redirect_url"`
	MobileNumber       string                      `json:"mobile_number"`
	Items              []TransactionItems          `json:"items"`
	AdditionalInfo     domain.CashInAdditionalInfo `json:"additional_info"`
}

type CashinRefundRequest struct {
	InvoiceNumber string `json:"invoice_number" validate:"required"`
}

type CashinBulkRequest struct {
	Data []struct {
		ID int `json:"id" validate:"required"`
	} `json:"data" validate:"required"`
}

func (e *CashinRequest) ConverToCashinTranscation(companyId, productId, providerId, channelId int, items []domain.CashInTransactionItems) domain.CashInTransactions {
	return domain.CashInTransactions{
		CompanyID:              companyId,
		CompanyProductID:       productId,
		InvoiceNumber:          e.InvoiceNumber,
		CustomerName:           e.CustomerName,
		CustomerPhone:          e.CustomerPhone,
		CustomerEmail:          e.CustomerEmail,
		PaymentProviderID:      providerId,
		PaymentChannelID:       channelId,
		Total:                  e.Total,
		Discount:               e.Discount,
		Voucher:                e.Voucher,
		PaymentStatus:          constants.PaymentDraft,
		Status:                 constants.CashInStatusPending,
		CashInTransactionItems: &items,
		AdditionalInfo:         e.AdditionalInfo,
	}
}

type TransactionItems struct {
	RefInvoiceNumber string  `json:"ref_invoice_number"`
	PartnerCode      string  `json:"partner_code" validate:"required"`
	ItemName         string  `json:"item_name" validate:"required"`
	Amount           float64 `json:"amount" validate:"required"`
	Note             string  `json:"note"`
}

type CashinActivityLog struct {
	ID                     int       `json:"id"`
	PaymentProvider        string    `json:"payment_provider"`
	PaymentChannel         string    `json:"payment_channel"`
	VirtualAccountNumber   string    `json:"virtual_account_number"`
	PaymentStatus          string    `json:"payment_status"`
	CreatedAt              time.Time `json:"created_at"`
	Description            string    `json:"description"`
	PgReferenceInformation string    `json:"pg_reference_information"`
}

type CashInTransactionHistories struct {
	ID                     int       `gorm:"primaryKey" json:"id"`
	CashInTransactionID    int       `json:"cashin_transcation_id"`
	Description            string    `json:"description"`
	PgReferenceInformation string    `json:"pg_reference_information"`
	PaymentStatus          string    `json:"payment_status"`
	Status                 string    `json:"status"`
	ChannelID              int       `json:"channel_id"`
	ProviderID             int       `json:"provider_id"`
	VirtualAccount         string    `json:"virtual_account"`
	CreatedAt              time.Time `json:"created_at"`
}

type CashinTransactionItems struct {
	ID                 int     `json:"id"`
	ItemName           string  `json:"item_name"`
	TransactionAmount  float64 `json:"transaction_amount"`
	PartnerName        string  `json:"partner_name"`
	DisbursementStatus string  `json:"disbursement_status"`
	DisbursementDate   string  `json:"disbursement_date"`
}

type CashinTranscationResponse struct {
	ID                    int                    `json:"id"`
	ProviderTransactionID string                 `json:"provider_transcation_id"`
	InvoiceNumber         string                 `json:"invoice_number"`
	Amount                float64                `json:"amount"`
	Status                string                 `json:"status"`
	PaymentProvider       string                 `json:"provider"`
	ExpiredTime           string                 `json:"expired_time,omitempty"`
	Type                  string                 `json:"type"`
	Fees                  float64                `json:"fees"`
	Total                 float64                `json:"total"`
	Bank                  string                 `json:"bank,omitempty"`
	ProviderChannelCode   string                 `json:"provider_channel_code"`
	AccountNo             string                 `json:"account_no,omitempty"`
	PaymentCode           string                 `json:"payment_code,omitempty"`
	DisplayName           string                 `json:"display_name,omitempty"`
	PaymentURL            string                 `json:"payment_url,omitempty"`
	CallbackUrl           string                 `json:"callback_url,omitempty"`
	SuccessRediretUrl     string                 `json:"success_redirect_url,omitempty"`
	Actions               *EwalletActionResponse `json:"action,omitempty"`
	AdditionalInfo        *AdditionalInfo        `json:"additional_info,omitempty"`
}

type EwalletActionResponse struct {
	DesktopWebCheckoutURL     string      `json:"desktop_web_checkout_url"`
	MobileDeeplinkCheckoutURL interface{} `json:"mobile_deeplink_checkout_url"`
	MobileWebCheckoutURL      string      `json:"mobile_web_checkout_url"`
	QrCheckoutString          interface{} `json:"qr_checkout_string"`
}

type AdditionalInfo struct {
	HowToPayPage string `json:"how_to_pay_page,omitempty"`
	*domain.CashInAdditionalInfoCashlezz
}

type CashinUpdateTransaction struct {
	PaymentMethodCode  string `json:"payment_method_code"`
	Description        string `json:"description"`
	SuccessRedirectUrl string `json:"success_redirect_url"`
	MobileNumber       string `json:"mobile_number"`
}

type CashinTransactionProcess struct {
	CashinTransaction      domain.CashInTransactions
	PaymentChannel         domain.PaymentChannels
	CompanyMapping         domain.CompanyPaymentProviderChannelMappings
	Credential             string
	ProviderName           string
	ChannelName            string
	ProviderChannelCode    string
	ProcessAt              time.Time
	ExpiredTime            time.Time
	Description            string
	MobileNumber           string
	SuccessRedirectUrl     string
	FailureRedirectUrl     string
	Command                string
	Tx                     *gorm.DB
	IsOverwriteTransaction bool
}

type CashInDetailRes struct {
	ID                            int       `json:"id"`
	CompanyID                     int       `json:"company_id"`
	CompanyName                   string    `json:"company_name"`
	ProductName                   string    `json:"product_name"`
	CompanyProductID              int       `json:"company_product_id"`
	InvoiceNumber                 string    `json:"invoice_number"`
	PaymentAt                     string    `json:"payment_at"`
	ManualPaymentAt               *string   `json:"manual_payment_at"`
	CustomerName                  string    `json:"customer_name"`
	CustomerPhone                 string    `json:"customer_phone"`
	CustomerEmail                 string    `json:"customer_email"`
	PaymentProviderID             int       `json:"payment_provider_id"`
	PaymentProviderName           string    `json:"payment_provider_name"`
	PaymentChannelID              int       `json:"payment_channel_id"`
	PaymentChannelName            string    `json:"payment_channel_name"`
	PaymentChannelTypePaymentType string    `json:"payment_channel_type_payment_type"`
	Total                         float64   `json:"total"`
	AdminFee                      float64   `json:"admin_fee"`
	Discount                      float64   `json:"discount"`
	Voucher                       float64   `json:"voucher"`
	PgDeliveryFee                 float64   `json:"delivery_fee"`
	PaymentStatus                 string    `json:"payment_status"`
	Status                        string    `json:"status"`
	CreatedAt                     time.Time `json:"created_at"`
	UpdatedAt                     time.Time `json:"updated_at"`
	ProductFee                    float64   `json:"product_fee"`
	CompanyProductFee             float64   `json:"company_product_fee"`
	ExpiredAt                     time.Time `json:"expired_at"`
	SubTotalItem                  float64   `json:"sub_total_item"`
	CashInTotal                   float64   `json:"cash_in_total"`
	GrandTotal                    float64   `json:"grand_total"`
	CompanyProductCashoutFee      float64   `json:"company_product_cashout_fee"` // cashout Product Fee = Product fee (Rp) + (Product Fee (%) * total sub item)
}

type CashInTransactionItemCompleteRes struct {
	ID                  int        `json:"id"`
	CashInTransactionID int        `json:"cash_in_transaction_id"`
	PartnerId           int        `json:"partner_id"`
	PartnerName         string     `json:"partner_name"`
	RefInvoiceNumber    string     `json:"ref_invoice_number"`
	ItemName            string     `json:"item_name"`
	Amount              float64    `json:"amount"`
	Status              string     `json:"status"`
	Note                string     `json:"note"`
	IsCashout           bool       `json:"is_cashout"`
	SlaDate             *time.Time `json:"sla_date"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
	ReconciledDate      *time.Time `json:"reconciled_date"`
	DisbursementDate    *time.Time `json:"disbursement_date"`
	DisbursementStatus  string     `json:"disbursement_status"`
	IsOutstanding       bool       `json:"is_outstanding"`
	BatchNumberCashOut  string     `json:"batch_number_cash_out"`
}

func FromDomainCashInTransactionItemComplete(d domain.CashInTransactionItemComplete) CashInTransactionItemCompleteRes {
	return CashInTransactionItemCompleteRes{
		ID:                  d.ID,
		CashInTransactionID: d.CashInTransactionID,
		PartnerId:           d.PartnerId,
		PartnerName:         d.PartnerName,
		ItemName:            d.ItemName,
		RefInvoiceNumber:    d.RefInvoiceNumber,
		Amount:              d.Amount,
		Status:              d.Status,
		Note:                d.Note,
		IsCashout:           d.IsCashout,
		SlaDate:             d.SlaDate,
		CreatedAt:           d.CreatedAt,
		UpdatedAt:           d.UpdatedAt,
		ReconciledDate:      d.ReconciledDate,
		DisbursementDate:    d.DisbursementDate.NullTime(),
		DisbursementStatus:  d.DisbursementStatus,
		IsOutstanding:       d.IsOutstanding,
		BatchNumberCashOut:  d.BatchNumberCashOut,
	}
}

func FromDomainsCashInTransactionItemComplete(ds []domain.CashInTransactionItemComplete) (res []CashInTransactionItemCompleteRes) {
	for _, d := range ds {
		res = append(res, FromDomainCashInTransactionItemComplete(d))
	}
	return
}

type CheckUnpaidBillVARequest struct {
	CustomerPhone     string `json:"customer_phone" validate:"number"`
	PaymentMethodCode string `json:"payment_method_code" validate:"required"`
}
type CheckUnpaidBillVAResponse struct {
	IsExist bool `json:"is_exist"`
}

// Retry Callback Entity
// used for wrapping field that used in retry callback
// this entity would be used for unmarshaling target from pg_reference_information field in cashin history
type RetryCallbackNicepay struct {
	TXid        string `json:"tXid"`
	ReferenceNo string `json:"referenceNo"`
	Amt         string `json:"amt"`
}

type RetryCallbackXenditVA struct {
	ID                       string `json:"id"`
	CallbackVirtualAccountID string `json:"callback_virtual_account_id"`
	ExternalID               string `json:"external_id"`
}
type RetryCallbackXenditEwallet struct {
	ID   string `json:"id"`
	Data struct {
		ID string `json:"id"`
	} `json:"data"`
}
type RetryCallbackXenditCC struct {
	ID string `json:"id"`
}
type RetryCallbackXenditPaymentFixedCode struct {
	ID                 string `json:"id"`
	PaymentCode        string `json:"payment_code"`
	FixedPaymentCodeID string `json:"fixed_payment_code_id"`
}
type RetryCallbackOttocash struct {
	ReferenceNumber     string `json:"referenceNumber"`
	ResponseCode        string `json:"responseCode"`
	ResponseDescription string `json:"responseDescription"`
}

type RetryCallbackOttocashDokuVA struct {
	Order struct {
		InvoiceNumber string `json:"invoice_number"`
	} `json:"order"`
}

type RetryCallbackXfers struct {
	Data struct {
		ID string `json:"id"`
	}
}

type ManualPaymentRequest struct {
	Id        int       `json:"-"`
	PaymentAt time.Time `json:"payment_at"`
	UpdatedBy int       `json:"-"`
}

type CashinListParam struct {
	Pagination         utils.Pagination
	PaymentChannelIDs  []int64
	PaymentProviderIDs []int64
	CompanyProductIDs  []int64
	PartnerIDs         []int64
	Statuses           []string
}

func (p *CashinListParam) Enrich(c *fiber.Ctx) {
	p.PaymentChannelIDs = str.SplitToInt64s(c.Query("payment_channel_ids"), ",")
	p.PartnerIDs = str.SplitToInt64s(c.Query("partner_ids"), ",")
	p.PaymentProviderIDs = str.SplitToInt64s(c.Query("payment_provider_ids"), ",")
	p.CompanyProductIDs = str.SplitToInt64s(c.Query("company_product_ids"), ",")
	if c.Query("statuses") != "" {
		p.Statuses = strings.Split(c.Query("statuses"), ",")
	}
}

type PaymentInstructionResp struct {
	Instructions string `json:"instructions"`
}

type CashInCancelationRequest struct {
	ProviderTransactionID string  `json:"provider_transcation_id"`
	InvoiceNumber         string  `json:"invoice_number" validate:"required"`
	CustomerName          string  `json:"customer_name" validate:"required"`
	Total                 float64 `json:"total" validate:"required"`
}

type CashInChangeChannelRequest struct {
	ProviderTransactionID string                      `json:"provider_transcation_id"`
	LastInvoiceNumber     string                      `json:"last_invoice_number" validate:"min=6"`
	NewInvoiceNumber      string                      `json:"new_invoice_number" validate:"min=6"`
	CustomerEmail         string                      `json:"customer_email" validate:"email"`
	CustomerName          string                      `json:"customer_name"`
	CustomerPhone         string                      `json:"customer_phone" validate:"number"`
	Description           string                      `json:"description"`
	PaymentMethodCode     string                      `json:"payment_method_code"`
	CompanyProductCode    string                      `json:"company_product_code"`
	SuccessRedirectUrl    string                      `json:"success_redirect_url"`
	FailureRedirectUrl    string                      `json:"failure_redirect_url"`
	MobileNumber          string                      `json:"mobile_number"`
	Total                 float64                     `json:"total" validate:"required"`
	AdditionalInfo        domain.CashInAdditionalInfo `json:"additional_info"`
}
