package nicepay

import (
	"encoding/json"
	"fmt"
	"testing"

	"repo.nusatek.id/nusatek/payment/utils"
)

func TestDecryptCredential(t *testing.T) {
	secret := `5ffa8d14f461193311a4dfbf2fae1dcb3e3530db979631857ddf2d83846d8b85fe4ae5412be62ff1c4faa2b86183937d0d1582eebf083bde36347c1d5e7eb83a3c187041c406f2c3bdf284667eb77d94634bf7f3a7470325611e298e874327d107f9a376524f07906d98e50e55a8fd36fb78ed23dd1b43c2da87cb5df2af0eb7d379cb5e0ccdc1e69103f9ee77194d368864f5f5478ec8a10f8adcb5fe07818e03a7f17926ef40541bfa4eacf4a4f45377281980462d8ed1d80afe4eee84aad0f2e7b3b3fbcc1abbf5a386aeda`
	dec, err := utils.DecryptAES(secret, "24Hts87532KlstrdsNgrys973Op34GhnEwL")
	if err != nil {
		return
	}
	var cred Credential
	err = json.Unmarshal([]byte(dec), &cred)
	if err != nil {
		err = fmt.Errorf("unmarshan credential error: %v", err.Error())
		return
	}

	fmt.Println(cred)
}

func TestEncryptCredential(t *testing.T) {
	secret := `{}`
	encpStr, err := utils.EncryptAES(secret, "24Hts87532KlstrdsNgrys973Op34GhnEwL")
	if err != nil {
		return
	}

	fmt.Println(encpStr)
}

func TestGetMerchantToken(t *testing.T) {
	res := GetMerchantToken("20220824200859", Credential{
		MerchantID:  "IONPAYTEST",
		MerchantKey: "33F49GnCMS1mFYlGXisbUDzVf2ATWCl9k3R++d5hDd3Frmuos/XLx8XhXpe+LDYAbpGKZYSwtlyyLOtS/8aD7A==",
	}, "ord20220824200859", "100")
	t.Log(res) //3edb8a095e7139537f05baed105ba5f98bff0df583c0aac05d26941012242ae3
}
