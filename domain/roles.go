package domain

import (
	"time"

	"gorm.io/gorm"
)

type Roles struct {
	ID          int            `json:"id"`
	Code        string         `json:"code" validate:"required"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Status      bool           `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}
