package entity

type UserLogin struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type RSAEncrpty struct {
	Encrypt string `json:"encrypt"`
}

type UserLoginRSA struct {
	Login string `json:"login" validate:"required"`
}

type MenuMappingPermission struct {
	Id         int             `json:"id"`
	MenuId     int             `json:"menu_id,omitempty"`
	MenuName   string          `json:"name,omitempty"`
	MenuCode   string          `json:"menu_code,omitempty"`
	Permission map[string]bool `json:"permission"`
}

type UsersRequest struct {
	ID        int    `json:"-"`
	Name      string `json:"name"`
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password"`
	RoleId    int    `json:"role_id" validate:"required"`
	Status    bool   `json:"status"`
	CompanyId []int  `json:"company_id"`
}

type UsersResponse struct {
	ID        int    `json:"id"`
	Name      string `json:"name"`
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password"`
	RoleId    int    `json:"role_id" validate:"required"`
	Status    bool   `json:"status"`
	CompanyId []int  `json:"company_id"`
}

type UserList struct {
	Id          int    `json:"id"`
	Name        string `json:"name"`
	Email       string `json:"email"`
	RoleId      int    `json:"role_id"`
	RoleName    string `json:"role_name"`
	LastLoginAt string `json:"last_login"`
	Status      bool   `json:"status"`
	CompanyId   []int  `json:"company_id" gorm:"-"`
}
