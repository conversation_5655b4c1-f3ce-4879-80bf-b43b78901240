package xfers

import (
	"context"

	"repo.nusatek.id/nusatek/payment/infrastructure/xfers/disbursement_request"
)

type RestXfers interface {
	Payments(ctx context.Context, req PaymentRequest, credential Credential) (resp PaymentResponse, err error)
	CheckStatus(ctx context.Context, providerTranscationId string, credential Credential) (resp PaymentResponse, err error) 
	Disbursements(ctx context.Context, bodyRequest disbursement_request.AttributeValue, credential Credential) (resp interface{}, statusCode int, err error)
	BankAccountValidation(ctx context.Context, accountNo, bankShortCode string, credential Credential) (resp interface{}, err error)
	AccountBalance(ctx context.Context, credential Credential) (resp interface{}, err error)
	RetrieveDisbursement(ctx context.Context, disbursementID string, credential Credential) (resp DisbursementResponse, err error)
}
