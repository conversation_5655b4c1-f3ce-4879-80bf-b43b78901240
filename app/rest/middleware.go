package rest

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/google/uuid"

	jwtware "github.com/gofiber/jwt/v3"
	"github.com/golang-jwt/jwt/v4"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/constants"

	"repo.nusatek.id/nusatek/payment/utils/model"
)

func SetupMiddleware(app *fiber.App, trxReqLoguc usecase.TrxRequestLogUseCase) {
	app.Use(SetupLogger(trxReqLoguc))
	app.Use(cors.New(cors.Config{
		AllowOrigins:  "*",
		AllowMethods:  "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS",
		AllowHeaders:  "Origin,Authorization,Access-Control-Allow-Origin,token,Content-Type,Accept,Content-Length,Accept-Encoding,Authorization,X-TIMESTAMP,X-CLIENT-KEY,X-CLIENT-SECRET,Content-Type,X-SIGNATURE,Accept,Authorization,Authorization-Customer,ORIGIN,X-PARTNER-ID,X-EXTERNAL-ID,X-IPADDRESS,X-DEVICE-ID,CHANNEL-ID,X-LATITUDE,X-LONGITUDE",
		ExposeHeaders: "Content-Length,Access-Control-Allow-Origin",
	}))
	// Enable CORS with specific headers
	// app.Use(cors.New(cors.Config{
	// 	AllowOrigins: "https://apidevportal.aspi-indonesia.or.id/",
	// 	AllowMethods: "POST,GET,PUT,DELETE",
	// 	AllowHeaders: "X-TIMESTAMP,X-CLIENT-KEY,X-CLIENT-SECRET,Content-Type,XSIGNATURE,Accept,Authorization,Authorization-Customer,ORIGIN,X-PARTNER-ID,X-EXTERNAL-ID,X-IPADDRESS,X-DEVICE-ID,CHANNEL-ID,X-LATITUDE,X-LONGITUDE",
	// }))
	app.Use(func(c *fiber.Ctx) error {
		c.Set("Content-Security-Policy", "upgrade-insecure-requests")
		return c.Next()
	})
}

func SetupLogger(trxReqLoguc usecase.TrxRequestLogUseCase) fiber.Handler {
	return func(c *fiber.Ctx) error {
		threadId := string(c.Request().Header.Peek(constants.HEADER_TID))

		ctx := c.UserContext()
		ctx = logger.SetCtxData(ctx, map[string]interface{}{})
		ctx = logger.MustCorrelationId(ctx, threadId)

		trxReqLogid := uuid.New().String()
		c.SetUserContext(ctx)

		wg := new(sync.WaitGroup)
		wg.Add(1)
		go func() {
			defer wg.Done()
			trxReqLogid = trxLogReqMiddlewareSetter(trxReqLoguc, c, trxReqLogid)
		}()
		ctx = logutil.SetTrxLogId(ctx, trxReqLogid)
		c.SetUserContext(ctx)

		logFields := []logger.Field{
			logger.String("ip", c.IP()),
			logger.String("path", c.Path()),
			logger.String("method", c.Method()),
			logger.String("header", c.Request().Header.String()),
			logger.String("body", string(c.Body())),
		}
		logger.Info(ctx, "request", logFields...)

		err := c.Next()
		logRespFields := []logger.Field{
			logger.String("ip", c.IP()),
			logger.String("header", c.Response().Header.String()),
		}
		if err != nil {
			logRespFields = append(logFields, logger.Err(err))
		}

		logRespFields = append(logRespFields,
			logger.String("body", string(c.Response().Body())),
			logger.Int("status", c.Response().StatusCode()))
		logger.Info(ctx, "response", logRespFields...)

		wg.Wait()
		go trxLogReqMiddlewareUpdater(trxReqLoguc, domain.TrxRequestLog{
			Id:             trxReqLogid,
			ResponseStatus: fmt.Sprintf("%d", c.Response().StatusCode()),
			ResponseBody:   string(c.Response().Body()),
		})

		return nil
	}
}

func AuthJWTValidation(redis *redis.Client) func(c *fiber.Ctx) error {
	jwtConfig := jwtware.Config{
		SigningKey:     []byte(config.GetString("jwtKey")),
		ErrorHandler:   AuthFailedHandler,
		SuccessHandler: AuthSuccessHandler(redis),
	}
	return jwtware.New(jwtConfig)
}

func RolePermissionValidation() fiber.Handler {
	return func(c *fiber.Ctx) error {
		token := c.Locals("user").(*jwt.Token)
		claims := token.Claims.(jwt.MapClaims)
		permission := fmt.Sprintf("%v", claims["permissions"])
		if permission == "" {
			err := errors.SetErrorMessage(http.StatusUnauthorized, "unauthorized invalid token")
			return err
		}
		var dataPermission []model.RolePermissions
		err := json.Unmarshal([]byte(permission), &dataPermission)
		if err != nil {
			err = errors.SetError(http.StatusUnauthorized, "[middleware.RolePermissionValidate] error: "+err.Error())
			return err
		}

		for i := 0; i < len(dataPermission); i++ {
			if dataPermission[i].MenuCode == constants.MenuCompany {
				if strings.HasPrefix(c.Path(), "/v1/company") {
					if strings.HasPrefix(c.Path(), "/v1/companies") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuProduct {
				if strings.HasPrefix(c.Path(), "/v1/company-product") {
					if strings.HasPrefix(c.Path(), "/v1/company-products") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuPartner {
				if strings.HasPrefix(c.Path(), "/v1/partner") {
					if strings.HasPrefix(c.Path(), "/v1/partner/disbursement") ||
						strings.HasPrefix(c.Path(), "/v1/partner/company") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuCashIn {
				if strings.HasPrefix(c.Path(), "/v1/cashin") {
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuCashOut {
				if strings.HasPrefix(c.Path(), "/v1/cashout") {
					if strings.HasPrefix(c.Path(), "/v1/cashout/generate") {
						return c.Next()
					}
					if strings.HasPrefix(c.Path(), "/v1/cashout/approval") ||
						strings.HasPrefix(c.Path(), "/v1/cashout/fee-payment") {
						if dataPermission[i].Permissions["approve"] {
							return c.Next()
						}
					}
					if strings.HasPrefix(c.Path(), "/v1/cashout/rejected") {
						if dataPermission[i].Permissions["reject"] {
							return c.Next()
						}
					}

					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuChannel {
				if strings.HasPrefix(c.Path(), "/v1/payment-channel") {
					if strings.HasPrefix(c.Path(), "/v1/payment-channels") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuProviderChannel {
				if strings.HasPrefix(c.Path(), "/v1/payment-provider") {
					if strings.HasPrefix(c.Path(), "/v1/payment-providers") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
				if strings.HasPrefix(c.Path(), "/v1/channel-mapping") {
					if strings.HasPrefix(c.Path(), "/v1/channel-mapping/provider") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuCompanyProvider {
				if strings.HasPrefix(c.Path(), "/v1/provider/company/mapping") {
					if c.Method() == http.MethodGet {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}

				if strings.HasPrefix(c.Path(), "/v1/provider/channel/mapping") {
					if c.Method() == http.MethodGet {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuRole {
				if strings.HasPrefix(c.Path(), "/v1/user/role") {
					if strings.HasPrefix(c.Path(), "/v1/user/roles/all") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}

			if dataPermission[i].MenuCode == constants.MenuUser {
				if strings.HasPrefix(c.Path(), "/v1/user") {
					if strings.HasPrefix(c.Path(), "/v1/user/role") ||
						strings.HasPrefix(c.Path(), "/v1/user/roles") ||
						strings.HasPrefix(c.Path(), "/v1/user/menu") ||
						strings.HasPrefix(c.Path(), "/v1/user/permission") ||
						strings.HasPrefix(c.Path(), "/v1/user/login") {
						return c.Next()
					}
					err := validBasicPermission(c, dataPermission[i].Permissions)
					if err != nil {
						return err
					}
				}
			}
		}

		return c.Next()
	}
}

func AuthSuccessHandler(rdb *redis.Client) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		token := c.Locals("user").(*jwt.Token)

		ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*1000)
		defer cancel()

		res, err := rdb.Get(ctx, fmt.Sprintf("token:revoked:%s", token.Raw)).Result()
		if err != redis.Nil || res != "" {
			if err != nil {
				err = errors.SetError(http.StatusUnauthorized, "failed to verify the token in database: "+err.Error())
			}
			return errors.SetError(http.StatusUnauthorized, err.Error())
		}

		tokenData := token.Claims.(jwt.MapClaims)
		encodedToken, err := json.Marshal(tokenData)
		if err != nil {
			return errors.SetError(http.StatusBadRequest, fmt.Sprintf("filed to encode token data: %v", err.Error()))
		}

		decodedToken := model.JwtToken{}
		if err := json.Unmarshal(encodedToken, &decodedToken); err != nil {
			return errors.SetError(http.StatusInternalServerError, fmt.Sprintf("filed to decode token data: %v", err.Error()))
		}

		c.Locals("token", decodedToken)
		return c.Next()
	}
}

func AuthFailedHandler(c *fiber.Ctx, e error) error {
	message := http.StatusText(http.StatusUnauthorized)
	if e != nil {
		message = e.Error()
	}

	err := errors.SetErrorMessage(http.StatusUnauthorized, message)
	return errors.ErrorHandle(c, err)
}

func validBasicPermission(c *fiber.Ctx, permission map[string]bool) (err error) {
	switch c.Method() {
	case http.MethodGet:
		if permission["view"] {
			return
		}

	case http.MethodPost:
		if permission["create"] {
			return
		}

	case http.MethodDelete:
		if permission["delete"] {
			return
		}

	case http.MethodPut:
		switch true {
		case permission["edit"]:
			return

		case permission["transfer"]:
			return
		}
	}

	err = errors.SetErrorMessage(http.StatusUnauthorized, "user role does not have access")
	return
}

var trxMapWhitelist = map[string]struct{}{
	"/xfers/callback":                   {},
	"/nicepay/callback":                 {},
	"/nicepay/snap/callback":            {},
	"/xendit/va/callback":               {},
	"/xendit/ewallet/callback":          {},
	"/xendit/invoice/callback":          {},
	"/xendit/fixedPaymentCode/callback": {},
	"/xendit/cashout/callback":          {},
	"/ottocash/invoice/callback":        {},
	"/doku/va/callback":                 {},
	"/cashlez/offlinePg/callback":       {},
	"/openapi/v1.0/transfer-va/inquiry": {},
	"/openapi/v1.0/transfer-va/payment": {},
}

func trxLogReqMiddlewareSetter(uc usecase.TrxRequestLogUseCase, c *fiber.Ctx, trxid string) string {
	ctx := c.UserContext()
	if _, exist := trxMapWhitelist[c.Path()]; !exist {
		return ""
	}

	reqLog := &domain.TrxRequestLog{
		Id:             trxid,
		CorrelationId:  logger.MustCorrIdVal(ctx),
		RequestHeaders: c.Request().Header.String(),
		RequestMethod:  c.Method(),
		RequestPath:    c.Path(),
		RequestQuery:   c.Context().QueryArgs().String(),
		RequestBody:    string(c.Request().Body()),
	}

	errLog := uc.Insert(ctx, reqLog)
	if errLog != nil {
		logger.Error(ctx, "error insert trx request log", logger.Err(errLog))
	}

	return reqLog.Id
}

func trxLogReqMiddlewareUpdater(uc usecase.TrxRequestLogUseCase, data domain.TrxRequestLog) {
	if len(data.Id) == 0 {
		return
	}
	ctx := context.TODO()
	errLog := uc.UpdateResponse(ctx, &data)
	if errLog != nil {
		logger.Error(ctx, "error update trx request log", logger.Err(errLog))
	}
}
