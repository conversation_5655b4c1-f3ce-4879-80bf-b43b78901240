package xfers

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	rest "repo.nusatek.id/moaja/backend/libraries/rest-utils"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers/disbursement_request"
	"repo.nusatek.id/nusatek/payment/utils/config"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

type wrapper struct {
	Endpoint  string
	isSandbox bool
	client    rest.RestClient
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Setup() RestXfers {
	var (
		EndpointSandbox = config.GetString("xfers.sandbox")
		Endpoint        = config.GetString("xfers.host")
	)

	w.isSandbox = config.GetBool("xfers.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	restOptions := rest.Options{
		Address:   w.Endpoint,
		Timeout:   config.GetDuration("xfers.timeout"),
		SkipTLS:   config.GetBool("xfers.skiptls"),
		DebugMode: config.GetBool("xfers.debug"),
	}

	if config.GetString("xfers.proxy") != "" {
		restOptions.WithProxy = true
		restOptions.ProxyAddress = config.GetString("xfers.proxy")
	}

	w.client = rest.New(restOptions)

	return w
}

type Data struct {
	Data Attributes `json:"data"`
}
type Attributes struct {
	AttributeValue interface{} `json:"attributes"`
}

func setHeader(credential Credential) http.Header {
	secret := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%v:%v", credential.Username, credential.Password)))

	header := http.Header{}
	header.Add("Authorization", "Basic "+secret)
	header.Add("Accept", "application/json")
	header.Add("Content-Type", "application/vnd.api+json")

	return header
}

func (w *wrapper) Payments(ctx context.Context, req PaymentRequest, credential Credential) (resp PaymentResponse, err error) {
	path := "/payments"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusCreated {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if len(datas.Data) >= 1 {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Data[0].Detail)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, "failed request payment to payment provider")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatus(ctx context.Context, providerTranscationId string, credential Credential) (resp PaymentResponse, err error) {
	path := "/payments/" + providerTranscationId
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", providerTranscationId))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if len(datas.Data) >= 1 {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Data[0].Detail)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, "failed request payment to payment provider")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) Disbursements(ctx context.Context, bodyRequest disbursement_request.AttributeValue, credential Credential) (resp interface{}, statusCode int, err error) {
	header := setHeader(credential)
	data := &Data{
		Data: Attributes{
			AttributeValue: bodyRequest,
		},
	}
	conv, _ := json.Marshal(data)
	req := string(conv)
	path := "/disbursements"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	body, statusCode, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) BankAccountValidation(ctx context.Context, accountNo, bankShortCode string, credential Credential) (resp interface{}, err error) {
	data := &Data{
		Data: Attributes{
			AttributeValue: map[string]interface{}{
				"accountNo":     accountNo,
				"bankShortCode": bankShortCode,
			},
		},
	}
	conv, _ := json.Marshal(data)
	req := string(conv)
	path := "/validation_services/bank_account_validation"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, _, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) AccountBalance(ctx context.Context, credential Credential) (resp interface{}, err error) {
	path := "/overviews/balance_overview"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path))

	header := setHeader(credential)
	body, _, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) RetrieveDisbursement(ctx context.Context, disbursementID string, credential Credential) (resp DisbursementResponse, err error) {
	path := "/disbursements/" + disbursementID
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", disbursementID))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if len(datas.Data) >= 1 {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Data[0].Detail)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}
		err = errors.SetErrorMessage(http.StatusInternalServerError, "Failed request retrieval status to xfers")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}
