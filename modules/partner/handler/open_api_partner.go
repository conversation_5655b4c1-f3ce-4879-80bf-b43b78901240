package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

func (h *PartnerHandler) CreatePartnerAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")

	var req domain.Partners
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.PartnerUseCase.CreatePartnerAPI(c.UserContext(), &req, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *PartnerHandler) UpdatePartnerAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	partnerId := c.Params("id")

	var req domain.Partners
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.PartnerUseCase.UpdatePartnerAPI(c.UserContext(), &req, partnerId, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, req)
}

func (h *PartnerHandler) DeletePartnerAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	id := c.Params("id")

	err = h.PartnerUseCase.DeletePartnerApi(c.UserContext(), id, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccess(c, nil)
}

func (h *PartnerHandler) GetListPartnerAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}

	resp, totalData, err := h.PartnerUseCase.GetListPartnerAPI(c.UserContext(), paginate, string(auth))
	if err != nil {
		return
	}

	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *PartnerHandler) GetPartnerCodeAPI(c *fiber.Ctx) (err error) {
	auth := c.Request().Header.Peek("Authorization")
	code := c.Params("code")

	resp, err := h.PartnerUseCase.GetPartnerCodeAPI(c.UserContext(), string(auth), code)
	if err != nil {
		return
	}

	return response.HandleSuccess(c, resp)
}
