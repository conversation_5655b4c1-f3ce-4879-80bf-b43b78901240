package handler

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/file/usecase"
)

type defaultFile struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.FileRepository {
	return &defaultFile{db}
}

func (r *defaultFile) CreateFile(ctx context.Context, req *domain.Files) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultFile) UpdateFile(ctx context.Context, req *domain.Files) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultFile) GetFileById(ctx context.Context, id string) (resp *domain.Files, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultFile) DeleteFile(ctx context.Context, req *domain.Files) (err error) {
	err = r.db.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}
	return
}

func (r *defaultFile) GetFileListAll(ctx context.Context) (resp *[]domain.Files, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.Files{}).Order("updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultFile) GetFileListAllUnassignedByUser(ctx context.Context, types, userUpload string) (resp *[]domain.Files, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.Files{}).
		Joins("LEFT JOIN payment_channels ON payment_channels.logo_id = files.id").
		Where("payment_channels.id IS NULL AND files.type = ? AND files.user_upload = ?", types, userUpload).
		Order("files.updated_at DESC").Count(&totalData).Find(&resp).Error

	return
}
