package usecase

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type CompanyMappingRepository interface {
	BeginTrans() *gorm.DB
	GetProviderChannelMapByCode(ctx context.Context, code string) (domain.PaymentProviderChannelMappings, bool)
	GetCompleteProviderChannelMapByCode(ctx context.Context, code string) (res domain.PaymentProviderChannelMappingsComplete, err error)
	PaymentChannel(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	CreateMapping(ctx context.Context, tx *gorm.DB, partner *domain.CompanyPaymentProviderChannelMappings) (res *domain.CompanyPaymentProviderChannelMappings, err error)
	UpdateMapping(ctx context.Context, tx *gorm.DB, providerMapping *domain.CompanyPaymentProviderChannelMappings) (err error)
	ViewMapping(ctx context.Context, id int) (interface{}, error)
	DeleteMapping(ctx context.Context, ID int64) (bool, error)
	ListCompanyXProviderMapping(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	CreateCompanyXProviderMapping(ctx context.Context, provider *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error)
	UpdateCompanyXProviderMapping(ctx context.Context, provider *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error)
	ViewCompanyXProviderMapping(ctx context.Context, param map[string]interface{}) (interface{}, error)
	DeleteCompanyXProviderMapping(ctx context.Context, ID int64) (bool, error)
	GetCompanyByID(ctx context.Context, ID int64) (domain.Companies, bool)
	GetCompanyProviderMappingByID(ctx context.Context, ID int64) (domain.CompanyPaymentProviderMappings, bool)
	GetCompanyProviderChannelMappingByID(ctx context.Context, ID int64) (domain.CompanyPaymentProviderChannelMappings, bool)
	GetCompanyProviderChannelMappingByProviderIdNChannelID(ctx context.Context, providerID int, channelID int) (resp *domain.CompanyPaymentProviderChannelMappings, err error)
	GetListCompanyProviderChannelMapping(ctx context.Context, req *domain.GetCompanyMappingReq, paginate utils.Pagination) (resp []entity.CompanyMappingShow, totalData int64, err error)
	GetCompanyProviderChannelMappingRequestBy(ctx context.Context, companyId, providerId, channelId, capability string) (resp *domain.CompanyPaymentProviderChannelMappings, err error)
	GetCompanyProviderMappingByCompanyIdAndProviderId(ctx context.Context, companyId, providerId string) (resp *domain.CompanyPaymentProviderMappings, err error)
	GetListAllCompanyProviderMappingChannelByCompanyID(ctx context.Context, companyId string) (resp *[]entity.ListCompanyMappingChannel, err error)
	GetListAllCompanyProviderMappingAPI(ctx context.Context, companyId string, req domain.GetAllCompanyProviderMappingReq) (resp *[]entity.ListCompanyMappingProviderChannel, err error)
	GetCompanyProviderMappingCodeAPI(ctx context.Context, companyId, code string) (resp *entity.ListCompanyMappingProviderChannel, err error)
	GetListAllCompanyPaymentProvider(ctx context.Context, companyId, channelName string) (resp *[]entity.ListCompanyMappingChannelCashOut, err error)
	GetListMappingCashInPaymentMethod(ctx context.Context, companyId string) (resp *[]entity.ListChannelMappingOpenAPI, err error)
	UpdateCompanyChannelMappingStatusByCompanyIdAndChannelId(ctx context.Context, companyId, channelId, capability string, reqStatus bool) (err error)
	UpdateCompanyChannelMappingStatusById(ctx context.Context, id int64, reqStatus bool) (err error)
	GetMappingById(ctx context.Context, id int) (resp *domain.CompanyPaymentProviderChannelMappings, err error)
	GetCompanyProviderMapping(ctx context.Context, companyId, paymentProviderId, paymentChannelId, capability int) (resp *entity.CompanyMappingShow, err error)
}
type CounterRepository interface {
	GetLastCounter(ctx context.Context, mappingId int, types string) (domain.CompanyPaymentProviderChannelMappingCounter, error)
	DecreaseCounter(ctx context.Context, mappingId int, types string) (res domain.CompanyPaymentProviderChannelMappingCounter, err error)
	CreateIfNotExist(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter) (res domain.CompanyPaymentProviderChannelMappingCounter, err error)
	CreateOrUpdateCounter(ctx context.Context, tx *gorm.DB, req *domain.CompanyPaymentProviderChannelMappingCounter, currentCounter int64) (res domain.CompanyPaymentProviderChannelMappingCounter, err error)
}

// fixed va customer
type CustomerFVARepository interface {
	Create(ctx context.Context, req *domain.FixedVACustomer) error
	Update(ctx context.Context, tx *gorm.DB, req *domain.FixedVACustomer) error
	GetOne(ctx context.Context, mappingId int64, phone string) (domain.FixedVACustomer, error)
	GetOneByVA(ctx context.Context, mappingId int64, phone string) (domain.FixedVACustomer, error)
}

type CompanyMappingUsecase interface {
	PaymentChannelList(ctx context.Context, req *domain.GetCompanyMappingReq, paginate utils.Pagination) (resp []entity.CompanyMappingShow, totalItem int64, err error)
	CreateMapping(ctx context.Context, partner *domain.CompanyPaymentProviderChannelMappings, providerCode string) (res *domain.CompanyPaymentProviderChannelMappings, err error)
	UpdateMapping(ctx context.Context, partner *domain.CompanyPaymentProviderChannelMappings, providerCode string) (res *domain.CompanyPaymentProviderChannelMappings, err error)
	ViewMapping(ctx context.Context, id int) (interface{}, error)
	DeleteMapping(ctx context.Context, ID int64) (bool, error)
	ListCompanyXProviderMapping(ctx context.Context, params map[string]interface{}, orderParam map[string]interface{}) ([]interface{}, int64, error)
	CreateCompanyXProviderMapping(ctx context.Context, provider *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error)
	UpdateCompanyXProviderMapping(ctx context.Context, req *domain.CompanyPaymentProviderMappings) (res *domain.CompanyPaymentProviderMappings, err error)
	ViewCompanyXProviderMapping(ctx context.Context, param map[string]interface{}) (interface{}, error)
	DeleteCompanyXProviderMapping(ctx context.Context, ID int64) (bool, error)
	GetCompanyProviderMappingByCompanyIdAndProviderId(ctx context.Context, companyId, providerId string) (resp *domain.CompanyPaymentProviderMappings, err error)
	GetListAllCompanyProviderMappingChannelByCompanyID(ctx context.Context, companyId string) (resp *[]entity.ListCompanyMappingChannel, err error)
	GetListAllCompanyPaymentProvider(ctx context.Context, companyId, channelId string) (resp *[]entity.ListCompanyMappingChannelCashOut, err error)
	GetListAllCompanyProviderMappingAPI(ctx context.Context, auth string, req domain.GetAllCompanyProviderMappingReq) (resp *[]entity.ListCompanyMappingProviderChannel, err error)
	GetCompanyProviderMappingCodeAPI(ctx context.Context, auth, code string) (resp *entity.ListCompanyMappingProviderChannel, err error)
	GetListMappingCashInPaymentMethodAPI(ctx context.Context, auth string) (resp *[]entity.ListChannelMappingOpenAPI, err error)
	// customer fixed va
	UpdateFixedVACustomer(ctx context.Context, tx *gorm.DB, req *domain.FixedVACustomer) (err error)
	GetOrCreateCustomerFVA(ctx context.Context, ignoreCounter bool, phone string, companyMapping domain.CompanyPaymentProviderChannelMappings) (res domain.FixedVACustomer, err error)
	GetOrCreateCustomerFVAByVaNumber(ctx context.Context, ignoreCounter bool, cust domain.FixedVACustomer, companyMapping domain.CompanyPaymentProviderChannelMappings) (res domain.FixedVACustomer, err error)
	GetOneCustomerFVAByPhone(ctx context.Context, phone string, channelMappingID int64) (res domain.FixedVACustomer, err error)
	UpdateStatusCompanyXProviderMapping(ctx context.Context, id int64, status bool) (res *domain.CompanyPaymentProviderMappings, err error)
	UpdateStatusChannelMapping(ctx context.Context, id int64, status bool) (res *domain.CompanyProductProviderChannelMapping, err error)
}
