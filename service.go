package ottopay

import (
	"context"
	"time"

	"github.com/gofiber/fiber/v2"
)

// OttoPayService defines the interface for OttoPay integration
type OttoPayService interface {
	// Authentication methods
	GenerateToken(ctx context.Context, req TokenRequest) (TokenResponse, error)
	ValidateToken(ctx context.Context, token string) (*TokenClaims, error)

	// Business logic integration methods
	ProcessInquiry(ctx context.Context, req InquiryRequest) (InquiryResponse, error)
	ProcessPayment(ctx context.Context, req PaymentRequest) (PaymentResponse, error)

	// HTTP handlers for Fiber
	GetTokenHandler(c *fiber.Ctx) error
	InquiryHandler(c *fiber.Ctx) error
	PaymentHandler(c *fiber.Ctx) error

	// Middleware
	AuthMiddleware() fiber.Handler

	// Configuration and setup
	Setup(config Config) OttoPayService
}

// BusinessLogicHandler defines the interface for business logic integration
type BusinessLogicHandler interface {
	// HandleInquiry processes inquiry requests and returns customer/bill information
	HandleInquiry(ctx context.Context, req InquiryRequest) (InquiryData, error)

	// HandlePayment processes payment requests and returns payment status
	HandlePayment(ctx context.Context, req PaymentRequest) (PaymentData, error)

	// ValidateCredentials validates username/password for token generation
	ValidateCredentials(ctx context.Context, username, password string) (UserData, error)
}

// InquiryData represents the business data for inquiry response
type InquiryData struct {
	CustomerName   string  `json:"customer_name"`
	TotalAmount    float64 `json:"total_amount"`
	CurrencyCode   string  `json:"currency_code"`
	SubCompany     string  `json:"sub_company"`
	AdditionalData string  `json:"additional_data"`
	IsValid        bool    `json:"is_valid"`
	ErrorMessage   string  `json:"error_message,omitempty"`
}

// PaymentData represents the business data for payment response
type PaymentData struct {
	IsSuccess      bool    `json:"is_success"`
	CustomerName   string  `json:"customer_name"`
	TotalAmount    float64 `json:"total_amount"`
	PaidAmount     float64 `json:"paid_amount"`
	CurrencyCode   string  `json:"currency_code"`
	AdditionalData string  `json:"additional_data"`
	ErrorMessage   string  `json:"error_message,omitempty"`
	IsTimeout      bool    `json:"is_timeout,omitempty"`
}

// UserData represents user information for authentication
type UserData struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	IsValid  bool   `json:"is_valid"`
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	AppName  string `json:"app_name"`
	Exp      int64  `json:"exp"`
}

// Config represents OttoPay service configuration
type Config struct {
	// JWT configuration
	JWTSecret     string        `json:"jwt_secret"`
	TokenDuration time.Duration `json:"token_duration"`

	// Server configuration
	Port         string   `json:"port"`
	AllowedIPs   []string `json:"allowed_ips"`
	EnableIPAuth bool     `json:"enable_ip_auth"`

	// Bank prefixes configuration
	BankPrefixes map[string]string `json:"bank_prefixes"`

	// Security configuration
	EnableSignatureValidation bool   `json:"enable_signature_validation"`
	SignatureSecret           string `json:"signature_secret"`
	APIKey                    string `json:"api_key"` // optional: when set, token endpoint requires matching x-api-key

	// Timeout configuration
	RequestTimeout time.Duration `json:"request_timeout"`

	// Retry configuration (hardcoded defaults via builder; no env)
	RetryMaxAttempts       int           `json:"retry_max_attempts"`
	RetryInitialBackoff    time.Duration `json:"retry_initial_backoff"`
	RetryMaxBackoff        time.Duration `json:"retry_max_backoff"`
	RetryBackoffMultiplier float64       `json:"retry_backoff_multiplier"`

	// Business logic handler
	BusinessHandler BusinessLogicHandler `json:"-"`
}
