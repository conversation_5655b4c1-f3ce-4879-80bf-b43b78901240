package routers

import (
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"

	middleware "repo.nusatek.id/nusatek/payment/app/rest"
	"repo.nusatek.id/nusatek/payment/modules/trx_request_log/usecase"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

func (h *rooters) SetupRouters(r fiber.Router, cache *redis.Client) {
	// Check Healty
	r.Get("/", h.healtyHandler.HealtyCheck)

	// User Login
	r.Post("/login", h.userHandler.UserLogin)

	// Jwt Autorization
	// f := r.Use(middleware.AuthJWTValidation(cache)).Use(middleware.RolePermissionValidation())
	f := r.Use(middleware.AuthJWTValidation(cache))

	// Companies
	f.Get("/companyGenerateApiKey", h.companyHandler.GenerateRandomApiKey)
	f.Post("/company", h.companyHandler.CreateCompany)
	f.Put("/company/:id/webhook", h.webhookHandler.VerifyAndSave)
	f.Get("/company/:id", h.companyHandler.GetCompanyByID)
	f.Put("/company/:id", h.companyHandler.UpdateCompany)
	f.Put("/company/:id/status", h.companyHandler.UpdateStatus)
	f.Delete("/company/:id", h.companyHandler.DeleteCompany)
	f.Get("/company", h.companyHandler.SearchCompany)
	f.Get("/companies", h.companyHandler.GetCompanyListAll)

	// CompanyProduct
	f.Post("/company-product", h.companyProductHandler.CreateCompanyProduct)
	f.Get("/company-product/:id", h.companyProductHandler.GetCompanyProductByID)
	f.Put("/company-product/:id", h.companyProductHandler.UpdateCompanyProduct)
	f.Put("/company-product/:id/status", h.companyProductHandler.UpdateCompanyProductStatus)
	f.Delete("/company-product/:id", h.companyProductHandler.DeleteCompanyProduct)
	f.Get("/company-product", h.companyProductHandler.SearchCompanyProduct)
	f.Get("/company-products", h.companyProductHandler.GetCompanyProductListAll)
	// CompanyProduct and channel mappings
	f.Post("/company-product/:id/payment-channels/bulk", h.companyProductProviderChannelMappingHandler.CreateBulk)
	f.Get("/company-product/:id/payment-channels", h.companyProductProviderChannelMappingHandler.GetAllWithDetail)
	f.Put("/company-product/:id/payment-channels/bulk", h.companyProductProviderChannelMappingHandler.PatchDataBulk)
	f.Delete("/company-product/:id/payment-channels/bulk", h.companyProductProviderChannelMappingHandler.DeleteBulk)

	// Payment Channel
	f.Post("/payment-channel", h.paymentChannelHandler.CreatePaymentChannel)
	f.Put("/payment-channel/:id", h.paymentChannelHandler.UpdatePaymentChannel)
	f.Put("/payment-channel/:id/status", h.paymentChannelHandler.UpdatePaymentChannelStatus)
	f.Delete("/payment-channel/:id", h.paymentChannelHandler.DeletePaymentChannel)
	f.Get("/payment-channel", h.paymentChannelHandler.SearchPaymentChannel)
	f.Get("/payment-channel/:id", h.paymentChannelHandler.GetPaymentChannelById)
	f.Get("/payment-channels", h.paymentChannelHandler.GetPaymentChannelListAll)
	f.Get("/payment-channels/active", h.paymentChannelHandler.GetPaymentChannelListActive)
	f.Get("/payment-channels/types", h.paymentChannelHandler.GetListPaymentChannelType)

	// Payment Provider
	// f.Post("/payment-provider", h.paymentProviderHandler.CreatePaymentProvider)
	f.Put("/payment-provider/:id", h.paymentProviderHandler.UpdatePaymentProvider)
	// f.Delete("/payment-provider/:id", h.paymentProviderHandler.DeletePaymentProvider)
	f.Put("/payment-provider/:id/status", h.paymentProviderHandler.UpdateStatus)
	f.Get("/payment-provider/:id", h.paymentProviderHandler.GetPaymentProviderById)
	f.Get("/payment-provider", h.paymentProviderHandler.SearchPaymentProvider)
	f.Get("/payment-providers", h.paymentProviderHandler.GetPaymentProviderListAll)
	f.Get("/payment-providers/default", h.paymentProviderHandler.GetPaymentProviderDefaults)

	// Channel Mappings
	f.Post("/channel-mapping", h.channelMappingHandler.CreateChannelMapping)
	f.Put("/channel-mapping/:id", h.channelMappingHandler.UpdateChannelMapping)
	f.Delete("/channel-mapping/:id", h.channelMappingHandler.DeleteChannelMapping)
	f.Get("/channel-mapping/:id", h.channelMappingHandler.GetChannelMappingByID)
	f.Get("/channel-mapping/provider/:providerId", h.channelMappingHandler.GetListChannelMappingByProviderId)

	// File
	f.Post("/file/upload", h.fileHandler.Upload)

	// partner
	partnerRoute := f.Group("/partner")
	partnerRoute.Get("/", h.partnerHandler.List)
	partnerRoute.Get("/:id", h.partnerHandler.View)
	partnerRoute.Delete("/:id", h.partnerHandler.Delete)
	partnerRoute.Post("/", h.partnerHandler.Create)
	partnerRoute.Patch("/status", h.partnerHandler.PatchStatusBulk)
	partnerRoute.Put("/:id/status", h.partnerHandler.UpdateStatus)
	partnerRoute.Put("/:id", h.partnerHandler.Update)
	partnerRoute.Get("/disbursement/history", h.partnerHandler.ListHistory)
	partnerRoute.Get("/disbursement/history/view", h.partnerHandler.ViewHistory)
	partnerRoute.Get("/company/:companyId", h.partnerHandler.GetPartnersByCompanyId)
	partnerRoute.Get("/tag/:id", h.partnerHandler.GetTagByID)
	partnerRoute.Post("/tag", h.partnerHandler.CreateTag)
	partnerRoute.Put("/tag/:id", h.partnerHandler.UpdateTag)
	partnerRoute.Delete("/tag/:id", h.partnerHandler.DeleteTag)
	partnerRoute.Get("/tag/list/all", h.partnerHandler.GetListAllTag)

	// company payment provider
	channelMappingRoute := f.Group("/provider/channel/mapping")
	channelMappingRoute.Get("/", h.companyMappingHandler.ListChannelMapping)
	channelMappingRoute.Get("/all", h.companyMappingHandler.ListChannelMappingAll)
	channelMappingRoute.Get("/:id", h.companyMappingHandler.ViewChannelMapping)
	channelMappingRoute.Post("/", h.companyMappingHandler.CreateChannelMapping)
	channelMappingRoute.Put("/:id", h.companyMappingHandler.UpdateChannelMapping)
	channelMappingRoute.Put("/:id/status", h.companyMappingHandler.UpdateStatusChannelMapping)
	channelMappingRoute.Delete("/:id", h.companyMappingHandler.DeleteMapping)

	// Company List Mapping Dropdown
	f.Get("/company-channel/company/:companyId", h.companyMappingHandler.GetListAllCompanyProviderMappingChannelByCompanyID)
	f.Get("/company-channel/company/:companyId/channel/:channelId", h.companyMappingHandler.GetListAllCompanyPaymentProvider)

	companyMappingRoute := f.Group("/provider/company/mapping")
	companyMappingRoute.Get("/", h.companyMappingHandler.ListCompanyXProviderMapping)
	companyMappingRoute.Get("/:id", h.companyMappingHandler.ViewCompanyXProviderMapping)
	companyMappingRoute.Post("/", h.companyMappingHandler.CreateCompanyXProviderMapping)
	companyMappingRoute.Put("/:id", h.companyMappingHandler.UpdateCompanyXProviderMapping)
	companyMappingRoute.Delete("/:id", h.companyMappingHandler.DeleteCompanyXProviderMapping)
	companyMappingRoute.Put("/:id/status", h.companyMappingHandler.UpdateStatusCompanyXProviderMapping)

	// Cashin
	f.Get("/cashin", h.cashinHandler.SearchCashinTranscation)
	f.Get("/cashin/:id/detail", h.cashinHandler.GetByIDDetail)
	f.Get("/cashin/:id/activity", h.cashinHandler.GetCashinActivityLog)
	f.Get("/cashin/:id/items", h.cashinHandler.GetListItemsByTransactionID)
	f.Put("/cashin/refundPayment", h.cashinHandler.RefundPaymentCashinTransaction)
	f.Get("/cashin/export", h.cashinHandler.Export)
	f.Post("/cashin/retryCallback/:id", h.cashinHandler.RetryCallback)
	f.Post("/cashin/manualPayment/:id", h.cashinHandler.ManualPayment)

	cashOutRoute := f.Group("/cashout")
	cashOutRoute.Get("/", h.cashOutHandler.List)
	cashOutRoute.Get("/activity", h.cashOutHandler.ListHistory)
	cashOutRoute.Get("/detail/id/:id", h.cashOutHandler.GetDetailByID)
	cashOutRoute.Get("/detail/outstanding", h.cashOutHandler.GetAllOutstandingItemCashIn)
	cashOutRoute.Post("/detail/bulk", h.cashOutHandler.AddCashOutItemsByIDs)
	cashOutRoute.Delete("/detail/bulk", h.cashOutHandler.DeleteCashOutItemsByIDs)
	cashOutRoute.Put("/:id", h.cashOutHandler.RequestDisbursement)
	cashOutRoute.Get("/generate", h.cashOutHandler.GenerateQRGoogleAuthorization)
	cashOutRoute.Get("/export", h.cashOutHandler.Export)
	cashOutRoute.Post("/approval", h.cashOutHandler.ApproveCashOutTransactionStatus)
	cashOutRoute.Post("/rejected", h.cashOutHandler.RejectedCashOutTransactionStatus)
	cashOutRoute.Post("/fee-payment", h.cashOutHandler.UpdateCashOutTransactionStatusFeePaymentStatus)
	cashOutRoute.Get("/:cashOutId/items", h.cashOutHandler.GetListItemCashInBatchNumber)
	cashOutRoute.Put("/:cashOutId/done", h.cashOutHandler.UpdateDoneStatus)
	cashOutRoute.Post("/:cashOutId/resend-email", h.cashOutHandler.ResendEmail)

	// User management
	userRouter := f.Group("")
	userRouter.Post("/user", h.userHandler.CreateUser)
	userRouter.Delete("/user/:id", h.userHandler.DeleteUser)
	userRouter.Put("/user/:id", h.userHandler.UpdateUser)
	userRouter.Put("/user/:id/status", h.userHandler.UpdateStatus)
	userRouter.Get("/user/:id", h.userHandler.GetUserById)
	userRouter.Get("/users", h.userHandler.SearchUserList)
	userRouter.Post("/user/login/generate", h.userHandler.GenerateLogin)
	userRouter.Get("/user/permission/role/:roleId", h.userHandler.ListMenuMappingPermissionAll)
	userRouter.Put("/user/permission/role/:roleId", h.userHandler.UpdateListMenuMappingPermission)

	userRouter.Post("/user/menu", h.userHandler.CreateUserMenu)
	userRouter.Put("/user/user/menu/:id", h.userHandler.UpdateUserMenu)
	userRouter.Get("/user/menu/:id", h.userHandler.GetUserMenuById)
	userRouter.Delete("/user/menu/:id", h.userHandler.DeleteUserMenu)

	userRouter.Post("/user/role", h.userHandler.CreateRole)
	userRouter.Put("/user/role/:id", h.userHandler.UpdateRole)
	userRouter.Put("/user/role/:id/status", h.userHandler.UpdateStatusRole)
	userRouter.Get("/user/role/:id", h.userHandler.GetRoleByID)
	userRouter.Delete("/user/role/:id", h.userHandler.DeleteRole)
	userRouter.Get("/user/roles/all", h.userHandler.GetListAllRoles)
	userRouter.Get("/user/roles/search", h.userHandler.SearchRoles)

	configurationEmailRoute := f.Group("/configuration")
	configurationEmailRoute.Get("/email-template", h.configurationEmailHandler.GetTemplates)
	configurationEmailRoute.Post("/email-template", h.configurationEmailHandler.CreateTemplate)
	configurationEmailRoute.Get("/email-template/:id", h.configurationEmailHandler.GetTemplate)
	configurationEmailRoute.Put("/email-template/:id", h.configurationEmailHandler.UpdateTemplate)
	configurationEmailRoute.Delete("/email-template/:id", h.configurationEmailHandler.DeleteTemplate)
	configurationEmailRoute.Get("/email-template/partner/:company_id", h.configurationEmailHandler.GetPartnersByCompanyId)

	configurationEmailRoute.Get("/email-logs", h.configurationEmailLogHandler.GetEmailLogs)
	configurationEmailRoute.Get("/email-logs/:id", h.configurationEmailLogHandler.GetEmailLogDetail)
	configurationEmailRoute.Post("/email-logs/:id/resend", h.configurationEmailLogHandler.ResendEmail)

	webhookRouter := f.Group("/webhooks")
	webhookRouter.Get("", h.webhookHandler.GetWebhookDeliveryLogs)
	webhookRouter.Get("/:id/histories", h.webhookHandler.GetWebhookDeliveryLogHistory)
	webhookRouter.Get("/:id", h.webhookHandler.GetWebhookDeliveryLogDetail)
}

func (h *rooters) SetupOpenAPI(r fiber.Router) {
	// Open API
	f := r.Group("/api")

	// Product
	f.Post("/product", h.companyProductHandler.CreateCompanyProductAPI)
	f.Put("/product/:id", h.companyProductHandler.UpdateCompanyProductAPI)
	f.Delete("/product/:id", h.companyProductHandler.DeleteCompanyProductAPI)
	f.Get("/list/product", h.companyProductHandler.GetListCompanyProductAPI)
	f.Get("/product/code/:code", h.companyProductHandler.GetCompanyProductCodeAPI)
	f.Get("/product/code/:code/channel/:channelCode", h.companyProductProviderChannelMappingHandler.GetOneWithDetailOpenAPI)
	f.Post("/product/code/:code/channel/:channelCode/calculate", h.companyProductProviderChannelMappingHandler.CalculateOpenAPI)

	// Payment cash in
	f.Post("/payment", h.cashinHandler.CreateCashinTransactionAPI)
	f.Put("/payment/refund", h.cashinHandler.RefundCashinTransactionAPI)
	f.Put("/payment/:provider_transcation_id/cancel", h.cashinHandler.CancelCashinTransactionAPI)
	f.Put("/payment/:provider_transcation_id/switch", h.cashinHandler.SwitchCashinPaymentChannelAPI)
	// f.Put("/payment/:id", h.cashinHandler.UpdateCashinTransactionAPI)
	f.Get("/list/payment", h.cashinHandler.GetListCashinTransactionAPI)
	f.Get("/list/payment/method", h.companyMappingHandler.GetListMappingCashInPaymentMethodAPI)
	f.Get("/payment/:id", h.cashinHandler.CheckStatusCashinAPI)
	f.Post("/payment/check-bill/va", h.cashinHandler.CheckUnpaidBillVA)
	f.Get("/payment-instructions", h.cashinHandler.GetPaymentInstructions)

	// Payment cash out
	f.Get("/list/disbursement", h.cashOutHandler.ListAPI)
	f.Get("/disbursement/:id", h.cashOutHandler.CheckStatusCashOutAPI)

	// Partner
	f.Post("/partner", h.partnerHandler.CreatePartnerAPI)
	f.Put("/partner/:id", h.partnerHandler.UpdatePartnerAPI)
	f.Delete("/partner/:id", h.partnerHandler.DeletePartnerAPI)
	f.Get("/list/partner", h.partnerHandler.GetListPartnerAPI)
	f.Get("/partner/code/:code", h.partnerHandler.GetPartnerCodeAPI)

	// Channel
	f.Get("/list/channel-mapping", h.companyMappingHandler.GetListAllCompanyProviderMappingChannelAPI)
	f.Get("/channel-mapping/code/:code", h.companyMappingHandler.GetCompanyProviderMappingCodeAPI)
}

func (h *rooters) SetupCallback(r *fiber.App, trxLogReqUc usecase.TrxRequestLogUseCase) {
	// The`CallbackXfers` function from the `cashinHandler` is called to handle the request.
	r.Post("/xfers/callback", h.cashinHandler.CallbackXfers)

	// The`CallbackNicepay` function from the `cashinHandler` is called to handle the request.
	r.Post("/nicepay/callback", h.cashinHandler.CallbackNicePay)
	r.Post("/nicepay/snap/callback", h.cashinHandler.CallbackSnapNicePay)

	// The`CallbackXendit` function from the `cashinHandler` is called to handle the request.
	r.Post("/xendit/va/callback", h.cashinHandler.CallbackXenditVA)
	r.Post("/xendit/ewallet/callback", h.cashinHandler.CallbackXenditEwallet)
	r.Post("/xendit/invoice/callback", h.cashinHandler.CallbackXenditInvoice)
	r.Post("/xendit/fixedPaymentCode/callback", h.cashinHandler.CallbackXenditFixedPaymentCode)
	r.Post("/xendit/cashout/callback", h.cashOutHandler.CallbackXenditDisburse)

	// The`CallbackXOttoCash` function from the `cashinHandler` is called to handle the request.
	r.Post("/ottocash/invoice/callback", h.cashinHandler.CallbackOttocash)
	r.Post("/doku/va/callback", h.cashinHandler.CallbackDokuVA)
	r.Post("/cashlez/offlinePg/callback", h.cashinHandler.CallbackCashlezOfflinePg) //callback will be trigger from mobile -> client payment gateway -> payment system

	r.Post("/email/callback", h.configurationEmailLogHandler.EmailWebhook)
}

func (h *rooters) SetupSnapAPI(r fiber.Router) {
	timeoutDuration := config.GetDuration("snap.timeout_seconds") * time.Second

	v1 := r.Group("/v1.0")
	v1.Use(h.snapHandler.ParseHeader())

	if config.GetString("env") != "production" {
		utilities := v1.Group("/utilities")
		utilities.Post("/signature-auth", h.snapHandler.GenerateSignatureAuth)
		utilities.Post("/signature-service", h.snapHandler.GenerateSignatureService)
	}

	accesToken := v1.Group("/access-token")
	accesToken.Group("/b2b").Use(h.snapHandler.InitMd("73", timeoutDuration), h.snapHandler.VerifyAsymetricSignature()).Post("", h.snapHandler.GetAccessTokenB2B)

	// transfer va
	transferVa := v1.Group("/transfer-va")
	transferVa.Group("/inquiry").Use(h.snapHandler.InitMd("24", timeoutDuration), h.snapHandler.VerifySymetricSignature()).Post("", h.snapHandler.TransferVaInquiry)
	transferVa.Group("/payment").Use(h.snapHandler.InitMd("25", timeoutDuration), h.snapHandler.VerifySymetricSignature()).Post("", h.snapHandler.TransferVaPayment)
}
