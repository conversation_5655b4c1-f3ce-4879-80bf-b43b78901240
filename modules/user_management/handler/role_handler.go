package handler

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

func (h *UserManagementHandler) CreateRole(c *fiber.Ctx) (err error) {
	var req domain.Roles
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.CreateRole(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) UpdateRole(c *fiber.Ctx) (err error) {
	var req domain.Roles
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.UpdateRole(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) UpdateStatusRole(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, "invalid id")
	}

	req := new(request)
	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.UpdateStatusRole(c.UserContext(), id, req.Status)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}

func (h *UserManagementHandler) GetRoleByID(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.userService.GetRoleById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *UserManagementHandler) DeleteRole(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.userService.DeleteRole(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *UserManagementHandler) SearchRoles(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, err.Error())
		return
	}
	company, totalData, err := h.userService.SearchRoles(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, company)
}

func (h *UserManagementHandler) GetListAllRoles(c *fiber.Ctx) (err error) {
	status := c.Query("status")
	resp, err := h.userService.GetListAllRole(c.UserContext(), status)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}
