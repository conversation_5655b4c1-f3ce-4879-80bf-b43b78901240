package snapclient

import (
	"context"
	"encoding/base64"
	"time"

	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
)

type iCredential interface {
	GetCred() Credential
}

var _ iCredential = (*Credential)(nil)

type Credential struct {
	ClientID         string `json:"client_id"`
	ClientSecret     string `json:"client_secret"`
	PrivateKeyBase64 string `json:"private_key_base64"`
}

func (c Credential) GetCred() Credential {
	return c
}

func (c Credential) PrivateKey() string {
	privKey, _ := base64.StdEncoding.DecodeString(c.PrivateKeyBase64)
	return string(privKey)
}

type AccessTokenB2BReq struct {
	GrantType string `json:"grantType"`
}

type AccessTokenB2BResp struct {
	BaseResp
	AccessToken string `json:"accessToken"`
	TokenType   string `json:"tokenType"`
	ExpiresIn   string `json:"expiresIn"`
}

func (w *Wrapper) AccessTokenB2B(ctx context.Context, cred iCredential, req AccessTokenB2BReq) (resp AccessTokenB2BResp, err error) {
	headers, err := w.accessTokenHeaders(ctx, cred)
	if err != nil {
		logger.Error(ctx, "error get access token headers", logger.Err(err))
		return
	}

	doReq := DoReq{
		Header: headers,
		Method: http.MethodPost,
		Path:   w.RPaths.AccessTokenB2B,
	}
	doReq.ReqBody = req

	_, _, err = w.do(ctx, doReq, &resp)
	if err != nil {
		return
	}

	return
}

func (w *Wrapper) GetB2BToken(ctx context.Context, icred iCredential) (accessToken string, err error) {
	w.clientTokensMutex.Lock()
	defer w.clientTokensMutex.Unlock()
	cred := icred.GetCred()
	token, exist := w.clientTokens[cred.ClientID]
	// if token exist and not expired
	if exist && token.expiredAt.After(w.now()) {
		return token.accessToken, nil
	}
	// get token
	accessTokenResp, err := w.AccessTokenB2B(ctx, cred, AccessTokenB2BReq{
		GrantType: "client_credentials",
	})
	if err != nil {
		logger.Error(ctx, "error get access token", logger.Err(err))
		return "", err
	}
	accessToken = accessTokenResp.AccessToken
	w.clientTokens[cred.ClientID] = clientToken{
		accessToken: accessToken,
		expiredAt:   w.now().Add(10 * time.Minute), // save for 10 minutes make sure always have not expired token
	}

	return accessToken, nil
}
