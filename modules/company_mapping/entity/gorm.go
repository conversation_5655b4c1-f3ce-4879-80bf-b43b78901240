package entity

import (
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
)

type CustomerFVAGORM struct {
	ID                                     int64     `gorm:"column:id"`
	CompanyPaymentProviderChannelMappingId int64     `gorm:"column:company_payment_provider_channel_mapping_id"`
	Counter                                int64     `gorm:"counter"`
	VANumber                               string    `gorm:"va_number"`
	Name                                   string    `gorm:"column:name"`
	Phone                                  string    `gorm:"column:phone"`
	LastPgReferenceId                      string    `gorm:"column:last_pg_reference_id"`
	ExpiredAt                              time.Time `gorm:"column:expired_at"`
	CreatedAt                              time.Time `gorm:"column:created_at"`
	UpdatedAt                              time.Time `gorm:"column:updated_at"`
}

func (g CustomerFVAGORM) ToDomain() domain.FixedVACustomer {
	return domain.FixedVACustomer{
		ID:                                     g.ID,
		VANumber:                               g.VANumber,
		Counter:                                g.Counter,
		CompanyPaymentProviderChannelMappingId: g.CompanyPaymentProviderChannelMappingId,
		Name:                                   g.Name,
		Phone:                                  g.Phone,
		LastPgReferenceId:                      g.LastPgReferenceId,
		ExpiredAt:                              g.ExpiredAt,
		CreatedAt:                              g.Created<PERSON>t,
		UpdatedAt:                              g.UpdatedAt,
	}
}

func (g *CustomerFVAGORM) FromDomain(d *domain.FixedVACustomer) {
	g.ID = d.ID
	g.CompanyPaymentProviderChannelMappingId = d.CompanyPaymentProviderChannelMappingId
	g.Counter = d.Counter
	g.VANumber = d.VANumber
	g.Name = d.Name
	g.Phone = d.Phone
	g.LastPgReferenceId = d.LastPgReferenceId
	g.ExpiredAt = d.ExpiredAt
	g.CreatedAt = d.CreatedAt
	g.UpdatedAt = d.UpdatedAt
}

func (CustomerFVAGORM) TableName() string {
	return "fva_customers"
}

type CounterGORM struct {
	ID                                     int       `gorm:"column:id"`
	CompanyPaymentProviderChannelMappingId int       `gorm:"column:company_payment_provider_channel_mapping_id"`
	Type                                   string    `gorm:"column:type"`
	Counter                                int64     `gorm:"column:counter"`
	CreatedAt                              time.Time `gorm:"column:created_at"`
	UpdatedAt                              time.Time `gorm:"column:updated_at"`
}

func (g CounterGORM) ToDomain() domain.CompanyPaymentProviderChannelMappingCounter {
	return domain.CompanyPaymentProviderChannelMappingCounter{
		ID:                                     g.ID,
		CompanyPaymentProviderChannelMappingId: g.CompanyPaymentProviderChannelMappingId,
		Type:                                   g.Type,
		Counter:                                g.Counter,
		CreatedAt:                              g.CreatedAt,
		UpdatedAt:                              g.UpdatedAt,
	}
}

func (g *CounterGORM) FromDomain(d *domain.CompanyPaymentProviderChannelMappingCounter) {
	g.ID = d.ID
	g.CompanyPaymentProviderChannelMappingId = d.CompanyPaymentProviderChannelMappingId
	g.Type = d.Type
	g.Counter = d.Counter
	g.CreatedAt = d.CreatedAt
	g.UpdatedAt = d.UpdatedAt
}

func (CounterGORM) TableName() string {
	return "company_payment_provider_channel_mapping_counters"
}
