{"debug": true, "env": "development", "port": "6081", "host": "0.0.0.0", "appName": "payment-service", "version": "v1", "jwtKey": "SW5wpvMagKRarbuHm4m84buhh6b3z8Ebf5YfnGSheERce9xcxHzXT9UtgaGgVfQM", "basic_secret_key": "TyRsp573Frs9Ljd905WEnD6LiP754", "secret_key_company": "24Hts87532KlstrdsNgrys973Op34GhnEwL", "user_key": "gtwiTr6398sGrwis98w0LskdgnDwqrEewQV2cbn", "login_key": "ti87DEpQwNm234bYWQni983Bc75PsTWm", "tz": "Asia/Jakarta", "db": {"host": "pgbouncer", "port": "5432", "name": "payment", "user": "payment", "pass": "CandyPayPalsBejeweling", "ssl": "disable", "timezone": "Asia/Jakarta", "maxIdle": 20, "maxOpenConn": 50, "maxTimeIdleConn": 30, "searchpath": "public", "debug": true}, "rabbit": {"host": "rabbitmq", "port": "5672", "user": "payment", "password": "DentistContradistinctionsBurner", "exchange_name": "service", "queue_name": "service-worker", "routing_key": "service.*"}, "redis": {"host": "redis", "port": "6379", "user": "", "password": "", "db": "0"}, "logger": {"file": true, "fileLocation": "./logs", "fileMaxAge": 30, "stdout": true}, "xfers": {"host": "", "sandbox": "https://sandbox-id.xfers.com/api/v4", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "nicepay": {"host": "https://www.nicepay.co.id/nicepay", "sandbox": "https://staging.nicepay.co.id/nicepay", "callback_url": "https://api-payment.nusatek.dev/nicepay/callback", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "xendit": {"host": "", "sandbox": "https://api.xendit.co", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "ottocash": {"host": "", "sandbox": "https://wolverine-dev.ottodigital.id/securepage/v2/rest/h2h/payment", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true, "callback_success_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback", "callback_failed_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback", "callback_back_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback"}, "available_providers": ["Ottocash", "NicePay", "Xfers", "Xendit", "Midtrans"], "s3": {"host": "ap-south-1.linodeobjects.com", "region": "ap-south-1", "access_key": "NQ62841V20A029LWGUOV", "secret_key": "wr7mcFA8oBTbEHAaUgOD4ECAQcrgsoqsA6VJtjyw", "bucket": "nusatek-payment-dev"}}