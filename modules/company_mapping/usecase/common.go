package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/model"
)

func getSecretTemplateValidation(ctx context.Context, secretTempRequest, secretTempDB string) (secretEncry string, err error) {
	if secretTempDB == "" {
		err = errors.SetErrorMessage(http.StatusForbidden, "secret template not set yet")
		logger.Error(ctx, err.Error())
	}
	var secretTempDb []model.SecretProviderTemplate
	err = json.Unmarshal([]byte(secretTempDB), &secretTempDb)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var secretTempReq = make(map[string]interface{})
	err = json.Unmarshal([]byte(secretTempRequest), &secretTempReq)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var secret = make(map[string]interface{})
	counter := 0
	for i := 0; i < len(secretTempDb); i++ {
		for key, val := range secretTempReq {
			if secretTempDb[i].Name == strings.ToLower(key) {
				counter = counter + 1
				secret[strings.ToLower(key)] = fmt.Sprintf("%v", val)
			}
		}
		if counter == 0 {
			err = errors.SetErrorMessage(http.StatusBadRequest, "incomplete secret data")
			logger.Error(ctx, err.Error())
			return
		}
	}

	secretByte, _ := json.Marshal(secret)
	secrets, errRes := utils.EncryptAES(string(secretByte), config.GetString("secret_key_company"))
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	secretEncry = secrets
	return
}

func (s *defaultCompanyMappingUsecase) getCacheCompanyPaymentProviderMappings(ctx context.Context, companyId, providerId string) (resp *domain.CompanyPaymentProviderMappings, err error) {
	key := fmt.Sprintf("%v-%v-%v", constants.CacheCompanyPaymentProviderMapping, companyId, providerId)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	switch true {
	case cache == "":
		prod, errRes := s.companyMappingRepo.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "provider not found in company")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCompanyPaymentProviderMapping(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.CompanyPaymentProviderMappings
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCompanyMappingUsecase) setCacheCompanyPaymentProviderMapping(ctx context.Context, req *domain.CompanyPaymentProviderMappings) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v-%v", constants.CacheCompanyPaymentProviderMapping, req.CompanyID, req.PaymentProviderID)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *defaultCompanyMappingUsecase) deleteCacheCompanyPaymentProviderMapping(ctx context.Context, companyId, providerId string) (err error) {
	key := fmt.Sprintf("%v-%v-%v", constants.CacheCompanyPaymentProviderMapping, companyId, providerId)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}
