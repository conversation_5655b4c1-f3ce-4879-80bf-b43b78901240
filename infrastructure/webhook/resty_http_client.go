package webhook

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/go-resty/resty/v2"
)

// HTTPMetrics tracks HTTP client metrics
type HTTPMetrics struct {
	RequestCount    int64
	SuccessCount    int64
	FailureCount    int64
	CircuitBreaks   int64
	LastRequestTime time.Time
	LastErrorTime   time.Time
	mu              sync.RWMutex
}

type CircuitState int

const (
	StateClosed   CircuitState = iota // Normal operation
	StateOpen                         // Failing, reject requests
	StateHalfOpen                     // Testing if service is healthy
)

// RestyHTTPClient implements HTTPClient with advanced features using resty
type RestyHTTPClient struct {
	client        *resty.Client
	config        HTTPClientConfig
	circuitConfig CircuitBreakerConfig
	metrics       *HTTPMetrics
	mu            sync.RWMutex
	failures      int
	lastFailure   time.Time
	state         CircuitState
	debug         bool // Debug mode flag
}

// convertHeaders converts http.Header to a map[string]string
func convertHeaders(headers http.Header) map[string]string {
	converted := make(map[string]string)
	for key, values := range headers {
		if len(values) > 0 {
			converted[key] = values[0]
		}
	}
	return converted
}

// NewRestyHTTPClient creates a new HTTP client using resty
func NewRestyHTTPClient(config HTTPClientConfig, circuitConfig CircuitBreakerConfig) *RestyHTTPClient {
	client := resty.New().
		SetTimeout(config.Timeout).
		SetTransport(&http.Transport{
			MaxIdleConns:      config.MaxIdleConns,
			IdleConnTimeout:   config.IdleConnTimeout,
			DisableKeepAlives: config.DisableKeepAlives,
		}).
		SetRetryCount(1). // Reduced retry count for DNS errors
		SetRetryWaitTime(50 * time.Millisecond).
		SetRetryMaxWaitTime(100 * time.Millisecond). // Shorter max wait time
		AddRetryCondition(
			func(r *resty.Response, err error) bool {
				// Only retry on server errors (500+)
				if err != nil {
					// Don't retry DNS errors
					if strings.Contains(err.Error(), "lookup") && strings.Contains(err.Error(), "no such host") {
						return false
					}
					return true
				}
				return r.StatusCode() >= 500
			},
		)

	return &RestyHTTPClient{
		client:        client,
		config:        config,
		circuitConfig: circuitConfig,
		metrics:       &HTTPMetrics{},
		state:         StateClosed,
		debug:         false, // Debug mode is off by default
	}
}

// SetDebugMode enables or disables debug mode
func (c *RestyHTTPClient) SetDebugMode(debug bool) {
	c.client.SetDebug(debug)
	c.debug = debug
}

// Do implements HTTPClient interface with circuit breaker, retries, and metrics
func (c *RestyHTTPClient) Do(req *http.Request) (*http.Response, error) {
	// Update metrics
	c.metrics.mu.Lock()
	c.metrics.RequestCount++
	c.metrics.LastRequestTime = time.Now()
	c.metrics.mu.Unlock()

	if !c.canRequest() {
		c.metrics.mu.Lock()
		c.metrics.CircuitBreaks++
		c.metrics.mu.Unlock()
		return nil, fmt.Errorf("circuit breaker is open")
	}

	// Convert *http.Request to resty.Request
	restyReq := c.client.R().
		SetContext(req.Context()).
		SetHeaders(convertHeaders(req.Header)).
		SetBody(req.Body)

	// Execute the request with retries and backoff
	var resp *resty.Response
	var err error

	operation := func() error {
		resp, err = restyReq.Execute(req.Method, req.URL.String())
		if err != nil {
			c.recordFailure()
			return err
		}

		if resp.StatusCode() >= 500 {
			c.recordFailure()
			return fmt.Errorf("server error: %d", resp.StatusCode())
		}

		c.recordSuccess()
		return nil
	}

	// Use exponential backoff for retries
	b := backoff.NewExponentialBackOff()
	b.InitialInterval = 100 * time.Millisecond
	b.MaxInterval = 2 * time.Second
	b.MaxElapsedTime = c.config.Timeout

	err = backoff.Retry(operation, b)
	if err != nil {
		// Handle DNS errors specifically
		if strings.Contains(err.Error(), "lookup") && strings.Contains(err.Error(), "no such host") {
			return &http.Response{
				StatusCode: http.StatusBadGateway,
				Header:     make(http.Header),
				Body: io.NopCloser(bytes.NewReader([]byte(fmt.Sprintf(
					`{"error": "DNS resolution failed", "message": "%s"}`,
					err.Error(),
				)))),
			}, nil
		}
		return nil, err
	}

	// Convert resty.Response to *http.Response
	httpResp := &http.Response{
		StatusCode: resp.StatusCode(),
		Header:     resp.Header(),
	}

	// Handle response body
	if resp.RawResponse != nil && resp.RawResponse.Body != nil {
		// Create a NopCloser from the response bytes to allow multiple reads
		bodyBytes := resp.Body()
		httpResp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	} else {
		// Create an empty reader if no body exists
		httpResp.Body = io.NopCloser(bytes.NewReader([]byte{}))
	}

	return httpResp, nil
}

func (c *RestyHTTPClient) canRequest() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	switch c.state {
	case StateClosed:
		return true
	case StateOpen:
		// Check if enough time has passed to try again
		if time.Since(c.lastFailure) > c.circuitConfig.ResetTimeout {
			c.state = StateHalfOpen
			return true
		}
		return false
	case StateHalfOpen:
		return true
	default:
		return true
	}
}

func (c *RestyHTTPClient) recordSuccess() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.failures = 0
	c.state = StateClosed

	c.metrics.mu.Lock()
	c.metrics.SuccessCount++
	c.metrics.mu.Unlock()
}

func (c *RestyHTTPClient) recordFailure() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.failures++
	c.lastFailure = time.Now()

	// Check if we should open the circuit
	if c.failures >= c.circuitConfig.MaxFailures {
		c.state = StateOpen
	}

	c.metrics.mu.Lock()
	c.metrics.FailureCount++
	c.metrics.LastErrorTime = c.lastFailure
	c.metrics.mu.Unlock()
}

// GetMetrics returns current metrics
func (c *RestyHTTPClient) GetMetrics() HTTPMetrics {
	c.metrics.mu.RLock()
	defer c.metrics.mu.RUnlock()

	// Return a copy without the mutex
	return HTTPMetrics{
		RequestCount:    c.metrics.RequestCount,
		SuccessCount:    c.metrics.SuccessCount,
		FailureCount:    c.metrics.FailureCount,
		CircuitBreaks:   c.metrics.CircuitBreaks,
		LastRequestTime: c.metrics.LastRequestTime,
		LastErrorTime:   c.metrics.LastErrorTime,
	}
}
