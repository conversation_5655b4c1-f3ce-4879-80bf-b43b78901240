-- Create EmailLog table
CREATE TABLE IF NOT EXISTS email_logs (
    id BIGSERIAL PRIMARY KEY,
    company_id BIGINT NOT NULL,
    partner_id BIGINT NOT NULL,
    batch_number VARCHAR(255),
    email VARCHAR(255) NOT NULL,
    send_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(50) NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- Add foreign key constraints
ALTER TABLE email_logs
ADD CONSTRAINT fk_email_logs_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE;
ALTER TABLE email_logs
ADD CONSTRAINT fk_email_logs_partner FOR<PERSON>GN KEY (partner_id) REFERENCES partners(id) ON DELETE CASCADE;
-- Create indexes for better query performance
CREATE INDEX idx_email_logs_company_id ON email_logs(company_id);
CREATE INDEX idx_email_logs_partner_id ON email_logs(partner_id);
CREATE INDEX idx_email_logs_batch_number ON email_logs(batch_number);
CREATE INDEX idx_email_logs_status ON email_logs(status);
CREATE INDEX idx_email_logs_send_date ON email_logs(send_date);
-- Add a comment to the table
COMMENT ON TABLE email_logs IS 'Stores logs of emails sent through the system';
-- Add comments to the columns
COMMENT ON COLUMN email_logs.id IS 'Unique identifier for the email log';
COMMENT ON COLUMN email_logs.company_id IS 'ID of the company associated with this email log';
COMMENT ON COLUMN email_logs.partner_id IS 'ID of the partner associated with this email log';
COMMENT ON COLUMN email_logs.batch_number IS 'Batch number for grouped emails';
COMMENT ON COLUMN email_logs.email IS 'Email address of the recipient';
COMMENT ON COLUMN email_logs.send_date IS 'Date and time when the email was sent';
COMMENT ON COLUMN email_logs.status IS 'Current status of the email (pending, sent, failed, opened, clicked)';
COMMENT ON COLUMN email_logs.error_message IS 'Error message if the email failed to send';
COMMENT ON COLUMN email_logs.created_at IS 'Timestamp when the log entry was created';