package handler

import (
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/entity"
	"repo.nusatek.id/nusatek/payment/modules/payment_provider/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type PaymentProviderHandler struct {
	paymentProviderService usecase.PaymentProviderUsecase
}

func NewHandler(companyProductService usecase.PaymentProviderUsecase) *PaymentProviderHandler {
	return &PaymentProviderHandler{companyProductService}
}

func (h *PaymentProviderHandler) CreatePaymentProvider(c *fiber.Ctx) (err error) {
	var req entity.PaymentProvidersShow
	err = c.<PERSON>er(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.paymentProviderService.CreatePaymentProvider(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *PaymentProviderHandler) UpdatePaymentProvider(c *fiber.Ctx) (err error) {
	var req entity.PaymentProvidersShow
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.paymentProviderService.UpdatePaymentProvider(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *PaymentProviderHandler) DeletePaymentProvider(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.paymentProviderService.DeletePaymentProvider(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *PaymentProviderHandler) GetPaymentProviderById(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.paymentProviderService.GetPaymentProviderByIdShow(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PaymentProviderHandler) SearchPaymentProvider(c *fiber.Ctx) (err error) {
	var paginate utils.Pagination
	err = c.QueryParser(&paginate)
	if err != nil {
		err = errors.SetError(http.StatusBadRequest, err.Error())
		return
	}
	tr := strings.ReplaceAll(paginate.Value, " ", "&")
	paginate.Value = tr
	resp, totalData, err := h.paymentProviderService.SearchPaymentProvider(c.UserContext(), paginate)
	if err != nil {
		return
	}
	return response.HandleSuccessWithPagination(c, float64(totalData), paginate, resp)
}

func (h *PaymentProviderHandler) GetPaymentProviderListAll(c *fiber.Ctx) (err error) {
	key := c.Query("status")
	resp, _, err := h.paymentProviderService.GetPaymentProviderListAll(c.UserContext(), key)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}

func (h *PaymentProviderHandler) GetPaymentProviderDefaults(c *fiber.Ctx) (err error) {
	resp := h.paymentProviderService.DefaultPaymentProvider(c.UserContext())
	return response.HandleSuccess(c, resp)
}

func (h *PaymentProviderHandler) UpdateStatus(c *fiber.Ctx) (err error) {
	type request struct {
		Status bool `json:"status"`
	}

	id := c.Params("id")
	req := new(request)

	if err = c.BodyParser(req); err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}

	if err = h.paymentProviderService.UpdateStatus(c.UserContext(), id, req.Status); err != nil {
		return
	}

	return response.HandleSuccess(c, map[string]interface{}{
		"id":     id,
		"status": req.Status,
	})
}
