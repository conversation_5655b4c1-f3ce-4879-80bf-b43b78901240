package snaputil

import (
	"net/http"
	"testing"
)

const (
	privKeyTest = `*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
	pubKeyTest = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtatInMYNr4JY7l46SD6VJ1gC/TbEVrRTFeBRR60togv9Uuw/DNZ9W/XYuwuU2LzanKJSLS4QNOlzIJu1RetaWR4vAgospls2vT71O/Q3wvVjRXQaYEsWouHRQlcWADPHbt1XH5U+KmD20Lx1WHcswgBJdv+n3nFbJKdycqb7Gb5mjrO
dzxXSKaBECHBd2Cvl4OFhopDRLoBpzhUoKOtFnfeibOkaOEDXYJlD5e7r6xyem0p3DfyJcH9Xixi+m1NOJYDBZh8CnSAGhELmWC4zDdaTaAiwxRXSf2AFKMtN+tOFfyFzYikYK0fbhkiPCmBe8CTZ9Mu+nOl+g6W/H2vvwwIDAQAB
-----END PUBLIC KEY-----`
)

func TestGenerateAsymetricSignature(t *testing.T) {
	type args struct {
		privPEM      string
		clientKey    string
		timestampStr string
	}
	tests := []struct {
		name     string
		args     args
		wantSign string
		wantErr  bool
	}{
		{
			name: "case 1",
			args: args{
				privPEM:      privKeyTest,
				clientKey:    "b66925de-d8ec-476e-a170-6cf06c863b78",
				timestampStr: "2017-03-17T09:44:18+07:00",
			},
			wantSign: "p2Pm61e/7MeQvDPEW84OcpGvGIIEjwc/2vHm6VoFcA7sBjLH6ZILrDnFwhDHq3yy07pfhX/MRZC9oTVP8d94o0NQigCew7gR2pj/rVwNXWbwqJ3KuFnQuYH+QfTkqLRlhwo4PIifjJyvHIBJFPLyhnSktL7uG6gr+fAtRtTbebutgOuQRdcpzkav8kimFIA0FfTZP5Rv5SBv4wf24nyeao249skpJ2iCrzfTTX0eIY2dyMYDPLtWDD7SXY3VwrwlKKRBQgmNa0j3tlGvZvFEXgk4QFMh3LgLdhKoJcu4y2E2YYuHwSi0kf76m/cVQGytoLrvwT6uiA9HEUmmRuAeNA==",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSign, err := GenerateAsymetricSignature(tt.args.privPEM, tt.args.clientKey, tt.args.timestampStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateAsymetricSignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSign != tt.wantSign {
				t.Errorf("GenerateAsymetricSignature() = %v, want %v", gotSign, tt.wantSign)
			}
		})
	}
}

func TestVerifyAsymetricSignature(t *testing.T) {
	type args struct {
		pubPEM       string
		clientKey    string
		timestampStr string
		signatureMsg string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "case 1",
			args: args{
				pubPEM:       pubKeyTest,
				clientKey:    "b66925de-d8ec-476e-a170-6cf06c863b78",
				timestampStr: "2017-03-17T09:44:18+07:00",
				signatureMsg: "p2Pm61e/7MeQvDPEW84OcpGvGIIEjwc/2vHm6VoFcA7sBjLH6ZILrDnFwhDHq3yy07pfhX/MRZC9oTVP8d94o0NQigCew7gR2pj/rVwNXWbwqJ3KuFnQuYH+QfTkqLRlhwo4PIifjJyvHIBJFPLyhnSktL7uG6gr+fAtRtTbebutgOuQRdcpzkav8kimFIA0FfTZP5Rv5SBv4wf24nyeao249skpJ2iCrzfTTX0eIY2dyMYDPLtWDD7SXY3VwrwlKKRBQgmNa0j3tlGvZvFEXgk4QFMh3LgLdhKoJcu4y2E2YYuHwSi0kf76m/cVQGytoLrvwT6uiA9HEUmmRuAeNA==",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := VerifyAsymetricSignature(tt.args.pubPEM, tt.args.clientKey, tt.args.timestampStr, tt.args.signatureMsg); (err != nil) != tt.wantErr {
				t.Errorf("VerifyAsymetricSignature() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGenerateSymetricSignature(t *testing.T) {
	type args struct {
		accessToken   string
		httpMethod    string
		path          string
		reqBody       string
		xtimestampStr string
		clientSecret  string
	}
	tests := []struct {
		name     string
		args     args
		wantSign string
		wantErr  bool
	}{
		{
			name: "case 1",
			args: args{
				accessToken:   "gp9HjjEj813Y9JGoqwOeOPWbnt4CUpvIJbU1mMU4a11MNDZ7Sg5u9a",
				httpMethod:    http.MethodGet,
				path:          "/banking/v2/corporates/h2hauto009/accounts/**********",
				reqBody:       "",
				xtimestampStr: "2017-03-17T09:44:18+07:00",
				clientSecret:  "efc71ced-b0e7-4b47-8270-3c24829764aa",
			},
			wantSign: "WTiG3F4mgq4ZTjjk44+MCOKWFeQGwCW3gQcKJOIje3xGmVHpJYvwYPcBzXwLqC8Bc+6qVCdTvFk7hX1c3Eb8wA==",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSign, err := GenerateSymetricSignature(tt.args.accessToken, tt.args.httpMethod, tt.args.path, tt.args.reqBody, tt.args.xtimestampStr, tt.args.clientSecret)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateSymetricSignature() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSign != tt.wantSign {
				t.Errorf("GenerateSymetricSignature() = %v, want %v", gotSign, tt.wantSign)
			}
		})
	}
}
