package usecase

import (
	"context"

	"github.com/golang-jwt/jwt/v4"
	"repo.nusatek.id/nusatek/payment/domain"
)

// due to prevent import cycle, instead create same function i prefer import the function from another repo here and make contract by function type
type RepoFuncWrapper struct {
	GetCashinTransactionById            func(ctx context.Context, id string) (resp *domain.CashInTransactions, err error)
	GetCashinTranscationItemByCashinId  func(ctx context.Context, id string) (resp *[]domain.CashInTransactionItems, err error)
	GetChannelMappingByCode             func(ctx context.Context, code string) (resp *domain.PaymentProviderChannelMappings, err error)
	GetLastCashinHistoryByVaAndStatus   func(ctx context.Context, va string, status string) (resp *domain.CashInTransactionHistories, err error)
	GetCashinTransactionByInvoiceNumber func(ctx context.Context, invoiceNumber string) (resp *domain.CashInTransactions, err error)
}

type Repository interface {
	FnWrapper() RepoFuncWrapper
	GetOneClient(ctx context.Context, clientId string) (res domain.SnapClient, err error)
	GetOneCompanyProductProviderChannelMappingBySnapCompCode(ctx context.Context, snapCompCode string) (res domain.CompanyPaymentProviderChannelMappings, err error)
	GetOneCustomerVaByNumber(ctx context.Context, mappingId int64, vaNumber string) (res domain.FixedVACustomer, err error)
	InsertVaRequestId(ctx context.Context, req *domain.SnapVaRequestId) (err error)
	GetLastVaRequestId(ctx context.Context, req domain.SnapVaRequestId) (res domain.SnapVaRequestId, err error)
}

type UcFuncWrapper struct {
	UpdateStatusSnapCashinTransaction func(ctx context.Context, cashIn *domain.CashInTransactions, cbData domain.SnapCbCashInTransaction, opts domain.UpdateWachingStatusCashinOpts) error
}

type Usecase interface {
	GenerateSignatureAuth(ctx context.Context, req domain.SnapGenerateSignatureAuthReq) (resp domain.SnapGenerateSignatureAuthResp, err error)
	GenerateSignatureService(ctx context.Context, req domain.SnapGenerateSignatureServiceReq) (resp domain.SnapGenerateSignatureServiceResp, err error)
	GetOneClient(ctx context.Context, clientId string) (res domain.SnapClient, err error)
	GetAccessTokenB2B(ctx context.Context, req domain.SnapAccesTokenB2BReq) (resp domain.SnapAccesTokenB2BResp, err error)
	ValidateToken(ctx context.Context, accessToken string) (claims *jwt.RegisteredClaims, err error)
	TransferVaInquiry(ctx context.Context, req domain.SnapTransferVaInquiryReq) (resp domain.SnapTransferVaInquiryResp, err error)
	TransferVaPayment(ctx context.Context, req domain.SnapTransferVaPaymentReq) (resp domain.SnapTransferVaPaymentResp, err error)
}
