package domain

import (
	"time"

	"gorm.io/gorm"
)

type CompanyCashFlows struct {
	ID                    int            `gorm:"primaryKey" json:"id"`
	CompanyID             int            `json:"company_id"`
	CashInTransactionID   int            `json:"cash_in_transaction_id"`
	CashOutTransactionID  int            `json:"cash_out_transaction_id"`
	Debit                 float64        `json:"debit"`
	Credit                float64        `json:"credit"`
	Description           string         `json:"description"`
	AdditionalInformation string         `gorm:"type:jsob; not null" json:"additional_information"`
	CreatedAt             time.Time      `json:"created_at"`
	UpdatedAt             time.Time      `json:"updated_at"`
	DeletedAt             gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
