package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/oklog/ulid"
)

func GenerateThreadId() string {
	t := time.Now()
	entropy := rand.New(rand.NewSource(t.UnixNano()))
	uniqueID := ulid.MustNew(ulid.Timestamp(t), entropy)
	return uniqueID.String()
}

func EncryptSHA256(secret string) string {
	h := sha256.Sum256([]byte(secret))
	return base64.StdEncoding.EncodeToString(h[:])
}

func ParseUnixTime(timeUnix string) (string, error) {
	t, err := strconv.ParseInt(timeUnix, 10, 64)
	if err != nil {
		err = fmt.Errorf("failed parse unix time %v", err.Error())
		return "", err
	}
	return time.Unix(t, 0).Format(time.RFC3339Nano), nil
}

func RedactKey(encodedKey string, visibleLength int) string {
	keyLength := len(encodedKey)
	if visibleLength >= keyLength {
		return encodedKey
	}
	// Calculate the number of characters to redact
	redactLength := keyLength - visibleLength
	redactedPart := strings.Repeat("*", 32)
	return redactedPart + encodedKey[redactLength:]
}
