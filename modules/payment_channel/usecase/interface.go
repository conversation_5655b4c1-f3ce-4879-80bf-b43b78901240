package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_channel/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type PaymentChannelRepository interface {
	CreatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error)
	UpdatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error)
	GetPaymentChannelById(ctx context.Context, id string) (resp *entity.PaymentChannelShow, err error)
	DeletePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error)
	SearchPaymentChannel(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelListAllActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelListAllNotActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelListAll(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelTypeById(ctx context.Context, id string) (resp *domain.PaymentChannelTypes, err error)
	GetListPaymentChannelType(ctx context.Context) (resp *[]domain.PaymentChannelTypes, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.PaymentChannels, err error)
	CheckExistingDataRelation(ctx context.Context, paymentChannelID int) (exist bool, err error)
	UpdatePaymentChannelStatus(ctx context.Context, id int, status bool) (err error)
}

type PaymentChannelUsecase interface {
	CreatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error)
	UpdatePaymentChannel(ctx context.Context, req *domain.PaymentChannels, id string) (err error)
	DeletePaymentChannel(ctx context.Context, id string) (err error)
	SearchPaymentChannel(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelListActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetPaymentChannelById(ctx context.Context, id string) (resp *entity.PaymentChannelShow, err error)
	GetPaymentChannelListAll(ctx context.Context, status string) (resp *[]entity.PaymentChannelShow, totalData int64, err error)
	GetListPaymentChannelType(ctx context.Context) (resp *[]domain.PaymentChannelTypes, err error)
	GetPaymentChannelTypeById(ctx context.Context, id string) (resp *domain.PaymentChannelTypes, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.PaymentChannels, err error)
	GetCachePaymentChannel(ctx context.Context, channelId string) (resp *entity.PaymentChannelShow, err error)
	UpdatePaymentChannelStatus(ctx context.Context, id string, status bool) (err error)
}
