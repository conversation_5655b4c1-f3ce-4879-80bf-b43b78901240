package entity

import (
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
)

type GORM struct {
	ID                                     int            `gorm:"column:id"`
	CompanyProductID                       int            `gorm:"column:company_product_id"`
	CompanyPaymentProviderChannelMappingID int            `gorm:"column:company_payment_provider_channel_mapping_id"`
	FeeFixValue                            float64        `gorm:"column:fee_fix_value"`
	FeePercentage                          float64        `gorm:"column:fee_percentage"`
	Status                                 bool           `gorm:"column:status"`
	CreatedAt                              time.Time      `gorm:"column:created_at"`
	UpdatedAt                              time.Time      `gorm:"column:updated_at"`
	DeletedAt                              gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (g GORM) ToDomain() domain.CompanyProductProviderChannelMapping {
	return domain.CompanyProductProviderChannelMapping{
		ID:                                     g.ID,
		CompanyProductID:                       g.CompanyProductID,
		CompanyPaymentProviderChannelMappingID: g.CompanyPaymentProviderChannelMappingID,
		FeeFixValue:                            g.FeeFixValue,
		FeePercentage:                          g.FeePercentage,
		Status:                                 g.Status,
		CreatedAt:                              g.CreatedAt,
		UpdatedAt:                              g.UpdatedAt,
	}
}

func (GORM) TableName() string {
	return "company_product_provider_channel_mappings"
}

// for select woth join tables
type GORMDetail struct {
	ID                                         int       `gorm:"column:id;<-false"`
	CompanyProductID                           int       `gorm:"column:company_product_id;<-false"`
	CompanyPaymentProviderChannelMappingID     int       `gorm:"column:company_payment_provider_channel_mapping_id;<-false"`
	FeeFixValue                                float64   `gorm:"column:fee_fix_value;<-false"`
	FeePercentage                              float64   `gorm:"column:fee_percentage;<-false"`
	Status                                     bool      `gorm:"column:status;<-false"`
	CreatedAt                                  time.Time `gorm:"column:created_at;<-false"`
	UpdatedAt                                  time.Time `gorm:"column:updated_at;<-false"`
	CompanyProductCode                         string    `gorm:"column:company_product_code;<-false"`
	CompanyProductName                         string    `gorm:"column:company_product_name;<-false"`
	CompanyProviderChannelMappingID            int       `gorm:"column:company_provider_channel_mapping_id;<-false"`
	CompanyProviderChannelMappingCode          string    `gorm:"column:company_provider_channel_mapping_code;<-false"`
	CompanyProviderChannelMappingProviderCode  string    `gorm:"column:company_provider_channel_mapping_provider_code;<-false"`
	CompanyProviderChannelMappingFeeFixValue   float64   `gorm:"column:company_provider_channel_mapping_fee_fix_value;<-false"`
	CompanyProviderChannelMappingFeePercentage float64   `gorm:"column:company_provider_channel_mapping_fee_percentage;<-false"`
	PaymentProviderID                          int       `gorm:"column:payment_provider_id;<-false"`
	PaymentProviderCode                        string    `gorm:"column:payment_provider_code;<-false"`
	PaymentProviderName                        string    `gorm:"column:payment_provider_name;<-false"`
	PaymentChannelID                           int       `gorm:"column:payment_channel_id;<-false"`
	PaymentChannelName                         string    `gorm:"column:payment_channel_name;<-false"`
}

func (g GORMDetail) ToDomain() domain.CompanyProductProviderChannelMapping {
	return domain.CompanyProductProviderChannelMapping{
		ID:                                     g.ID,
		CompanyProductID:                       g.CompanyProductID,
		CompanyPaymentProviderChannelMappingID: g.CompanyPaymentProviderChannelMappingID,
		FeeFixValue:                            g.FeeFixValue,
		FeePercentage:                          g.FeePercentage,
		Status:                                 g.Status,
		CreatedAt:                              g.CreatedAt,
		UpdatedAt:                              g.UpdatedAt,
		CompanyProductCode:                     g.CompanyProductCode,
		CompanyProductName:                     g.CompanyProductName,
		CompanyProviderChannelMappingID:        g.CompanyProviderChannelMappingID,
		CompanyProviderChannelMappingCode:      g.CompanyProviderChannelMappingCode,
		CompanyProviderChannelMappingProviderCode:  g.CompanyProviderChannelMappingProviderCode,
		CompanyProviderChannelMappingFeeFixValue:   g.CompanyProviderChannelMappingFeeFixValue,
		CompanyProviderChannelMappingFeePercentage: g.CompanyProviderChannelMappingFeePercentage,
		PaymentProviderID:                          g.PaymentProviderID,
		PaymentProviderCode:                        g.PaymentProviderCode,
		PaymentProviderName:                        g.PaymentProviderName,
		PaymentChannelID:                           g.PaymentChannelID,
		PaymentChannelName:                         g.PaymentChannelName,
	}
}

type GormExistingData struct {
	CompanyProductID                       int `gorm:"column:company_product_id;<-false"`
	CompanyPaymentProviderChannelMappingID int `gorm:"column:company_payment_provider_channel_mapping_id;<-false"`
}

type GORMCompanyProduct struct {
	ID            int     `gorm:"column:id;<-false"`
	CompanyID     int     `gorm:"column:company_id;<-false"`
	Code          string  `gorm:"column:code;<-false"`
	ProductName   string  `gorm:"column:product_name;<-false"`
	FeeFixValue   float64 `gorm:"column:fee_fix_value;<-false"`
	FeePercentage float64 `gorm:"column:fee_percentage;<-false"`
	Status        bool    `gorm:"column:status;<-false"`
}

func (GORMCompanyProduct) TableName() string {
	return "company_products"
}

func (g GORMCompanyProduct) ToDomain() domain.CompanyProducts {
	return domain.CompanyProducts{
		ID:            g.ID,
		CompanyID:     g.CompanyID,
		Code:          g.Code,
		ProductName:   g.ProductName,
		FeeFixValue:   g.FeeFixValue,
		FeePercentage: g.FeePercentage,
		Status:        g.Status,
	}
}

type GORMCompanyPaymentProviderChannelMapping struct {
	ID                int     `gorm:"column:id;<-false"`
	CompanyID         int     `gorm:"column:company_id;<-false"`
	PaymentProviderID int     `gorm:"column:payment_provider_id;<-false"`
	PaymentChannelID  int     `gorm:"column:payment_channel_id;<-false"`
	FeeFixValue       float64 `gorm:"column:fee_fix_value;<-false"`
	FeePercentage     float64 `gorm:"column:fee_percentage;<-false"`
	Capability        int     `gorm:"column:capability;<-false"`
	ExpiredTime       int     `gorm:"column:expired_time;<-false"`
	Status            bool    `gorm:"column:status;<-false"`
}

func (GORMCompanyPaymentProviderChannelMapping) TableName() string {
	return "company_payment_provider_channel_mappings"
}

func (g GORMCompanyPaymentProviderChannelMapping) ToDomain() domain.CompanyPaymentProviderChannelMappings {
	return domain.CompanyPaymentProviderChannelMappings{
		ID:                int64(g.ID),
		CompanyID:         int64(g.CompanyID),
		PaymentProviderID: int64(g.PaymentProviderID),
		PaymentChannelID:  int64(g.PaymentChannelID),
		FeeFixValue:       g.FeeFixValue,
		FeePercentage:     g.FeePercentage,
		Capability:        g.Capability,
		ExpiredTime:       g.ExpiredTime,
		Status:            g.Status,
	}
}
