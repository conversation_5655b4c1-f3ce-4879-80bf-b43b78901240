# OttoPay API Specification

This document provides detailed API specifications for the OttoPay Virtual Account integration.

## Base URL

```
https://your-domain.com/ottopay
```

## Authentication

The OttoPay API uses JWT (JSON Web Token) for authentication. First, obtain a token using the `/token` endpoint, then include it in the `Authorization` header for subsequent requests.

```
Authorization: Bearer <your-jwt-token>
```

## Common Headers

| Header | Value | Required | Description |
|--------|-------|----------|-------------|
| Content-Type | application/json | Yes | Request content type |
| Authorization | Bearer <token> | Yes (except /token) | JWT authentication token |
| X-API-Key | <api-key> | No | Additional API key if required |

## Error Handling

All endpoints return consistent error responses:

```json
{
    "meta": {
        "status": false,
        "code": 400,
        "message": "Error description"
    }
}
```

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - IP not allowed |
| 404 | Not Found |
| 408 | Request Timeout |
| 413 | Request Entity Too Large |
| 415 | Unsupported Media Type |
| 429 | Too Many Requests |
| 500 | Internal Server Error |

## Endpoints

### 1. Get Token

Authenticate and receive a JWT token for subsequent API calls.

**Endpoint:** `POST /token`

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
    "username": "string",
    "password": "string"
}
```

**Request Parameters:**

| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| username | string | Yes | 50 | Username provided by OttoPay |
| password | string | Yes | 100 | Password provided by OttoPay |

**Success Response (200):**
```json
{
    "meta": {
        "status": true,
        "code": 200,
        "message": "success"
    },
    "data": {
        "id": "45c14915-f731-43ac-b25a-2182e13c98f7",
        "username": "ottopay",
        "id_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
}
```

**Error Response (401):**
```json
{
    "meta": {
        "status": false,
        "code": 401,
        "message": "Authentication failed"
    }
}
```

### 2. Inquiry

Query customer information and bill details for a Virtual Account.

**Endpoint:** `POST /inquiry`

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body:**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "REQ123456789",
    "ChannelType": "BMRI",
    "Transaction Date": "15/03/2024 22:07:40",
    "Additional Data": ""
}
```

**Request Parameters:**

| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| CompanyCode | string | Yes | 5 | VA Company Code (numeric) |
| CustomerNumber | string | Yes | 11 | Customer Number/Bill ID (numeric) |
| RequestID | string | Yes | 255 | Unique request identifier |
| ChannelType | string | Yes | 50 | Bank channel (BMRI, BRI, BINA, BNI) |
| Transaction Date | string | No | 19 | Transaction date (YYYY/MM/DD HH:MM:SS) |
| Additional Data | string | No | 255 | Additional information |

**Success Response (200):**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "REQ123456789",
    "InquiryStatus": "00",
    "InquiryReason": {
        "Indonesian": "Sukses",
        "English": "Success"
    },
    "CustomerName": "Customer Name Virtual Account",
    "CurrencyCode": "IDR",
    "TotalAmount": "150000.00",
    "SubCompany": "00000",
    "DetailBills": null,
    "Additional Data": ""
}
```

**Error Response (400):**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "REQ123456789",
    "InquiryStatus": "01",
    "InquiryReason": {
        "Indonesian": "Virtual Account tidak ditemukan",
        "English": "Virtual Account not found"
    },
    "CustomerName": "",
    "CurrencyCode": "IDR",
    "TotalAmount": "0.00",
    "SubCompany": "00000",
    "DetailBills": null,
    "Additional Data": ""
}
```

### 3. Payment

Process payment for a Virtual Account.

**Endpoint:** `POST /payment`

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token>
```

**Request Body:**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "PAY123456789",
    "ChannelType": "BMRI",
    "CustomerName": "Test Customer",
    "CurrencyCode": "IDR",
    "PaidAmount": "150000.00",
    "TotalAmount": "150000.00",
    "SubCompany": "00000",
    "TransactionDate": "15/03/2024 22:07:40",
    "Reference": "REF123456789",
    "DetailBills": [],
    "Additionaldata": ""
}
```

**Request Parameters:**

| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| CompanyCode | string | Yes | 5 | VA Company Code (numeric) |
| CustomerNumber | string | Yes | 11 | Customer Number/Bill ID (numeric) |
| RequestID | string | Yes | 255 | Unique request identifier |
| ChannelType | string | Yes | 50 | Bank channel (BMRI, BRI, BINA, BNI) |
| CustomerName | string | Yes | 30 | Customer name |
| CurrencyCode | string | Yes | 3 | Currency code (IDR, USD) |
| PaidAmount | string | Yes | 15 | Amount paid by customer |
| TotalAmount | string | Yes | 15 | Total bill amount |
| SubCompany | string | No | 5 | Sub company code (default: 00000) |
| TransactionDate | string | No | 19 | Transaction date |
| Reference | string | Yes | 255 | Payment reference number |
| DetailBills | array | No | - | Bill details (optional) |
| Additionaldata | string | No | 255 | Additional data |

**Success Response (200):**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "PAY123456789",
    "PaymentFlagStatus": "00",
    "PaymentFlagReason": {
        "Indonesian": "Sukses",
        "English": "Success"
    },
    "CustomerName": "Test Customer",
    "CurrencyCode": "IDR",
    "PaidAmount": "150000.00",
    "TotalAmount": "150000.00",
    "Transaction Date": "15/03/2024 22:07:40",
    "DetailBills": null,
    "FreeTexts": null,
    "Additional Data": ""
}
```

**Error Response (400):**
```json
{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "PAY123456789",
    "PaymentFlagStatus": "01",
    "PaymentFlagReason": {
        "Indonesian": "Jumlah pembayaran salah",
        "English": "Invalid Amount"
    },
    "CustomerName": "Test Customer",
    "CurrencyCode": "IDR",
    "PaidAmount": "150000.00",
    "TotalAmount": "150000.00",
    "Transaction Date": "15/03/2024 22:07:40",
    "DetailBills": null,
    "FreeTexts": null,
    "Additional Data": ""
}
```

### 4. Health Check

Check service health status.

**Endpoint:** `GET /health`

**Headers:** None required

**Success Response (200):**
```json
{
    "status": "ok",
    "timestamp": **********,
    "service": "ottopay",
    "version": "1.0.0"
}
```

## Status Codes

### Inquiry Status Codes

| Code | Description |
|------|-------------|
| 00 | Success |
| 01 | Failed |

### Payment Status Codes

| Code | Description |
|------|-------------|
| 00 | Success |
| 01 | Failed |
| 02 | Timeout |

## Bank Channel Types

| Bank | Channel Type | Company Code | Sub-Prefix |
|------|-------------|--------------|------------|
| Mandiri | BMRI | 71101 | 409 |
| BRI | BRI | 15772 | 453 |
| BINA | BINA | 98192 | 501 |
| BNI | BNI | 8428 | 399 |

## Virtual Account Format

Virtual Account numbers follow this format:
```
VA Number = [Company Code] + [Customer Number]
```

**Example:**
- Company Code: 71101 (Mandiri)
- Customer Number: *********** (phone number)
- VA Number: 71101***********

## Rate Limiting

The API implements rate limiting to prevent abuse:
- Default: 100 requests per minute per IP
- Exceeded limit returns HTTP 429

## Security

### IP Whitelisting

Access is restricted to whitelisted IP addresses:
- ************
- *************

### Request Signing (Optional)

If enabled, requests must include an HMAC-SHA256 signature:

```
X-Signature: <hmac-sha256-signature>
```

### Token Expiration

JWT tokens expire after 24 hours by default. Obtain a new token when expired.

## Testing

### Simulator Information

For testing purposes, use the OttoPay simulator:
- URL: https://portal.ottodigital.id/
- Username: MOA
- Password: MOA123

### Test Credentials

```json
{
    "username": "ottopay",
    "password": "ac710qf!"
}
```

## Examples

### cURL Examples

**Get Token:**
```bash
curl -X POST https://your-domain.com/ottopay/token \
  -H "Content-Type: application/json" \
  -d '{"username":"ottopay","password":"ac710qf!"}'
```

**Inquiry:**
```bash
curl -X POST https://your-domain.com/ottopay/inquiry \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "REQ123456789",
    "ChannelType": "BMRI"
  }'
```

**Payment:**
```bash
curl -X POST https://your-domain.com/ottopay/payment \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "CompanyCode": "71101",
    "CustomerNumber": "***********",
    "RequestID": "PAY123456789",
    "ChannelType": "BMRI",
    "CustomerName": "Test Customer",
    "CurrencyCode": "IDR",
    "PaidAmount": "150000.00",
    "TotalAmount": "150000.00",
    "Reference": "REF123456789"
  }'
```
