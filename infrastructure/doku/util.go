package doku

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
)

func (w *wrapper) generateSignature(ctx context.Context, comp SignatureComponent) string {
	digest := comp.DigestReqBody()
	// fmt.Println("digest", string(digest))
	var componentSignature strings.Builder
	componentSignature.WriteString(HEADER_CLIENT_ID + SYMBOL_COLON + comp.Credential.ClientID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_ID + SYMBOL_COLON + comp.RequestID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_TIMESTAMP + SYMBOL_COLON + comp.RequestTimestamp)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_TARGET + SYMBOL_COLON + comp.RequestTarget)
	// If body not send when access API with HTTP method GET/DELETE
	if len(digest) > 0 && comp.Method == http.MethodPost {
		componentSignature.WriteString("\n")
		componentSignature.WriteString(HEADER_DIGEST + SYMBOL_COLON + digest)
	}

	// fmt.Println(componentSignature.String())
	// fmt.Println("")

	// Calculate HMAC-SHA256 base64 from all the components above
	key := []byte(comp.Credential.SecretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(componentSignature.String()))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	// Prepend encoded result with algorithm info HMACSHA256=
	return "HMACSHA256=" + signature
}

func (w *wrapper) verifyResponseSignature(ctx context.Context, comp SignatureComponent, signature string) error {
	digest := comp.DigestResBody()
	if len(signature) == 0 {
		return nil
	}

	// fmt.Println("digest", string(digest))
	var componentSignature strings.Builder
	componentSignature.WriteString(HEADER_CLIENT_ID + SYMBOL_COLON + comp.Credential.ClientID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_ID + SYMBOL_COLON + comp.RequestID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_RESPONSE_TIMESTAMP + SYMBOL_COLON + comp.ResponseTimestamp)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_TARGET + SYMBOL_COLON + comp.RequestTarget)
	if len(digest) > 0 && comp.Method == http.MethodPost {
		componentSignature.WriteString("\n")
		componentSignature.WriteString(HEADER_DIGEST + SYMBOL_COLON + digest)
	}

	// fmt.Println(componentSignature.String())
	// fmt.Println("")

	// Calculate HMAC-SHA256 base64 from all the components above
	key := []byte(comp.Credential.SecretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(componentSignature.String()))
	signatureResult := base64.StdEncoding.EncodeToString(h.Sum(nil))
	// Prepend encoded result with algorithm info HMACSHA256=
	signatureResult = "HMACSHA256=" + signatureResult

	if signatureResult != signature {
		return ErrSignature
	}

	return nil
}

func (w *wrapper) VerifyCallbackSignature(ctx context.Context, comp SignatureComponent, signature string) error {
	digest := comp.DigestReqBody()
	if len(signature) == 0 {
		return nil
	}

	// fmt.Println("digest", string(digest))
	var componentSignature strings.Builder
	componentSignature.WriteString(HEADER_CLIENT_ID + SYMBOL_COLON + comp.Credential.ClientID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_ID + SYMBOL_COLON + comp.RequestID)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_TIMESTAMP + SYMBOL_COLON + comp.RequestTimestamp)
	componentSignature.WriteString("\n")
	componentSignature.WriteString(HEADER_REQUEST_TARGET + SYMBOL_COLON + comp.RequestTarget)
	if len(digest) > 0 {
		componentSignature.WriteString("\n")
		componentSignature.WriteString(HEADER_DIGEST + SYMBOL_COLON + digest)
	}

	// fmt.Println(componentSignature.String())
	// fmt.Println("")

	// Calculate HMAC-SHA256 base64 from all the components above
	key := []byte(comp.Credential.SecretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(componentSignature.String()))
	signatureResult := base64.StdEncoding.EncodeToString(h.Sum(nil))
	// Prepend encoded result with algorithm info HMACSHA256=
	signatureResult = "HMACSHA256=" + signatureResult

	if signatureResult != signature {
		fmt.Println(signatureResult, signature)
		return ErrSignature
	}

	return nil
}

func (w *wrapper) setHeaders(ctx context.Context, credential Credential, method string, path string, reqBody []byte) map[string]string {
	now := time.Now().UTC()
	reqID := uuid.New().String()
	sc := SignatureComponent{
		Credential:       credential,
		Method:           method,
		RequestID:        reqID,
		RequestTimestamp: now.Format(time.RFC3339),
		RequestTarget:    path,
		RequestBody:      reqBody,
	}
	signature := w.generateSignature(ctx, sc)

	headers := make(map[string]string)
	headers[HEADER_CLIENT_ID] = credential.ClientID
	headers[HEADER_REQUEST_ID] = reqID
	headers[HEADER_REQUEST_TIMESTAMP] = now.Format(time.RFC3339)
	headers[HEADER_SIGNATURE] = signature
	headers["Accept"] = "application/json"
	headers["Content-Type"] = "application/json"

	return headers
}
