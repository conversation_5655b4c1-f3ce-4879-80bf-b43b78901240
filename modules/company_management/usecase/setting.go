package usecase

import (
	"github.com/go-redis/redis/v8"
)

type defaultCompanyManagement struct {
	companyRepo CompanyManagementRepo
	cache       *redis.Client
}

func Setup() *defaultCompanyManagement {
	return &defaultCompanyManagement{}
}

func (s *defaultCompanyManagement) SetCompanyRepo(t CompanyManagementRepo) *defaultCompanyManagement {
	s.companyRepo = t
	return s
}

func (s *defaultCompanyManagement) SetRedisClient(t *redis.Client) *defaultCompanyManagement {
	s.cache = t
	return s
}

func (s *defaultCompanyManagement) Validate() CompanyManagementUsecase {
	if s.companyRepo == nil {
		panic("company repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	return s
}
