package constants

import "repo.nusatek.id/nusatek/payment/utils/str"

// cash in and cash in item status
// Semua Invoice Pending = Pending
// Sebagian Paid = Partial Paid
// Semua Paid = Paid
// Sebagian  Reconciled = Partial Reconciled
// Semua Reconciled = Reconciled
// Sebagian Done = Partial Done
// Semua Done = Done
const (
	CashInStatusPending           = "pending"
	CashInStatusPaid              = "paid"
	CashInStatusPartialReconciled = "partial_reconciled"
	CashInStatusReconciled        = "reconciled"
	CashInStatusPartialDone       = "partial_done"
	CashInStatusDone              = "done"
	CashInStatusExpired           = "expired"
	CashInStatusFailed            = "failed" // general failed
	CashInStatusRefundRequest     = "refund_request"
	CashInStatusRefunded          = "refunded"

	CashInItemStatusPending              = "pending"
	CashInItemStatusPaid                 = "paid"
	CashInItemStatusWaitingForReconciled = "waiting_for_reconciled" // request disbursement
	CashInItemStatusReconciled           = "reconciled"
	CashInItemStatusFailed               = "failed"
	CashInItemStatusDone                 = "done"
)

var (
	CashInNoPartialStatus = []string{CashInStatusPending, CashInStatusPaid, CashInStatusExpired, CashInStatusFailed, CashInStatusRefundRequest, CashInStatusRefunded}
)

func DefineCashInStatusFromItemStatuses(currStatus string, itemStatuses []string) string {

	if str.Contains(itemStatuses, CashInStatusDone) {
		if len(itemStatuses) == 1 {
			return CashInStatusDone
		}
		return CashInStatusPartialDone
	} else if str.Contains(itemStatuses, CashInStatusReconciled) {
		if len(itemStatuses) == 1 {
			return CashInStatusReconciled
		}
		return CashInStatusPartialReconciled
	}

	return currStatus
}
