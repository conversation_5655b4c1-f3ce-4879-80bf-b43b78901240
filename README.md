# Nusatek Boilerplate

## Project Folder Structure

```
- app
    - rest
        - routes
        - rest_app.go
    - grpc
- infrastructure
    - database
        - postgres.go
    - messagebroker
        - rabbitmq.go
- modules
    - auth
        - entity
        - usecase
        - repository
        - auth.go
    - partner
        - entity
        - usecase
        - repository
        - partner.go
- rbac
    - policy.csv
    - model.conf
- utils
    - strings.go
- main.go
- .gitignore
- README.md
- gitlab-ci.yml
- config.json
- .dockerignore
- Dockerfile
- go.mod
- go.sum
```
