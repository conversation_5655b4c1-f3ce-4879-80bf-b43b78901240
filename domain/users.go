package domain

import (
	"time"

	"gorm.io/gorm"
)

type Users struct {
	ID          int            `json:"id"`
	Name        string         `json:"name"`
	Email       string         `json:"email" validate:"required,email"`
	Password    string         `json:"password" validate:"min=6"`
	RoleId      int            `json:"role_id" validate:"required"`
	LastLoginAt int64          `json:"last_login_at"`
	Status      bool           `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}
