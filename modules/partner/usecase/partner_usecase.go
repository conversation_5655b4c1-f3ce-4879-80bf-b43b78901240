package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/partner/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (du *partnerUseCase) List(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (resp *[]entity.PartnerShow, totalItem int64, err error) {
	res, totalItem, err := du.partnerRepository.List(ctx, param, orderParam)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	for i := 0; i < len(*res); i++ {
		partnerTag, errRes := du.GetPartnerTagByPartnerId(ctx, strconv.Itoa((*res)[i].ID))
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "partner tag not found")
			logger.Error(ctx, err.Error())
		}

		if partnerTag != nil {
			var tags []domain.Tags
			for j := 0; j < len(*partnerTag); j++ {
				tag, errRes := du.GetTagById(ctx, strconv.Itoa((*partnerTag)[j].TagID))
				if errRes != nil {
					err = errors.SetErrorMessage(http.StatusNotFound, "tags not found")
					logger.Error(ctx, err.Error())
					return
				}
				tags = append(tags, *tag)
			}
			(*res)[i].Tags = &tags
		}
	}

	resp = res
	return
}

func (du *partnerUseCase) ListHistory(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	res, totalItem, err = du.partnerRepository.ListHistory(ctx, param, orderParam)
	if err != nil {
		return
	}
	return
}

func (du *partnerUseCase) View(ctx context.Context, param map[string]interface{}) (*entity.PartnerShow, error) {
	partner, err := du.partnerRepository.View(ctx, param)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return nil, err
	}

	partnerTag, errRes := du.GetPartnerTagByPartnerId(ctx, strconv.Itoa(partner.ID))
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner tag not found")
		logger.Error(ctx, err.Error())
	}

	if partnerTag != nil {
		var tags []domain.Tags
		for j := 0; j < len(*partnerTag); j++ {
			tag, errRes := du.GetTagById(ctx, strconv.Itoa((*partnerTag)[j].TagID))
			if errRes != nil {
				err = errors.SetErrorMessage(http.StatusNotFound, "tags not found")
				logger.Error(ctx, err.Error())
				return nil, err
			}
			tags = append(tags, *tag)
		}
		partner.Tags = &tags
	}

	return partner, nil
}

func (du *partnerUseCase) ViewHistory(ctx context.Context, batchNumber string, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	_, exist := du.partnerRepository.GetByBatchNumber(ctx, batchNumber)
	if !exist {
		return res, totalItem, errors.SetErrorMessage(http.StatusBadRequest, "batch number not found")
	}

	params := map[string]interface{}{
		"batch_number": batchNumber,
	}

	res, totalItem, err = du.partnerRepository.ViewHistory(ctx, params, orderParam)
	if err != nil {
		return
	}
	return
}

func (du *partnerUseCase) DeletePartner(ctx context.Context, id string) (err error) {
	partner, err := du.partnerRepository.GetPartnerByID(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusBadRequest, "partner id not found")
		logger.Error(ctx, err.Error())
		return
	}

	err = du.partnerRepository.DeletePartner(ctx, partner)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	_ = du.deleteCacheParner(ctx, partner.Code)
	_ = du.deleteCachePartnerTags(ctx, strconv.Itoa(partner.ID))
	return
}

func (du *partnerUseCase) validate(ctx context.Context, partner *domain.Partners, companyCode string) (err error) {
	cm, exist := du.partnerRepository.GetByCompanyCode(ctx, companyCode)
	if !exist {
		err = errors.SetErrorMessage(http.StatusBadRequest, "company code not found")
		logger.Error(ctx, err.Error())
		return
	}

	partner.CompanyID = cm.ID // assing companyID

	_, err = du.channelRepo.GetPaymentChannelById(ctx, strconv.Itoa(partner.PaymentChannelId))
	if err != nil {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		return
	}

	return
}

func (du *partnerUseCase) CreatePartner(ctx context.Context, partner *domain.Partners, companyCode string, tagId []int) (err error) {
	err = du.validate(ctx, partner, companyCode)
	if err != nil {
		return
	}

	tx := du.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	err = du.partnerRepository.CreatePartner(ctx, partner, tx)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in partner code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	for i := 0; i < len(tagId); i++ {
		_, err = du.GetTagById(ctx, strconv.Itoa(tagId[i]))
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "tag not found")
			logger.Error(ctx, err.Error())
			return
		}
		partnerTag := domain.PartnerTags{
			PartnerID: partner.ID,
			TagID:     tagId[i],
		}

		err = du.partnerRepository.CreatePartnerTag(ctx, &partnerTag, tx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	tx.Commit()
	_ = du.setCachePartner(ctx, partner)
	_, _ = du.setCachePartnerTags(ctx, strconv.Itoa(partner.ID))
	return
}

func (du *partnerUseCase) UpdatePartner(ctx context.Context, partner *domain.Partners, companyCode string, tagId []int) (err error) {
	err = du.validate(ctx, partner, companyCode)
	if err != nil {
		return
	}

	part, err := du.partnerRepository.GetPartnerByID(ctx, strconv.Itoa(partner.ID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner id not found")
		logger.Error(ctx, err.Error())
		return
	}

	channel, err := du.channelRepo.GetPaymentChannelById(ctx, strconv.Itoa(partner.PaymentChannelId))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment channel not found")
		logger.Error(ctx, err.Error())
		return
	}

	tx := du.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	partner.CreatedAt = part.CreatedAt
	err = du.partnerRepository.UpdatePartner(ctx, partner, tx)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in code field")
		}
		logger.Error(ctx, err.Error())
		return
	}

	err = du.partnerRepository.DeletePartnerTagByPartnerId(ctx, strconv.Itoa(partner.ID), tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	for i := 0; i < len(tagId); i++ {
		_, err = du.GetTagById(ctx, strconv.Itoa(tagId[i]))
		if err != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "tag not found")
			logger.Error(ctx, err.Error())
			return
		}
		partnerTag := domain.PartnerTags{
			PartnerID: partner.ID,
			TagID:     tagId[i],
		}
		err = du.partnerRepository.CreatePartnerTag(ctx, &partnerTag, tx)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	reqTrx := domain.CashOutTransactions{
		PartnerName:          partner.Name,
		PartnerAccountName:   partner.ArAccount,
		PartnerAccountNumber: partner.ApAccount,
		PartnerBankName:      channel.Name,
		PaymentChannelID:     channel.ID,
	}
	err = du.partnerRepository.UpdatePaymentChannelPartnerInCashOutTrx(ctx, &reqTrx, strconv.Itoa(partner.ID), constants.CashOutStatusReady, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "failed update payment channel partner in cash out trx", logger.Err(err))
		return
	}

	tx.Commit()
	_ = du.setCachePartner(ctx, partner)
	_, _ = du.setCachePartnerTags(ctx, strconv.Itoa(partner.ID))
	return
}

func (s *partnerUseCase) GetPartnerByCompanyId(ctx context.Context, companyId string) (resp *[]domain.Partners, err error) {
	resp, err = s.partnerRepository.GetPartnerByCompanyId(ctx, companyId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) GetPartnerByCode(ctx context.Context, code string) (resp *domain.Partners, err error) {
	resp, err = s.getCachePartner(ctx, code)
	return
}

func (s *partnerUseCase) getCachePartner(ctx context.Context, partnerCode string) (resp *domain.Partners, err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePartner, partnerCode)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes.Error() != "redis: nil" {
			err = errors.SetErrorMessage(http.StatusNotFound, "cache partner not found")
			logger.Error(ctx, err.Error())
			return
		}
	}

	switch true {
	case cache == "":
		prod, errRes := s.partnerRepository.GetPartnerByCode(ctx, partnerCode)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "parner not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCachePartner(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.Partners
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *partnerUseCase) setCachePartner(ctx context.Context, req *domain.Partners) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CachePartner, req.Code)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) deleteCacheParner(ctx context.Context, partnerCode string) (err error) {
	key := fmt.Sprintf("%v-%v", constants.CachePartner, partnerCode)
	err = s.cache.Del(ctx, key).Err()
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (s *partnerUseCase) GetPartnerById(ctx context.Context, id string) (resp *domain.Partners, err error) {
	resp, err = s.partnerRepository.GetPartnerByID(ctx, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (du *partnerUseCase) SelectByIds(ctx context.Context, ids []int) (resp []domain.Partners, err error) {
	resp, err = du.partnerRepository.SelectByIds(ctx, ids)
	if err != nil {
		return
	}
	return
}

func (du *partnerUseCase) PatchStatusBulk(ctx context.Context, ids []int, status bool) (err error) {
	tx := du.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	partners, err := du.partnerRepository.PatchStatusBulk(ctx, tx, ids, status)
	if err != nil {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		return
	}

	err = tx.Commit().Error
	if err != nil {
		return err
	}

	for _, partner := range partners {
		_ = du.setCachePartner(ctx, &partner)
	}
	return
}

func (du *partnerUseCase) UpdateStatus(ctx context.Context, id string, status bool) error {
	partner, err := du.partnerRepository.GetPartnerByID(ctx, id)
	if err != nil {
		return errors.SetErrorMessage(http.StatusNotFound, "partner not found")
	}

	tx := du.partnerRepository.BeginsTrans()
	defer tx.Rollback()

	partner.Status = status
	err = du.partnerRepository.UpdatePartner(ctx, partner, tx)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate") {
			err = errors.SetErrorMessage(http.StatusBadRequest, "duplicate value in partner code field")
		}
		logger.Error(ctx, err.Error())
		return err
	}

	tx.Commit()
	_ = du.setCachePartner(ctx, partner)
	return nil
}
