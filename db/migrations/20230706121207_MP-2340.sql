-- migrate:up
ALTER TABLE payment_channels ADD COLUMN "sequence" SMALLINT;
UPDATE payment_channels SET "sequence" = 1 WHERE name = 'Bank BCA';
UPDATE payment_channels SET "sequence" = 2 WHERE name = 'Bank Mandiri';
UPDATE payment_channels SET "sequence" = 3 WHERE name = 'Bank BNI';
UPDATE payment_channels SET "sequence" = 4 WHERE name = 'Bank BRI';
UPDATE payment_channels SET "sequence" = 5 WHERE name = 'Bank CIMB';
UPDATE payment_channels SET "sequence" = 6 WHERE name = 'Bank BSI';
-- migrate:down

ALTER TABLE payment_channels DROP COLUMN "sequence";