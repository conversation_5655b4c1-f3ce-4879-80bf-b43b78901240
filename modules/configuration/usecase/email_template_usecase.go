package usecase

import (
	"context"
	"time"

	"github.com/go-playground/validator/v10"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/modules/configuration/repository"
	"repo.nusatek.id/nusatek/payment/utils"
)

type TemplateUsecase interface {
	CreateTemplate(ctx context.Context, template *entity.TemplateRequest) (*entity.Template, error)
	UpdateTemplate(ctx context.Context, id int64, template *entity.TemplateRequest) (*entity.Template, error)
	GetTemplates(ctx context.Context, paginate utils.Pagination) (*[]entity.Template, int64, error)
	GetTemplate(ctx context.Context, id int) (*entity.Template, error)
	DeleteTemplate(ctx context.Context, id int) error
	GetTemplateByCompanyID(ctx context.Context, companyID int64) ([]entity.Template, error)
	GetPartnersByCompanyId(ctx context.Context, companyId int64) ([]map[string]interface{}, error)
}

type templateUsecase struct {
	repo      repository.EmailTemplateRepository
	validator *validator.Validate
}

func NewEmailTemplateUsecase(repo repository.EmailTemplateRepository) TemplateUsecase {
	return &templateUsecase{repo: repo, validator: validator.New()}
}

func (uc *templateUsecase) CreateTemplate(ctx context.Context, templateReq *entity.TemplateRequest) (*entity.Template, error) {
	if err := uc.validator.Struct(templateReq); err != nil {
		return nil, errors.SetErrorMessage(400, err.Error())
	}

	now := time.Now()

	templateModel := entity.Template{
		CompanyID:  templateReq.CompanyID,
		PartnerIds: templateReq.PartnerIds,
		EmailCcs:   templateReq.EmailCcs,
		Subject:    templateReq.Subject,
		Content:    templateReq.Content,
		CreatedAt:  now,
		UpdatedAt:  now,
		IsDefault:  true,
		IsActive:   true,
	}

	createdTemplate, err := uc.repo.CreateTemplate(ctx, &templateModel)
	if err != nil {
		logger.Error(ctx, "Failed to create template", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to create template")
	}

	return createdTemplate, nil
}

func (uc *templateUsecase) UpdateTemplate(ctx context.Context, id int64, templateReq *entity.TemplateRequest) (*entity.Template, error) {
	if err := uc.validator.Struct(templateReq); err != nil {
		return nil, errors.SetErrorMessage(400, err.Error())
	}

	templateModel := entity.Template{
		ID:         int(id),
		CompanyID:  templateReq.CompanyID,
		PartnerIds: templateReq.PartnerIds,
		EmailCcs:   templateReq.EmailCcs,
		Subject:    templateReq.Subject,
		Content:    templateReq.Content,
		UpdatedAt:  time.Now(),
		IsActive:   templateReq.IsActive,
		IsDefault:  templateReq.IsDefault,
	}

	updatedTemplate, err := uc.repo.UpdateTemplate(ctx, &templateModel)
	if err != nil {
		logger.Error(ctx, "Failed to update template", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to update template")
	}

	return updatedTemplate, nil
}

func (uc *templateUsecase) GetTemplates(ctx context.Context, paginate utils.Pagination) (*[]entity.Template, int64, error) {
	templates, totalData, err := uc.repo.GetTemplates(ctx, paginate)
	if err != nil {
		logger.Error(ctx, "Failed to get templates", logger.Err(err))
		return nil, 0, errors.SetErrorMessage(500, "Failed to get templates")
	}

	return templates, totalData, nil
}

func (uc *templateUsecase) GetTemplate(ctx context.Context, id int) (*entity.Template, error) {
	template, err := uc.repo.GetTemplate(ctx, id)

	if err != nil {
		logger.Error(ctx, "Failed to get template", logger.Err(err))
		return nil, errors.SetErrorMessage(404, "Template not found")
	}
	return template, nil
}

func (uc *templateUsecase) DeleteTemplate(ctx context.Context, id int) error {
	err := uc.repo.DeleteTemplate(ctx, id)

	if err != nil {
		logger.Error(ctx, "Failed to delete template", logger.Err(err))
		return errors.SetErrorMessage(500, "Failed to delete template")
	}
	return nil
}

func (uc *templateUsecase) GetTemplateByCompanyID(ctx context.Context, companyID int64) ([]entity.Template, error) {
	templates, err := uc.repo.GetTemplateByCompanyID(ctx, companyID)

	if err != nil {
		logger.Error(ctx, "Failed to get templates by company ID", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to get templates by company ID")
	}
	return templates, nil
}

func (uc *templateUsecase) GetPartnersByCompanyId(ctx context.Context, companyId int64) ([]map[string]interface{}, error) {
	partners, err := uc.repo.GetPartnersByCompanyId(ctx, companyId)
	if err != nil {
		logger.Error(ctx, "Failed to get partners by company ID", logger.Err(err))
		return nil, errors.SetErrorMessage(500, "Failed to get partners by company ID")
	}
	return partners, nil
}
