package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/modules/configuration/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type EmailTemplateRepository interface {
	CreateTemplate(ctx context.Context, template *entity.Template) (*entity.Template, error)
	UpdateTemplate(ctx context.Context, template *entity.Template) (*entity.Template, error)
	GetTemplates(ctx context.Context, paginate utils.Pagination) (*[]entity.Template, int64, error)
	GetTemplate(ctx context.Context, id int) (*entity.Template, error)
	DeleteTemplate(ctx context.Context, id int) error
	GetTemplateByCompanyID(ctx context.Context, companyID int64) ([]entity.Template, error)
	GetDefaultTemplate(ctx context.Context, companyID int64) (*entity.Template, error)
	GetPartnersByCompanyId(ctx context.Context, companyId int64) ([]map[string]interface{}, error)
	GetTemplateByCompanyAndPartner(ctx context.Context, companyID int64, partnerID int64) (entity.Template, error)
	BeginsTrans() *gorm.DB
}

type templateRepository struct {
	DB *gorm.DB
}

func NewEmailTemplateRepository(db *gorm.DB) EmailTemplateRepository {
	return &templateRepository{DB: db}
}

func (r *templateRepository) BeginsTrans() *gorm.DB {
	return r.DB.Begin()
}

func (r *templateRepository) CreateTemplate(ctx context.Context, template *entity.Template) (*entity.Template, error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create the main template record
	if err := tx.Table("email_templates").Create(template).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create entries in the template_partners junction table
	if len(template.PartnerIds) > 0 {
		for _, id := range template.PartnerIds {
			if err := tx.Table("template_partners").Create(&struct {
				TemplateID int   `gorm:"column:template_id"`
				PartnerID  int64 `gorm:"column:partner_id"`
			}{
				TemplateID: template.ID,
				PartnerID:  int64(id),
			}).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Fetch the created template with all relations
	createdTemplate, err := r.GetTemplate(ctx, template.ID)
	if err != nil {
		return nil, err
	}

	return createdTemplate, nil
}

func (r *templateRepository) UpdateTemplate(ctx context.Context, template *entity.Template) (*entity.Template, error) {
	tx := r.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete existing entries in the template_partners junction table
	if err := tx.Table("template_partners").Where("template_id = ?", template.ID).Delete(&struct{ TemplateID int }{}).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create new entries in the template_partners junction table
	if len(template.PartnerIds) > 0 {
		for _, partnerId := range template.PartnerIds {
			if err := tx.Table("template_partners").Create(&struct {
				TemplateID int   `gorm:"column:template_id"`
				PartnerID  int64 `gorm:"column:partner_id"`
			}{
				TemplateID: template.ID,
				PartnerID:  int64(partnerId),
			}).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}
	}

	if err := tx.Table("email_templates").Save(template).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Fetch the updated template with all relations
	updatedTemplate, err := r.GetTemplate(ctx, template.ID)
	if err != nil {
		return nil, err
	}

	return updatedTemplate, nil
}

func (r *templateRepository) GetTemplates(ctx context.Context, paginate utils.Pagination) (*[]entity.Template, int64, error) {
	var templates []entity.Template
	var totalData int64

	query := func(db *gorm.DB) *gorm.DB {
		condition := db
		value := "%" + paginate.Value + "%"
		switch paginate.Key {
		case "subject":
			condition.Where("subject ILIKE ?", value)
		case "company_id":
			condition.Where("company_id = ?", paginate.Value)
		default:
			condition.Where("CONCAT(subject, ' ', content) ILIKE ?", value)
		}

		if paginate.Search != "" {
			paginate.Search = "%" + paginate.Search + "%"
			condition.Where("subject ILIKE ? OR content ILIKE ?", paginate.Search, paginate.Search)
		}

		return condition
	}

	err := r.DB.WithContext(ctx).Table("email_templates").Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Preload("Company").Preload("Partners").
		Scopes(query).Order(paginate.GetOrderBy("")).Find(&templates).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.DB.WithContext(ctx).Table("email_templates").Scopes(query).Model(&entity.Template{}).Count(&totalData).Error
	if err != nil {
		return nil, 0, err
	}

	return &templates, totalData, nil
}

func (r *templateRepository) GetTemplate(ctx context.Context, id int) (*entity.Template, error) {
	var template entity.Template
	err := r.DB.WithContext(ctx).Table("email_templates").
		Preload("Company").
		Preload("Partners").
		First(&template, id).Error
	if err != nil {
		return nil, err
	}

	return &template, nil
}

func (r *templateRepository) DeleteTemplate(ctx context.Context, id int) error {
	return r.DB.WithContext(ctx).Table("email_templates").Delete(&entity.Template{}, id).Error
}

func (r *templateRepository) GetTemplateByCompanyID(ctx context.Context, companyID int64) ([]entity.Template, error) {
	var templates []entity.Template
	err := r.DB.WithContext(ctx).Table("email_templates").
		Preload("Company").
		Preload("Partners").
		Where("company_id = ?", companyID).Find(&templates).Error
	return templates, err
}

func (r *templateRepository) GetDefaultTemplate(ctx context.Context, companyID int64) (*entity.Template, error) {
	var template entity.Template
	err := r.DB.WithContext(ctx).Table("email_templates").Where("company_id = ? AND is_default = ?", companyID, true).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *templateRepository) GetPartnersByCompanyId(ctx context.Context, companyId int64) ([]map[string]interface{}, error) {
	var partners []map[string]interface{}

	err := r.DB.WithContext(ctx).
		Table("partners AS p").
		Select("p.id AS partner_id, p.name AS partner_name, CASE WHEN tp.partner_id IS NOT NULL THEN true ELSE false END AS is_use").
		Joins("LEFT JOIN template_partners tp ON p.id = tp.partner_id").
		Where("p.company_id = ?", companyId).
		Scan(&partners).Error

	if err != nil {
		return nil, err
	}

	return partners, nil
}

func (r *templateRepository) GetTemplateByCompanyAndPartner(ctx context.Context, companyID int64, partnerID int64) (entity.Template, error) {
	var template entity.Template

	err := r.DB.WithContext(ctx).Table("email_templates").Joins("JOIN template_partners ON email_templates.id = template_partners.template_id").
		Where("email_templates.company_id = ? AND template_partners.partner_id = ?", companyID, partnerID).First(&template).Error

	return template, err
}
