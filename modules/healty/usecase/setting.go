package usecase

type defaultHealtyService struct {
	healtyRepo HealtyRepository
}

func Setup() *defaultHealtyService {
	return &defaultHealtyService{}
}

func (s *defaultHealtyService) SetHealtyRepo(t HealtyRepository) *defaultHealtyService {
	s.healtyRepo = t
	return s
}

func (s *defaultHealtyService) Validate() HealtyService {
	if s.healtyRepo == nil {
		panic("Healty Repo is nil")
	}

	return s
}
