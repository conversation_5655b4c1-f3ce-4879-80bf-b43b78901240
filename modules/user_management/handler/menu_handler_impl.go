package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	user "repo.nusatek.id/nusatek/payment/modules/user_management/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/response"
)

type UserManagementHandler struct {
	userService user.UserManagementUsecase
}

func NewHandler(userService user.UserManagementUsecase) *UserManagementHandler {
	return &UserManagementHandler{userService}
}

func (h *UserManagementHandler) CreateUserMenu(c *fiber.Ctx) (err error) {
	var req domain.Menus
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusInternalServerError, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.CreateUserMenu(c.UserContext(), &req)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) UpdateUserMenu(c *fiber.Ctx) (err error) {
	var req domain.Menus
	id := c.Params("id")
	err = c.BodyParser(&req)
	if err != nil {
		return errors.SetError(http.StatusBadRequest, err.Error())
	}
	err = utils.ValidateStruct(req)
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, err.Error())
	}
	err = h.userService.UpdateUserMenu(c.UserContext(), &req, id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, req)
}

func (h *UserManagementHandler) DeleteUserMenu(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	err = h.userService.DeleteUserMenuById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, nil)
}

func (h *UserManagementHandler) GetUserMenuById(c *fiber.Ctx) (err error) {
	id := c.Params("id")
	resp, err := h.userService.GetUserMenuById(c.UserContext(), id)
	if err != nil {
		return
	}
	return response.HandleSuccess(c, resp)
}
