package repository

import (
	"context"
	"strings"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product/entity"
	"repo.nusatek.id/nusatek/payment/modules/company_product/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
)

type defaultCompanyProduct struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.CompanyProductRepo {
	return &defaultCompanyProduct{db}
}

func (r *defaultCompanyProduct) CreateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultCompanyProduct) UpdateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultCompanyProduct) GetCompanyProductById(ctx context.Context, id int) (resp *domain.CompanyProducts, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultCompanyProduct) SelectByIds(ctx context.Context, ids []int) (resp []domain.CompanyProducts, err error) {
	err = r.db.Where("id IN ?", ids).Table("company_products").Order("created_at").Scan(&resp).Error

	return resp, err
}

func (r *defaultCompanyProduct) DeleteCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("company_product_id = ?", req.ID).Delete(&domain.CashInTransactions{}).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultCompanyProduct) GetCompanyProductByCode(ctx context.Context, code string) (resp *domain.CompanyProducts, err error) {
	err = r.db.WithContext(ctx).Where("code = ?", code).Take(&resp).Error
	return
}

func (r *defaultCompanyProduct) SearchCompanyProduct(ctx context.Context, paginate utils.Pagination) (resp *[]entity.CompanyProductShow, totalData int64, err error) {
	joinQuery := "JOIN companies s ON p.company_id = s.id"
	query := func(db *gorm.DB) *gorm.DB {
		condision := db.Where("p.deleted_at IS NULL")
		if paginate.Value != "" {
			searchValue := "%" + paginate.Value + "%"
			switch paginate.Key {
			case "company_name":
				condision.Where("s.\"name\" ILIKE ?", searchValue)
			case "product_name":
				condision.Where("p.product_name ILIKE ?", searchValue)
			case "product_code":
				condision.Where("p.code ILIKE ?", searchValue)
			case "admin_fee":
				condision.Where("p.admin_fee = ?", paginate.Value)
			case "status":
				condision.Where("p.status = ?", paginate.Value)
			case "company_id":
				condision.Where("p.company_id = ?", paginate.Value)
			default:
				condision.Where("CONCAT(p.product_name, ' ', s.\"name\", ' ', p.code) ILIKE ?", searchValue)
			}
		}
		if paginate.Search != "" {
			searchValue := "%" + paginate.Search + "%"
			condision.Where("p.code ILIKE ? OR p.product_name ILIKE ?", searchValue, searchValue)
		}

		return condision
	}

	// Update untuk memperbaiki Order
	orderBy := paginate.GetOrderBy("")

	// Map kolom alias yang diperbolehkan
	allowedColumns := map[string]string{
		"company_name":            "s.\"name\"",
		"product_name":            "p.product_name",
		"product_code":            "p.code",
		"admin_fee":               "p.admin_fee",
		"status":                  "p.status",
		"fee_fix_value":           "p.fee_fix_value",
		"fee_percentage":          "p.fee_percentage",
		"cashout_fee_fix_value":   "p.cashout_fee_fix_value",
		"cashout_fee_percentage ": "p.cashout_fee_percentage",
		"created_at ":             "p.created_at ",
	}

	// Cek apakah kolom diizinkan dan ubah ke alias yang sesuai
	for key, alias := range allowedColumns {
		if strings.Contains(orderBy, key) {
			orderBy = alias
			break
		}
	}

	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("company_products p").
		Select("p.id, p.company_id, p.product_name, p.fee_fix_value, p.fee_percentage, p.cashout_fee_fix_value, p.cashout_fee_percentage, s.\"name\" as company_name, p.status, p.code as product_code").
		Joins(joinQuery).Scopes(query).Order(orderBy).Find(&resp).Error
	if err != nil {
		return
	}

	err = r.db.WithContext(ctx).Table("company_products p").Joins(joinQuery).
		Scopes(query).Count(&totalData).Error
	return
}

func (r *defaultCompanyProduct) GetCompanyProductListAll(ctx context.Context) (resp *[]entity.CompanyProductShow, totalData int64, err error) {
	joinQuery := "JOIN companies s ON p.company_id = s.id"
	err = r.db.WithContext(ctx).Table("company_products p").Select("p.id, p.company_id, p.product_name, p.fee_fix_value, p.fee_percentage, p.cashout_fee_fix_value, p.cashout_fee_percentage, s.\"name\" as company_name, p.status, p.code as product_code").
		Joins(joinQuery).Where("p.deleted_at IS NULL").Order("p.updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}
