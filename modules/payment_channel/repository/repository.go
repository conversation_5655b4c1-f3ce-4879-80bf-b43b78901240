package handler

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/payment_channel/entity"
	"repo.nusatek.id/nusatek/payment/modules/payment_channel/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
)

type defaultPaymentChannel struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.PaymentChannelRepository {
	return &defaultPaymentChannel{db}
}

func (r *defaultPaymentChannel) CreatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultPaymentChannel) UpdatePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error) {
	err = r.db.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultPaymentChannel) GetPaymentChannelById(ctx context.Context, id string) (resp *entity.PaymentChannelShow, err error) {
	// err = r.db.WithContext(ctx).Model(&domain.PaymentChannels{}).
	err = r.db.Debug().WithContext(ctx).Table("payment_channels c").
		Select("c.*, files.url AS logo_url, t.payment_type as payment_channel_type_name").
		Joins("JOIN payment_channel_types t ON c.payment_channel_type_id = t.id").
		Joins("LEFT JOIN files ON files.id = c.logo_id").
		First(&resp, id).Error
	return
}

func (r *defaultPaymentChannel) CheckExistingDataRelation(ctx context.Context, paymentChannelID int) (exist bool, err error) {
	var partnerID int
	err = r.db.Raw("SELECT id FROM partners WHERE payment_channel_id = ? AND deleted_at IS NULL LIMIT 1", paymentChannelID).Scan(&partnerID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var paymentProviderChannelMappingID int
	err = r.db.Raw("SELECT id FROM payment_provider_channel_mappings WHERE payment_channel_id = ? AND deleted_at IS NULL LIMIT 1", paymentChannelID).Scan(&paymentProviderChannelMappingID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}

	var companyPaymentProviderChannelMappingID int
	err = r.db.Raw("SELECT id FROM company_payment_provider_channel_mappings WHERE payment_channel_id = ? AND deleted_at IS NULL LIMIT 1", companyPaymentProviderChannelMappingID).Scan(&partnerID).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return false, err
	}
	exist = (partnerID > 0) || (paymentProviderChannelMappingID > 0) || companyPaymentProviderChannelMappingID > 0
	return exist, nil
}

func (r *defaultPaymentChannel) DeletePaymentChannel(ctx context.Context, req *domain.PaymentChannels) (err error) {
	tx := r.db.Begin()
	defer tx.Rollback()

	err = tx.WithContext(ctx).Where("payment_channel_id = ?", req.ID).Delete(&domain.PaymentProviderChannelMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Where("payment_channel_id = ?", req.ID).Delete(&domain.CompanyPaymentProviderChannelMappings{}).Error
	if err != nil {
		return
	}
	err = tx.WithContext(ctx).Delete(req).Error
	if err != nil {
		return
	}

	tx.Commit()
	return
}

func (r *defaultPaymentChannel) SearchPaymentChannel(ctx context.Context, paginate utils.Pagination) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	query := func(db *gorm.DB) *gorm.DB {
		condition := db.Where("c.deleted_at IS NULL")
		if paginate.Value != "" {
			switch paginate.Key {
			case "channel_name":
				condition.Where("c.\"name\" ILIKE ?", "%"+paginate.Value+"%")
			case "status":
				condition.Where("c.status = ?", paginate.Value)
			case "payment_type":
				condition.Where(`t.payment_type ILIKE ?`, "%"+paginate.Value+"%")
			default:
				condition.Where(`c."name" ILIKE ? OR t.payment_type ILIKE ?`, "%"+paginate.Value+"%", "%"+paginate.Value+"%")
			}
		}

		return condition
	}
	orderByMap := map[string]string{
		"logo_url":                  "files.url",
		"name":                      "c.name",
		"payment_channel_type_name": "t.payment_type",
		"status":                    "c.status",
	}

	// Buat OrderBy dengan validasi alias
	orderBy := paginate.GetOrderByMaps("c", orderByMap)
	err = r.db.Debug().WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("payment_channels c").
		Select(`c.id, c.name, c.logo_id, c.status, t.payment_type as payment_channel_type_name,
		t.id as payment_channel_type_id, files.url AS logo_url`).
		Joins("JOIN payment_channel_types t ON c.payment_channel_type_id = t.id").
		Joins("LEFT JOIN files ON files.id = c.logo_id").
		Scopes(query).Order(orderBy).Find(&resp).Error
	if err != nil {
		return
	}

	err = r.db.WithContext(ctx).Table("payment_channels c").
		Joins("JOIN payment_channel_types t ON c.payment_channel_type_id = t.id").
		Scopes(query).Count(&totalData).Error

	return
}

func (r *defaultPaymentChannel) GetPaymentChannelListAll(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentChannels{}).
		Select("payment_channels.*, files.url AS logo_url").
		Joins("LEFT JOIN files ON files.id = payment_channels.logo_id").
		Order("payment_channels.updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentChannel) GetPaymentChannelListAllActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentChannels{}).
		Select("payment_channels.*, files.url AS logo_url").
		Joins("LEFT JOIN files ON files.id = payment_channels.logo_id").
		Where("payment_channels.status = true").Order("payment_channels.updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentChannel) GetPaymentChannelListAllNotActive(ctx context.Context) (resp *[]entity.PaymentChannelShow, totalData int64, err error) {
	err = r.db.WithContext(ctx).Model(domain.PaymentChannels{}).
		Select("payment_channels.*, files.url AS logo_url").
		Joins("LEFT JOIN files ON files.id = payment_channels.logo_id").
		Where("payment_channels.status = false").Order("payment_channels.updated_at DESC").Count(&totalData).Find(&resp).Error
	return
}

func (r *defaultPaymentChannel) GetPaymentChannelTypeById(ctx context.Context, id string) (resp *domain.PaymentChannelTypes, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultPaymentChannel) GetListPaymentChannelType(ctx context.Context) (resp *[]domain.PaymentChannelTypes, err error) {
	err = r.db.WithContext(ctx).Find(&resp).Error
	return
}

func (r *defaultPaymentChannel) SelectByIds(ctx context.Context, ids []int) (resp []domain.PaymentChannels, err error) {
	err = r.db.Where("id IN ?", ids).Table("payment_channels").Order("created_at").Scan(&resp).Error
	return resp, err
}

func (r *defaultPaymentChannel) UpdatePaymentChannelStatus(ctx context.Context, id int, status bool) (err error) {
	err = r.db.WithContext(ctx).Model(&domain.PaymentChannels{}).Where("id = ?", id).Update("status", status).Error
	return
}
