module repo.nusatek.id/nusatek/payment

go 1.23.3

toolchain go1.23.4

require (
	github.com/AfterShip/email-verifier v1.4.1
	github.com/aws/aws-sdk-go v1.43.41
	github.com/cenkalti/backoff/v4 v4.2.1
	github.com/dgryski/dgoogauth v0.0.0-**************-5a805980a5f3
	github.com/go-co-op/gocron v1.11.0
	github.com/go-playground/validator v9.31.0+incompatible
	github.com/go-playground/validator/v10 v10.9.0
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-resty/resty/v2 v2.16.3
	github.com/gofiber/fiber/v2 v2.52.4
	github.com/gofiber/jwt/v3 v3.2.6
	github.com/golang-jwt/jwt/v4 v4.3.0
	github.com/google/uuid v1.6.0
	github.com/leekchan/accounting v1.0.0
	github.com/lib/pq v1.10.9
	github.com/mailgun/mailgun-go/v4 v4.12.0
	github.com/mitchellh/mapstructure v1.4.2
	github.com/oklog/ulid v1.3.1
	github.com/satori/go.uuid v1.2.0
	github.com/spf13/viper v1.9.0
	github.com/streadway/amqp v1.1.0
	github.com/xuri/excelize/v2 v2.7.0
	go.uber.org/zap v1.27.0
	golang.org/x/time v0.8.0
	gorm.io/datatypes v1.2.5
	gorm.io/driver/postgres v1.5.7
	gorm.io/gorm v1.25.12
	repo.nusatek.id/moaja/backend/libraries/database-handler v1.6.1
	repo.nusatek.id/moaja/backend/libraries/logger v1.0.1-0.**************-c48625730d7c
	repo.nusatek.id/moaja/backend/libraries/logr v0.0.3
	repo.nusatek.id/moaja/backend/libraries/message-broker v1.6.10
	repo.nusatek.id/moaja/backend/libraries/rest-utils v1.1.1
	repo.nusatek.id/moaja/backend/libraries/service-config v1.5.4
	repo.nusatek.id/moaja/backend/libraries/service-utils v0.0.8
	repo.nusatek.id/moaja/backend/services/entities v1.12.7
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cockroachdb/apd v1.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fsnotify/fsnotify v1.5.1 // indirect
	github.com/go-chi/chi/v5 v5.0.8 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20231201235250-de7065d80cb9 // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/magiconair/properties v1.8.5 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/pelletier/go-toml v1.9.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rogpeppe/go-internal v1.10.0 // indirect
	github.com/rs/zerolog v1.32.0 // indirect
	github.com/shopspring/decimal v0.0.0-20180709203117-cd690d0c9e24 // indirect
	github.com/spf13/afero v1.6.0 // indirect
	github.com/spf13/cast v1.4.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.52.0 // indirect
	github.com/valyala/tcplisten v1.0.0 // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/ini.v1 v1.63.2 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gorm.io/driver/mysql v1.5.6 // indirect
)
