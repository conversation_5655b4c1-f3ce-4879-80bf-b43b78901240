package cashlez

import "strings"

const (
	APPROVAL_STATUS_APPROVED = "100"
	APPROVAL_STATUS_SETTLED  = "104"
	APPROVAL_STATUS_PENDING  = "200"
)

type CallbackOfflinePG struct {
	Status               string      `json:"status"`
	ResponseCode         string      `json:"response_code"`
	ApprovalStatus       string      `json:"approval_status"`
	MerchantTrxID        string      `json:"merchant_trx_id"`
	HostResponseCode     interface{} `json:"host_response_code"`
	ErrorMessage         interface{} `json:"error_message"`
	CashlezAppID         string      `json:"cashlez_app_id"`
	ApprovalCode         interface{} `json:"approval_code"`
	CashlezTransactionID string      `json:"cashlez_transaction_id"`
	BatchNumber          interface{} `json:"batch_number"`
	ApprovedAmount       int         `json:"approved_amount"`
	ApprovedAmountExtra  interface{} `json:"approved_amount_extra"`
	ApprovedCurrencyCode string      `json:"approved_currency_code"`
	PaymentType          string      `json:"payment_type"`
	MaskedPan            interface{} `json:"masked_pan"`
	Rrn                  interface{} `json:"rrn"`
	TransactionDatetime  string      `json:"transaction_datetime"`
	AcquirerBankName     interface{} `json:"acquirer_bank_name"`
	Email                interface{} `json:"email"`
	NoHandphone          interface{} `json:"no_handphone"`
	Longitude            float64     `json:"longitude"`
	Latitude             float64     `json:"latitude"`
	RedeemedAmount       interface{} `json:"redeemed_amount"`
	RedeemedPoint        interface{} `json:"redeemed_point"`
	BalanceAmount        interface{} `json:"balance_amount"`
	BalancePoint         interface{} `json:"balance_point"`
	BeforeBalance        interface{} `json:"before_balance"`
	LastBalance          interface{} `json:"last_balance"`
	ExpiredDate          string      `json:"expired_date"`
	VaNumber             string      `json:"va_number"`
	PaymentURL           interface{} `json:"payment_url"`
}

type DoReq struct {
	Header  map[string]string
	Method  string
	Path    string
	ReqBody interface{}
}

type baseResp struct {
	Errors  map[string]string `json:"errors"`
	Message string            `json:"message"`
	Status  string            `json:"status"`
}

type ibaseResp interface {
	getBaseResp() baseResp
}

func (b baseResp) getBaseResp() baseResp {

	return b
}

func (b baseResp) Error() string {
	errMsg := b.Message

	var errstrs []string

	for k, v := range b.Errors {
		errstrs = append(errstrs, k+":"+v)
	}

	errMsg += " " + strings.Join(errstrs, ",")

	return errMsg
}

type EncryptReq struct {
	Password string `json:"password"`
}

type EncryptResp struct {
	baseResp
	EncryptedPassword string `json:"encrypted_password"`
}

type LoginReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResp struct {
	baseResp
	Token string `json:"token"`
	User  struct {
		UsersID              string      `json:"users_id"`
		Salutation           string      `json:"salutation"`
		FirstName            string      `json:"first_name"`
		MiddleName           interface{} `json:"middle_name"`
		LastName             interface{} `json:"last_name"`
		Email                string      `json:"email"`
		Username             string      `json:"username"`
		Contact              string      `json:"contact"`
		ActivateDate         string      `json:"activate_date"`
		SuspendDate          interface{} `json:"suspend_date"`
		Pic                  int         `json:"pic"`
		MerchantID           string      `json:"merchant_id"`
		ControlID            string      `json:"control_id"`
		UserLoginStatus      string      `json:"user_login_status"`
		LastLogin            string      `json:"last_login"`
		IPAddress            string      `json:"ip_address"`
		CompanyName          string      `json:"company_name"`
		MerchantName         string      `json:"merchant_name"`
		UniqueMerchantCode   string      `json:"unique_merchant_code"`
		TrxCallbackURL       string      `json:"trx_callback_url"`
		Permission           string      `json:"permission"`
		IsManageChild        int         `json:"is_manage_child"`
		Role                 string      `json:"role"`
		Control              string      `json:"control"`
		Npwp                 string      `json:"npwp"`
		Ktp                  interface{} `json:"ktp"`
		NpwpURL              interface{} `json:"npwp_url"`
		KtpURL               interface{} `json:"ktp_url"`
		SelfieURL            interface{} `json:"selfie_url"`
		SignURL              interface{} `json:"sign_url"`
		Birthplace           interface{} `json:"birthplace"`
		Birthdate            string      `json:"birthdate"`
		UserAddress1         string      `json:"user_address1"`
		UserAddress2         string      `json:"user_address2"`
		UserCity             string      `json:"user_city"`
		UserState            string      `json:"user_state"`
		UserDistrict         string      `json:"user_district"`
		UserPostcode         string      `json:"user_postcode"`
		BusinessRelationType string      `json:"business_relation_type"`
		RoleID               string      `json:"role_id"`
		IsActive             int         `json:"is_active"`
		BankID               string      `json:"bank_id"`
		BankName             string      `json:"bank_name"`
		MerchantLoanEligible int         `json:"merchant_loan_eligible"`
		MerchantCodePremium  int         `json:"merchant_code_premium"`
		HadPremium           string      `json:"had_premium"`
		SubscriptionStatus   string      `json:"subscription_status"`
		PlanName             string      `json:"plan_name"`
		ExpiryDate           string      `json:"expiry_date"`
		AccessToken          string      `json:"access_token"`
		MobileUserPremium    string      `json:"mobile_user_premium"`
	} `json:"user"`
	Premium struct {
		HadPremium          string `json:"had_premium"`
		SubscriptionStatus  string `json:"subscription_status"`
		PlanName            string `json:"plan_name"`
		AccessToken         string `json:"access_token"`
		ExpiryDate          string `json:"expiry_date"`
		MerchantCodePremium int    `json:"merchant_code_premium"`
		MobileUserPremium   string `json:"mobile_user_premium"`
	} `json:"premium"`
}
type TransactionAdvanceSearchReq struct {
	Txid         string `json:"txid"`
	MerchantTxid string `json:"merchant_txid"`
	Order        string `json:"order"`
	Sort         string `json:"sort"`
	Limit        int    `json:"limit"`
}

type TransactionAdvanceSearchResp struct {
	baseResp
	Data                   []TransactionAdvanceSearchRespData `json:"data"`
	Total                  int                                `json:"total"`
	TotalTransactionAmount int                                `json:"totalTransactionAmount"`
}

type TransactionAdvanceSearchRespData struct {
	TransactionAmount           int         `json:"transaction_amount"`
	MerchantPayment             interface{} `json:"merchant_payment"`
	TaxPayment                  interface{} `json:"tax_payment"`
	FeeMerchant                 interface{} `json:"fee_merchant"`
	UsernameVoid                interface{} `json:"username_void"`
	TransStatus                 string      `json:"trans_status"`
	TransactionType             string      `json:"transaction_type"`
	NumberVa                    string      `json:"number_va"`
	BankName                    string      `json:"bank_name"`
	MerchantID                  string      `json:"merchant_id"`
	CompanyName                 string      `json:"company_name"`
	MerchantName                string      `json:"merchant_name"`
	MerchantReference           string      `json:"merchant_reference"`
	City                        string      `json:"city"`
	State                       string      `json:"state"`
	MerchantUniqueCode          string      `json:"merchant_unique_code"`
	ApprovalDate                string      `json:"approval_date"`
	AppID                       string      `json:"app_id"`
	Applicationidentifier       string      `json:"applicationidentifier"`
	Txid                        string      `json:"txid"`
	Tid                         string      `json:"tid"`
	CreatedDate                 string      `json:"created_date"`
	AuthCode                    interface{} `json:"auth_code"`
	Pan                         interface{} `json:"pan"`
	RetrievalReferenceNumber    interface{} `json:"retrieval_reference_number"`
	OldRetrievalReferenceNumber interface{} `json:"old_retrieval_reference_number"`
	Status                      int         `json:"status"`
	TransactionAuthorizedID     string      `json:"transaction_authorized_id"`
	Trace                       int         `json:"trace"`
	SettlementDate              interface{} `json:"settlement_date"`
	VoidedDate                  interface{} `json:"voided_date"`
	Mid                         string      `json:"mid"`
	CardHolderName              interface{} `json:"card_holder_name"`
	CardType                    string      `json:"card_type"`
	ApplicationLabel            string      `json:"application_label"`
	AuthResponseCode            interface{} `json:"auth_response_code"`
	Stan                        int         `json:"stan"`
	Aidcc                       interface{} `json:"aidcc"`
	SourceTransferBankCode      interface{} `json:"source_transfer_bank_code"`
	SourceTransferBankName      interface{} `json:"source_transfer_bank_name"`
	AppBankName                 interface{} `json:"app_bank_name"`
	DestTransferBankCode        interface{} `json:"dest_transfer_bank_code"`
	DestTransferBankName        interface{} `json:"dest_transfer_bank_name"`
	DestTransferAccountNo       interface{} `json:"dest_transfer_account_no"`
	SourceTransferCustName      interface{} `json:"source_transfer_cust_name"`
	DestTransferCustName        interface{} `json:"dest_transfer_cust_name"`
	PosEntryMode                interface{} `json:"pos_entry_mode"`
	MerchantTxid                string      `json:"merchant_txid"`
	Discount                    interface{} `json:"discount"`
	PriceIncludeTax             interface{} `json:"price_include_tax"`
	ServiceCharge               interface{} `json:"service_charge"`
	SubTotal                    interface{} `json:"sub_total"`
	Tax                         interface{} `json:"tax"`
	TaxPercentage               interface{} `json:"tax_percentage"`
	ServiceChargePercentage     interface{} `json:"service_charge_percentage"`
	ServiceChargeFixed          interface{} `json:"service_charge_fixed"`
	DiscountPercentage          interface{} `json:"discount_percentage"`
	DiscountFixed               interface{} `json:"discount_fixed"`
	TaxName                     interface{} `json:"tax_name"`
	Muid                        string      `json:"muid"`
	MidType                     string      `json:"mid_type"`
	MiniAtmTransfer             int         `json:"mini_atm_transfer"`
	Did                         string      `json:"did"`
	Latitude                    string      `json:"latitude"`
	Longitude                   string      `json:"longitude"`
	RoutingType                 interface{} `json:"routing_type"`
	CardOnUsOffUs               interface{} `json:"card_on_us_off_us"`
	NonCardOnUsOffUs            interface{} `json:"non_card_on_us_off_us"`
	ItemDesc                    string      `json:"item_desc"`
	ItemPicture                 interface{} `json:"item_picture"`
	CustomerName                interface{} `json:"customer_name"`
	CustomerEmail               interface{} `json:"customer_email"`
	CustomerMobilePhone         interface{} `json:"customer_mobile_phone"`
	EmoneyFtpFilename           interface{} `json:"emoney_ftp_filename"`
	TransactionFromCashlezLink  string      `json:"transaction_from_cashlez_link"`
	IsManualOffInstallment      string      `json:"is_manual_off_installment"`
	ManualOffInstallmentBank    string      `json:"manual_off_installment_bank"`
	ManualOffInstallmentTenor   interface{} `json:"manual_off_installment_tenor"`
}
