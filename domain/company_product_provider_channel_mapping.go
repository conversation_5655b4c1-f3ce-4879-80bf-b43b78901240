package domain

import "time"

type CompanyProductProviderChannelMapping struct {
	ID                                     int
	CompanyProductID                       int
	CompanyPaymentProviderChannelMappingID int
	FeeFixValue                            float64 // product fee per channel
	FeePercentage                          float64 // product fee per channel
	Status                                 bool
	CreatedAt                              time.Time
	UpdatedAt                              time.Time
	// CompanyProduct
	CompanyProductCode string
	CompanyProductName string
	//CompanyPaymentProviderChannelMapping
	CompanyProviderChannelMappingID            int
	CompanyProviderChannelMappingCode          string
	CompanyProviderChannelMappingProviderCode  string
	CompanyProviderChannelMappingFeeFixValue   float64
	CompanyProviderChannelMappingFeePercentage float64
	//PaymentProvider
	PaymentProviderID   int
	PaymentProviderCode string
	PaymentProviderName string
	//PaymentChannel
	PaymentChannelID   int
	PaymentChannelName string
}

type CompanyProductProviderChannelMappingCalculate struct {
	CompanyProductProviderChannelMapping CompanyProductProviderChannelMapping
	CalculatedAmount                     float64
	AdminFee                             float64
}
