package repository

import (
	"context"

	"gorm.io/gorm"
	"repo.nusatek.id/nusatek/payment/domain"
)

func (r *defaultUserManagement) CreateUserCompany(ctx context.Context, req *domain.UserCompanies, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defaultUserManagement) UpdateUserCompany(ctx context.Context, req *domain.UserCompanies, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *defaultUserManagement) GetUserCompanyById(ctx context.Context, id string) (resp *domain.UserCompanies, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defaultUserManagement) GetUserCompanyByUserId(ctx context.Context, id string) (resp *[]domain.UserCompanies, err error) {
	err = r.db.WithContext(ctx).Where("user_id = ?", id).Find(&resp).Error
	return
}

func (r *defaultUserManagement) DeleteUserCompany(ctx context.Context, req *domain.UserCompanies) (err error) {
	err = r.db.WithContext(ctx).Delete(req).Error
	return
}

func (r *defaultUserManagement) DeleteUserCompanyByUserId(ctx context.Context, id int, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Where("user_id = ?", id).Delete(&domain.UserCompanies{}).Error
	return
}
