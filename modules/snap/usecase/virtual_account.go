package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/snaputil"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

var (
	partnerIdWhitelist = map[string]bool{
		"10687": true,
	}
	channelIdWhitelist = map[string]bool{
		"95231": true,
	}
)

func (uc defaultUC) validatePartner(ctx context.Context, br *domain.SnapBaseResp) (err error) {
	hdr := snaputil.GetHeaderReq(ctx)

	// validate
	_, exist := partnerIdWhitelist[hdr.XPartnerId]
	if !exist {
		err = br.Set(http.StatusUnauthorized, "00", "Unauthorized. [Unknown client]")
		return
	}

	_, exist = channelIdWhitelist[hdr.ChannelId]
	if !exist {
		err = br.Set(http.StatusUnauthorized, "00", "Unauthorized. [Unknown client]")
		return
	}

	return nil
}

func (uc defaultUC) TransferVaInquiry(ctx context.Context, req domain.SnapTransferVaInquiryReq) (resp domain.SnapTransferVaInquiryResp, err error) {
	ctx = logger.AppendCtxData(ctx, "vaNo", req.VirtualAccountNo)
	ctx = logger.AppendCtxData(ctx, "custNo", req.CustomerNo)

	defer func() {
		logger.Info(ctx, "TransferVaInquiry resp", logger.Any("resp", resp))
	}()
	logger.Info(ctx, "TransferVaInquiry req", logger.Any("req", req))
	resp.SnapBaseResp = snaputil.NewBaseRespCtx(ctx)
	resp = req.ToResp(&resp.SnapBaseResp)

	ok, err := uc.validateExternalID(ctx, &resp.SnapBaseResp)
	if err != nil {
		return
	}
	if !ok {
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = domain.SnapLanguage{
			English:   "Cannot use the same X-EXTERNAL-ID",
			Indonesia: "Tidak bisa menggunakan X-EXTERNAL-ID yang sama",
		}
		err = resp.Set(http.StatusConflict, "00", "Conflict")
		return
	}

	err = uc.validatePartner(ctx, &resp.SnapBaseResp)
	if err != nil {
		resp.VirtualAccountData = nil
		return
	}

	err = req.Validate(ctx, &resp)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error req validate %v", err))
		return
	}

	vaNo := strings.TrimSpace(req.VirtualAccountNo)
	if len(vaNo) <= 0 {
		logger.Error(ctx, "empty va")
		err = resp.Set(http.StatusBadRequest, "02", "Invalid Mandatory Field {Virtual Account No}")
		return
	}

	custNo := strings.TrimSpace(req.CustomerNo)
	if len(custNo) <= 0 {
		logger.Error(ctx, "empty custNo")
		err = resp.Set(http.StatusBadRequest, "02", "Invalid Mandatory Field {Customer No}")
		return
	}

	// save inquiry req id
	client := snaputil.GetClient(ctx)
	// duration := time.Until(time.Now().Add(24 * time.Hour))
	// err = uc.cache.Set(ctx, fmt.Sprintf("snap_transfer_va_req_id:%s_%s", client.Id, custNo), req.InquiryRequestId, duration).Err()
	// if err != nil {
	// 	logger.Error(ctx, fmt.Sprintf("error save payment and external id %v", err))
	// 	err = resp.Set(http.StatusInternalServerError, "00", "General Error")
	// 	return
	// }

	header := snaputil.GetHeaderReq(ctx)
	// get payment channel mapping id by partner id
	mappingId, err := uc.repo.GetOneCompanyProductProviderChannelMappingBySnapCompCode(ctx, header.XPartnerId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("company product provider not found %v", err))
		err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Partner id not found]")
		return
	}

	// get consumer fixed va
	cust, err := uc.repo.GetOneCustomerVaByNumber(ctx, mappingId.ID, vaNo)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("customer not found %v", err))
		if err == gorm.ErrRecordNotFound {
			resp.VA().InquiryStatus = "01"
			resp.VA().InquiryReason = domain.SnapLanguage{
				English:   "Bill not found",
				Indonesia: "Tagihan tidak ditemukan",
			}
			err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Not Found]")
			return
		}
		err = resp.Set(http.StatusNotFound, "00", "General Error")
		return
	}

	if len(cust.LastPgReferenceId) <= 0 {
		logger.Warn(ctx, "cust.LastPgReferenceId is empty")
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = domain.SnapLanguage{
			English:   "Bill not found",
			Indonesia: "Tagihan tidak ditemukan",
		}
		err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Not Found]")
		return
	}

	// get cash in transaction
	cashIn, err := uc.repo.FnWrapper().GetCashinTransactionByInvoiceNumber(ctx, cust.LastPgReferenceId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("GetOneCashInByVa %v", err))
		if err == gorm.ErrRecordNotFound {
			resp.VA().InquiryStatus = "01"
			resp.VA().InquiryReason = domain.SnapLanguage{
				English:   "Bill not found",
				Indonesia: "Tagihan tidak ditemukan",
			}
			err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Not Found]")
			return
		}
		err = resp.Set(http.StatusNotFound, "00", "General Error")
		return
	}
	go func() { // update http log
		uc.trxReqLogService.UpdateResourceCtx(ctx, cashIn.InvoiceNumber, logutil.ResourceTypeCashIn)
	}()

	// save inquiry req id
	vaReq := domain.SnapVaRequestId{
		ClientId:            client.Id,
		RequestId:           req.InquiryRequestId,
		VaNumber:            cust.VANumber,
		CashInTransactionId: cashIn.ID,
	}
	err = uc.repo.InsertVaRequestId(ctx, &vaReq)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error save inquiry request id %v", err))
		err = resp.Set(http.StatusInternalServerError, "00", "General Error")
		return
	}

	if str.Contains([]string{constants.PaymentPaid, constants.PaymentSettled}, cashIn.PaymentStatus) {
		logger.Error(ctx, "cash in has been paid")
		resp.VirtualAccountData.InquiryReason = domain.SnapLanguage{
			English:   "Bill has been paid",
			Indonesia: "Tagihan sudah dibayarkan",
		}
		err = resp.Set(http.StatusNotFound, "14", "Paid Bill")
		return
	}

	if cashIn.IsExpired() {
		logger.Error(ctx, "cash in expired")
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = domain.SnapLanguage{
			English:   "Bill expired",
			Indonesia: "Tagihan kedaluwarsa",
		}
		err = resp.Set(http.StatusNotFound, "19", "Invalid Bill/Virtual Account")
		return
	}

	if cashIn.PaymentStatus == constants.PaymentFailed {
		logger.Error(ctx, "cash in failed")
		resp.VA().InquiryStatus = "01"
		resp.VA().InquiryReason = domain.SnapLanguage{
			English:   "Payment failed",
			Indonesia: "Transaksi gagal",
		}
		err = resp.Set(http.StatusNotFound, "19", "Invalid Bill/Virtual Account")
		return
	}

	// TODO: save relation between snap and cash in transactions
	resp.VA().InquiryStatus = "00"
	resp.VA().InquiryReason = domain.SnapLanguage{
		English:   "Success",
		Indonesia: "Sukses",
	}
	resp.VA().SubCompany = "00000"
	resp.VA().VirtualAccountTrxType = "C" // C = Closed Payment

	resp.VA().InjectCashIn(*cashIn)

	return
}

var paymentReqIdCacheKey = "snap_payment_req_and_ext_id:%s_%s_%s" // client id , payment req id, external_id

func (uc defaultUC) validatePaymentRequestId(ctx context.Context, req domain.SnapTransferVaPaymentReq, resp *domain.SnapTransferVaPaymentResp) (err error) {
	client := snaputil.GetClient(ctx)
	header := snaputil.GetHeaderReq(ctx)

	loc, _ := time.LoadLocation(config.GetString("tz"))
	now := time.Now().In(loc)
	end := time.Date(now.Year(), now.Month(), now.Day()+3, 24, 0, 0, 0, loc) // save in 3 days
	duration := end.Sub(now)
	ok, err := uc.cache.SetNX(ctx, fmt.Sprintf(paymentReqIdCacheKey, client.Id, req.PaymentRequestId, header.XExternalId), "", duration).Result()
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error save payment req id %v", err))
		err = resp.Set(http.StatusInternalServerError, "00", "General Error")
		return
	}
	if !ok {
		logger.Error(ctx, fmt.Sprintf("payment %s and ext id %s is used", req.PaymentRequestId, header.XExternalId))
		var paymentRespJson string
		paymentRespJson, err = uc.cache.Get(ctx, fmt.Sprintf(paymentReqIdCacheKey, client.Id, req.PaymentRequestId, header.XExternalId)).Result()
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error get payment req id %v", err))
			err = resp.Set(http.StatusInternalServerError, "00", "General Error")
			return
		}
		_ = json.Unmarshal([]byte(paymentRespJson), resp.VA())
		err = resp.Set(http.StatusNotFound, "18", "Inconsistent Request")
		return
	}

	ok, err = uc.validateExternalID(ctx, &resp.SnapBaseResp)
	if err != nil {
		return
	}
	if !ok {
		resp.VA().PaymentFlagStatus = "01"
		resp.VA().PaymentFlagReason = domain.SnapLanguage{
			English:   "Cannot use the same X-EXTERNAL-ID",
			Indonesia: "Tidak bisa menggunakan X-EXTERNAL-ID yang sama",
		}
		err = resp.Set(http.StatusConflict, "00", "Conflict")
		return
	}

	if len(req.PaymentRequestId) <= 0 {
		logger.Error(ctx, "empty payment req id")
		err = resp.Set(http.StatusBadRequest, "00", "Invalid Mandatory Field {paymentRequestId}")
		return
	}

	if len(header.XExternalId) <= 0 {
		logger.Error(ctx, "empty external id")
		err = resp.Set(http.StatusBadRequest, "00", "Invalid Mandatory Field {X-EXTERNAL-ID}")
		return
	}

	return
}

func (uc defaultUC) TransferVaPayment(ctx context.Context, req domain.SnapTransferVaPaymentReq) (resp domain.SnapTransferVaPaymentResp, err error) {
	ctx = logger.AppendCtxData(ctx, "vaNo", req.VirtualAccountNo)
	ctx = logger.AppendCtxData(ctx, "custNo", req.CustomerNo)
	ctx = logger.AppendCtxData(ctx, "paidAmount", req.PaidAmount.Float64())

	defer func() {
		logger.Info(ctx, "TransferVaPayment resp", logger.Any("resp", resp))
	}()
	logger.Info(ctx, "TransferVaPayment req", logger.Any("req", req))
	resp.SnapBaseResp = snaputil.NewBaseRespCtx(ctx)
	resp = req.ToResp(&resp.SnapBaseResp)

	err = uc.validatePaymentRequestId(ctx, req, &resp)
	if err != nil {
		return
	}

	err = uc.validatePartner(ctx, &resp.SnapBaseResp)
	if err != nil {
		resp.VirtualAccountData = nil
		return
	}

	err = req.Validate(ctx, &resp)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error req validate %v", err))
		return
	}

	vaNo := strings.TrimSpace(req.VirtualAccountNo)
	if len(vaNo) <= 0 {
		logger.Error(ctx, "empty va")
		err = resp.Set(http.StatusBadRequest, "02", "Invalid Mandatory Field {Virtual Account No}")
		return
	}

	custNo := strings.TrimSpace(req.CustomerNo)
	if len(custNo) <= 0 {
		logger.Error(ctx, "empty CustNo")
		err = resp.Set(http.StatusBadRequest, "02", "Invalid Mandatory Field {Customer No}")
		return
	}

	// validate request id
	client := snaputil.GetClient(ctx)
	// reqId, _ := uc.cache.Get(ctx, fmt.Sprintf("snap_transfer_va_req_id:%s_%s", client.Id, custNo)).Result()
	// if reqId != req.PaymentRequestId {
	// 	logger.Error(ctx, fmt.Sprintf("transfer va req id not match %v %v", reqId, req.PaymentRequestId))
	// 	err = resp.Set(http.StatusBadRequest, "00", "Invalid paymentRequestId")
	// 	return
	// }
	// get inquiry req id
	vaReq := domain.SnapVaRequestId{
		ClientId: client.Id,
		VaNumber: vaNo,
	}
	vaReqId, err := uc.repo.GetLastVaRequestId(ctx, vaReq)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error save inquiry request id %v", err))
		err = resp.Set(http.StatusInternalServerError, "00", "General Error")
		return
	}

	if vaReqId.RequestId != req.PaymentRequestId {
		logger.Error(ctx, fmt.Sprintf("transfer va req id not match %v %v", vaReqId.RequestId, req.PaymentRequestId))
		err = resp.Set(http.StatusBadRequest, "00", "Invalid paymentRequestId")
		return
	}

	header := snaputil.GetHeaderReq(ctx)
	// get payment channel mapping id by partner id
	mappingId, err := uc.repo.GetOneCompanyProductProviderChannelMappingBySnapCompCode(ctx, header.XPartnerId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("company product provider not found %v", err))
		err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Partner id not found]")
		return
	}

	cacheRespFn := func() {
		var err error
		loc, _ := time.LoadLocation(config.GetString("tz"))
		now := time.Now().In(loc)
		end := time.Date(now.Year(), now.Month(), now.Day()+3, 24, 0, 0, 0, loc) // save in 3 days
		duration := end.Sub(now)

		respPaymentJsonB, _ := json.Marshal(resp.VirtualAccountData)
		err = uc.cache.Set(ctx, fmt.Sprintf(paymentReqIdCacheKey, client.Id, req.PaymentRequestId, header.XExternalId), string(respPaymentJsonB), duration).Err()
		if err != nil {
			logger.Warn(ctx, fmt.Sprintf("error save payment resp %v", err))
			// err = resp.Set(http.StatusInternalServerError, "00", "General Error")
			return
		}
	}

	// get consumer fixed va
	cust, err := uc.repo.GetOneCustomerVaByNumber(ctx, mappingId.ID, vaNo)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("customer not found %v", err))
		if err == gorm.ErrRecordNotFound {
			resp.VA().PaymentFlagStatus = "01"
			resp.VA().PaymentFlagReason = domain.SnapLanguage{
				English:   "Not registered",
				Indonesia: "Tidak terdaftar",
			}
			err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Not Found]")
			cacheRespFn()
			return
		}
		err = resp.Set(http.StatusNotFound, "00", "General Error")
		return
	}

	// get cash in transaction
	cashIn, err := uc.repo.FnWrapper().GetCashinTransactionByInvoiceNumber(ctx, cust.LastPgReferenceId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("GetOneCashInByVa %v", err))
		if err == gorm.ErrRecordNotFound {
			resp.VA().PaymentFlagStatus = "01"
			resp.VA().PaymentFlagReason = domain.SnapLanguage{
				English:   "Not registered",
				Indonesia: "Tidak terdaftar",
			}
			err = resp.Set(http.StatusNotFound, "12", "Invalid Bill/Virtual Account [Not Found]")
			cacheRespFn()
			return
		}
		err = resp.Set(http.StatusNotFound, "00", "General Error")
		return
	}
	go func() { // update http log
		uc.trxReqLogService.UpdateResourceCtx(ctx, cashIn.InvoiceNumber, logutil.ResourceTypeCashIn)
	}()

	if str.Contains([]string{constants.PaymentPaid, constants.PaymentSettled}, cashIn.PaymentStatus) {
		logger.Error(ctx, "cash in has been paid")
		resp.VA().PaymentFlagReason = domain.SnapLanguage{
			English:   "Bill has been paid",
			Indonesia: "Tagihan sudah dibayarkan",
		}
		resp.VA().PaymentFlagStatus = "01"
		err = resp.Set(http.StatusNotFound, "14", "Paid Bill")
		cacheRespFn()
		return
	}

	if cashIn.IsExpired() {
		logger.Error(ctx, "cash in expired")
		resp.VA().PaymentFlagReason = domain.SnapLanguage{
			English:   "Bill expired",
			Indonesia: "Tagihan kedaluwarsa",
		}
		resp.VA().PaymentFlagStatus = "01"
		err = resp.Set(http.StatusNotFound, "19", "Invalid Bill/Virtual Account")
		cacheRespFn()
		return
	}

	if cashIn.PaymentStatus == constants.PaymentFailed {
		logger.Error(ctx, "cash in failed")
		resp.VA().PaymentFlagStatus = "01"
		resp.VA().PaymentFlagReason = domain.SnapLanguage{
			English:   "Payment failed",
			Indonesia: "Transaksi gagal",
		}
		err = resp.Set(http.StatusNotFound, "19", "Invalid Bill/Virtual Account")
		cacheRespFn()
		return
	}

	resp.VA().PaymentFlagStatus = "00"
	resp.VA().PaymentFlagReason = domain.SnapLanguage{
		English:   "Success",
		Indonesia: "Sukses",
	}

	resp.VA().InjectCashIn(*cashIn)

	// validate amount
	// for fixed va paid amount = total amount
	if resp.VA().TotalAmount.Float64() != req.PaidAmount.Float64() {
		logger.Error(ctx, "invalid amount")
		resp.VA().PaymentFlagStatus = "01"
		resp.VA().PaymentFlagReason = domain.SnapLanguage{
			English:   "Incorrect amount paid",
			Indonesia: "Jumlah yang dibayar tidak sesuai",
		}
		err = resp.Set(http.StatusNotFound, "13", "Invalid Amount")
		cacheRespFn()
		return
	}

	// TODO: need save paymentRequestId from BCA

	// TODO: need to get payment provider name

	// update cash in status to paid using message broker
	cb := cashIn.ToSnapCb(domain.ToSnapCbOpts{
		PaymentProviderName: constants.ProviderBankBCA,
		PartnerId:           header.XPartnerId,
	})
	cb.Request = req
	err = uc.fnWrapper.UpdateStatusSnapCashinTransaction(ctx, cashIn, cb.ToStatus(constants.CashInStatusPaid), domain.UpdateWachingStatusCashinOpts{})
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error UpdateStatusSnapCashinTransaction %s", err))
		err = resp.Set(http.StatusInternalServerError, "00", "General Error")
		return
	}
	cacheRespFn()
	return
}
