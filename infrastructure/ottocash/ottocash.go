package ottocash

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/moaja/backend/libraries/rest-utils"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/utils/config"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
)

type wrapper struct {
	Endpoint           string
	isSandbox          bool
	client             rest.RestClient
	CallbackSuccessURL string
	CallbackFailedURL  string
	CallbackBackURL    string
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Setup() RestOttocash {
	var (
		EndpointSandbox = config.GetString("ottocash.sandbox")
		Endpoint        = config.GetString("ottocash.host")
	)

	w.isSandbox = config.GetBool("ottocash.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	restOptions := rest.Options{
		Address:   w.Endpoint,
		Timeout:   config.GetDuration("ottocash.timeout"),
		SkipTLS:   config.GetBool("ottocash.skiptls"),
		DebugMode: config.GetBool("ottocash.debug"),
	}

	if config.GetString("ottocash.proxy") != "" {
		restOptions.WithProxy = true
		restOptions.ProxyAddress = config.GetString("ottocash.proxy")
	}

	w.client = rest.New(restOptions)
	w.CallbackSuccessURL = config.GetString("ottocash.callback_success_url")
	w.CallbackFailedURL = config.GetString("ottocash.callback_failed_url")
	w.CallbackBackURL = config.GetString("ottocash.callback_back_url")

	return w
}

func setHeader(ctx context.Context, credential Credential, payload interface{}) http.Header {
	timestamp := strconv.Itoa(int(time.Now().UnixMilli()))

	// Generate signature
	signature := SetSignature(ctx, credential.SecretKey, payload, timestamp)

	header := http.Header{}
	header.Add("Content-Type", "application/json")
	header.Add("Timestamp", timestamp)
	header.Add("Apps-ID", credential.AppID)
	header.Add("Signature", signature)

	return header
}

func SetSignature(ctx context.Context, credential string, payload interface{}, timestamp string) string {
	payloadString := interfacepkg.Marshal(payload)
	logger.Info(ctx, "set signature", logger.String("payload", payloadString))
	reg, _ := regexp.Compile("[^a-zA-Z0-9{}:,.]+")
	payloadString = reg.ReplaceAllString(payloadString, "")
	payloadString = strings.ToUpper(payloadString)
	payloadString += ":" + timestamp

	key := []byte(credential)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(payloadString))

	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func (w *wrapper) Inquiry(ctx context.Context, req InquiryRequest, credential Credential) (resp InquiryResponse, err error) {
	path := "/inquirySecurePage"
	logger.Info(ctx, "request", logger.String("url", w.Endpoint+path), logger.Any("req", req))

	req.SuccessURL = w.CallbackSuccessURL
	req.FailedURL = w.CallbackFailedURL
	req.MerchantURL = w.CallbackBackURL

	header := setHeader(ctx, credential, req)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error post", logger.Err(err))
		return
	}

	logger.Info(ctx, "response", logger.Any("body", string(body)))

	if status != http.StatusOK {
		var datas interface{}
		_ = json.Unmarshal(body, &datas)

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Any("resp", datas), logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.Any("resp", string(body)), logger.Err(err))
		return
	}

	if resp.ReferenceNumber == "" {
		var error ErrorResponse
		_ = json.Unmarshal(body, &error)

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "reference number empty", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatus(ctx context.Context, req CheckStatusRequest, credential Credential) (resp CheckStatusResponse, err error) {
	path := "/checkStatusSecurePage"
	logger.Info(ctx, "request", logger.String("url", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(ctx, credential, req)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error post", logger.Err(err))
		return
	}

	logger.Info(ctx, "response", logger.Any("body", string(body)))

	if status != http.StatusOK {
		var datas interface{}
		_ = json.Unmarshal(body, &datas)

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.Err(err))
		return
	}

	return
}
