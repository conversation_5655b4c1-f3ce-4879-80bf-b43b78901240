package usecase

import (
	"github.com/go-redis/redis/v8"
	"repo.nusatek.id/nusatek/payment/utils/s3"
)

type defaultPaymentChannel struct {
	paymentChannelRepo PaymentChannelRepository
	cache              *redis.Client
	s3                 *s3.Credential
}

func Setup() *defaultPaymentChannel {
	return &defaultPaymentChannel{}
}

func (s *defaultPaymentChannel) SetPaymentChannelRepo(t PaymentChannelRepository) *defaultPaymentChannel {
	s.paymentChannelRepo = t
	return s
}

func (s *defaultPaymentChannel) SetRedisClient(t *redis.Client) *defaultPaymentChannel {
	s.cache = t
	return s
}

func (s *defaultPaymentChannel) SetS3(t *s3.Credential) *defaultPaymentChannel {
	s.s3 = t
	return s
}

func (s *defaultPaymentChannel) Validate() PaymentChannelUsecase {
	if s.paymentChannelRepo == nil {
		panic("payment channel repo is nil")
	}

	if s.cache == nil {
		panic("redis client is nil")
	}

	if s.s3 == nil {
		panic("s3 is nil")
	}

	return s
}
