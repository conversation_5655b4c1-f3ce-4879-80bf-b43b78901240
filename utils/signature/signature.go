package signature

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"log"
)

func ParseRsaPublicKeyFromPemStr(pubPEM string) (pub *rsa.PublicKey, err error) {
	block, _ := pem.Decode([]byte(pubPEM))
	if block == nil {
		return nil, errors.New("failed to parse pem block containing the key")
	}

	pub, err = x509.ParsePKCS1PublicKey(block.Bytes)
	if err != nil {
		if err.Error() == "x509: failed to parse public key (use ParsePKIXPublicKey instead for this key format)" {
			log.Println("switch parse method", err)
			pubKey, _ := x509.ParsePKIXPublicKey(block.Bytes)
			switch pubKey := pubKey.(type) {
			case *rsa.PublicKey:
				pub = pubKey
				return pub, nil
			default:
				break // fall through
			}
		}
		return nil, err
	}

	return pub, nil
}

func ParseRsaPrivateKeyFromPemStr(privPEM string) (*rsa.PrivateKey, error) {
	// Parse PEM block
	block, _ := pem.Decode([]byte(privPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key")
	}

	// Parse the key based on the key type
	var key interface{}
	var err error

	switch block.Type {
	case "RSA PRIVATE KEY":
		key, err = x509.ParsePKCS1PrivateKey(block.Bytes)
	case "PRIVATE KEY":
		key, err = x509.ParsePKCS8PrivateKey(block.Bytes)
	default:
		return nil, fmt.Errorf("unsupported key type %q", block.Type)
	}

	if err != nil {
		return nil, err
	}

	rsaKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("expected *rsa.PrivateKey, got %T", key)
	}

	return rsaKey, nil
}

func SignSHA256WithRSA(privateKey *rsa.PrivateKey, data []byte) ([]byte, error) {
	// Calculate the SHA256 hash of the data
	hashed := sha256.Sum256(data)

	// Sign the hash using RSA
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return nil, err
	}

	signature = []byte(base64.StdEncoding.EncodeToString(signature))

	return signature, nil
}

func VerifyPKCS(pubKey *rsa.PublicKey, msg string, base64Signature string) error {
	message := []byte(msg)
	bSignature, err := base64.StdEncoding.DecodeString(base64Signature)

	if err != nil {
		log.Println("Failed to decode signature")
		return err
	}
	hashed := sha256.Sum256(message)

	err = rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, hashed[:], bSignature)
	if err != nil {
		log.Println("error verifying", err)
		return err
	}

	return nil
}
