package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (s *defaultCashinTransaction) RetryCallback(ctx context.Context, id int) (err error) {
	cashInData, err := s.cashinRepo.GetCashinTransactionByIdDetail(ctx, fmt.Sprintf("%d", id))
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	var referenceId = cashInData.InvoiceNumber

	logger.Info(ctx, "cash in info", logger.String("invoiceNumber", cashInData.InvoiceNumber), logger.String("paymentStatus", cashInData.PaymentStatus), logger.String("status", cashInData.Status))

	if cashInData.Status != constants.CashInStatusPending { // if status not pending, just pub the callback message to client
		if errRes := s.PublishCallbackToClient(ctx, cashInData.InvoiceNumber, nil, nil, nil); errRes != nil {
			logger.Error(ctx, errRes.Error())
		}

		return
	}

	//get history
	cashInHistory, err := s.cashinRepo.GetCashinTransactionHistoryByPaymentStatus(ctx, cashInData.ID, cashInData.PaymentStatus)
	if err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	logger.Info(ctx, fmt.Sprintf("cash in with inv %s history status %+v", cashInData.InvoiceNumber, cashInHistory.Status))

	switch true {
	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderXfers):
		var resp entity.RetryCallbackXfers
		go s.CallbackXfersCashinTranscationProcess(ctx, xfers.PaymentResponse{
			Data: xfers.PaymentResponseData{
				ID: resp.Data.ID,
				PaymentDataResp: xfers.PaymentDataAtributsResp{
					ReferenceId: referenceId,
				},
			},
		})

	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderXendit):
		var (
			paymentId string
		)
		switch cashInData.PaymentChannelTypePaymentType {
		case constants.TrxVirtualAccount:
			var vaResponse entity.RetryCallbackXenditVA
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &vaResponse)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			paymentId = vaResponse.CallbackVirtualAccountID
			if paymentId == "" {
				paymentId = vaResponse.ID
			}

		case constants.TrxEwallet:
			var resp entity.RetryCallbackXenditEwallet
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &resp)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			paymentId = resp.Data.ID
			if paymentId == "" {
				paymentId = resp.ID
			}
		case constants.TrxCc:
			var resp entity.RetryCallbackXenditCC
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &resp)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			paymentId = resp.ID
		case constants.TrxRetail:
			var resp entity.RetryCallbackXenditPaymentFixedCode
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &resp)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			paymentId = resp.FixedPaymentCodeID
			if paymentId == "" {
				paymentId = resp.ID
			}
		}
		go s.CallbackXenditCashinTranscationProcess(ctx, referenceId, paymentId, cashInData.PaymentChannelTypePaymentType, nil)

	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderOttocash):
		var response entity.RetryCallbackOttocash
		err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &response)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		paymentId := response.ReferenceNumber
		reqByte, _ := json.Marshal(response)
		go s.CallbackOttocashCashinTranscationProcess(ctx, referenceId, paymentId, response.ResponseCode, reqByte)

	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderNicePay):
		// nicepay saved resp inquiry in history so we need to convert to the callback payload
		var req entity.RetryCallbackNicepay

		err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &req)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		reqNotif := nicepay.NotificationRequest{
			TXid:        req.TXid,
			ReferenceNo: req.ReferenceNo,
			Amt:         str.ToFloat64(req.Amt),
		}
		go s.CallbackNicePayCashInTransactionProcess(ctx, reqNotif)

	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderDoku):
		var (
			paymentId string
			reqByte   []byte
		)

		switch cashInData.PaymentChannelTypePaymentType {
		case constants.TrxVirtualAccount:
			var resp entity.RetryCallbackOttocashDokuVA
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &resp)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			paymentId = ""
			reqByte, _ = json.Marshal(resp)
		}

		go s.CallbackDokuCashinTranscationProcess(ctx, referenceId, paymentId, cashInData.PaymentChannelTypePaymentType, reqByte)

	case strings.Contains(strings.ToLower(cashInData.PaymentProviderName), constants.ProviderBankBCA):

		switch cashInData.PaymentChannelTypePaymentType {
		case constants.TrxSNAP:
			var cbData domain.SnapCbCashInTransaction
			err = json.Unmarshal([]byte(cashInHistory.PgReferenceInformation), &cbData)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			go s.CallbackBankBcaSnapCashinTranscationProcess(ctx, cbData)
		}

	default:
		err = errors.SetErrorMessage(http.StatusNotFound, "payment provider is not yet in service")
		return err
	}

	return nil
}
