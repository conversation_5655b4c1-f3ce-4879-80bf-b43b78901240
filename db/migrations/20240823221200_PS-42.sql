-- request log table
CREATE TABLE trx_request_logs (
    id UUID PRIMARY KEY NOT NULL,
    resource_type TEXT, -- cash_in / cash_out
    resource_id TEXT, -- invoice number / cashout batch number
    correlation_id TEXT, -- log correlation id
    request_headers TEXT, -- from client
    request_method TEXT, -- from client
    request_path TEXT, -- from client
    request_query TEXT, -- from client
    request_body TEXT, -- from client
    response_status TEXT, -- 200 / 400
    response_body TEXT, -- dari server
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

