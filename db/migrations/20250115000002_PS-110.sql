-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS webhook_delivery_logs_id_seq;

-- Table Definition
CREATE TABLE "public"."webhook_delivery_logs" (
    "id" int8 NOT NULL DEFAULT nextval('webhook_delivery_logs_id_seq'::regclass),
    "parent_id" int8,
    "company_id" int8 NOT NULL,
    "event_type" varchar(255) NOT NULL,
    "invoice_number" varchar(255),
    "endpoint" text NOT NULL,
    "payload" text NOT NULL,
    "attempt" int4 NOT NULL,
    "status" varchar(50) NOT NULL,
    "response_code" int4,
    "response_body" text,
    "error_message" text,
    "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "fk_company" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE CASCADE,
    CONSTRAINT "fk_parent" FOREIGN KEY ("parent_id") REFERENCES "public"."webhook_delivery_logs"("id") ON DELETE SET NULL,
    PRIMARY KEY ("id")
);


-- Indices
CREATE INDEX idx_webhook_delivery_logs_parent_id ON public.webhook_delivery_logs USING btree (parent_id)
CREATE INDEX idx_webhook_delivery_logs_company_id ON public.webhook_delivery_logs USING btree (company_id)
CREATE INDEX idx_webhook_delivery_logs_event_type ON public.webhook_delivery_logs USING btree (event_type)
CREATE INDEX idx_webhook_delivery_logs_invoice_number ON public.webhook_delivery_logs USING btree (invoice_number)
CREATE INDEX idx_webhook_delivery_logs_status ON public.webhook_delivery_logs USING btree (status)
CREATE INDEX idx_webhook_delivery_logs_created_at ON public.webhook_delivery_logs USING btree (created_at)
CREATE INDEX idx_webhook_delivery_logs_updated_at ON public.webhook_delivery_logs USING btree (updated_at)