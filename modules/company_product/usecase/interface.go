package usecase

import (
	"context"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_product/entity"
	"repo.nusatek.id/nusatek/payment/utils"
)

type CompanyProductRepo interface {
	CreateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error)
	UpdateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error)
	GetCompanyProductById(ctx context.Context, id int) (resp *domain.CompanyProducts, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.CompanyProducts, err error)
	DeleteCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error)
	SearchCompanyProduct(ctx context.Context, paginate utils.Pagination) (resp *[]entity.CompanyProductShow, totalData int64, err error)
	GetCompanyProductListAll(ctx context.Context) (resp *[]entity.CompanyProductShow, totalData int64, err error)
	GetCompanyProductByCode(ctx context.Context, code string) (resp *domain.CompanyProducts, err error)
}

type CompanyProductUsecase interface {
	CreateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error)
	GetCompanyProductById(ctx context.Context, id string) (resp *domain.CompanyProducts, err error)
	SelectByIds(ctx context.Context, ids []int) (resp []domain.CompanyProducts, err error)
	UpdateCompanyProduct(ctx context.Context, req *domain.CompanyProducts) (err error)
	DeleteCompanyProduct(ctx context.Context, id int) (err error)
	GetCompanyProductByCode(ctx context.Context, code string) (resp *domain.CompanyProducts, err error)
	SearchCompanyProduct(ctx context.Context, paginate utils.Pagination) (resp *[]entity.CompanyProductShow, totalData int64, err error)
	GetCompanyProductListAll(ctx context.Context) (resp *[]entity.CompanyProductShow, totalData int64, err error)
	GetCompanyProductByIdAndCompanyId(ctx context.Context, companyProductId, companyId string) (resp *domain.CompanyProducts, err error)
	GetCompanyProductByCodeAndCompanyId(ctx context.Context, productCode, companyId string) (resp *domain.CompanyProducts, err error)
	UpdateCompanyProductStatus(ctx context.Context, id string, status bool) (err error)

	// Open API
	CreateCompanyProductAPI(ctx context.Context, req *domain.CompanyProducts, auth string) (err error)
	UpdateCompanyProductAPI(ctx context.Context, id, auth string, req *domain.CompanyProducts) (err error)
	ListCompanyProductAPI(ctx context.Context, paginate utils.Pagination, auth string) (resp *[]entity.CompanyProductShow, totalData int64, err error)
	GetCompanyProductCodeAPI(ctx context.Context, auth, code string) (resp *entity.CompanyProductShow, err error)
	DeleteCompanyProductAPI(ctx context.Context, id, auth string) (err error)
}

