package routers

import (
	mb "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (h *rooters) SetupSubcriber() {
	mb.ConsumeMessage(constants.CallbackCashInXfersVa, h.cashinHandler.ListenCallbackXfers)
	mb.ConsumeMessage(constants.CallbackCashInNicePay, h.cashinHandler.ListenerCallbackNicePay)
	mb.ConsumeMessage(constants.CallbackCashInSnapNicePay, h.cashinHandler.ListenerCallbackSnapNicePay)
	mb.ConsumeMessage(constants.CallbackCashInXenditVa, h.cashinHandler.ListenerCallbackXenditVa)
	mb.ConsumeMessage(constants.CallbackCashInXenditEwalet, h.cashinHandler.ListenerCallbackXenditEwallet)
	mb.ConsumeMessage(constants.CallbackCashInXenditInvoice, h.cashinHandler.ListenerCallbackXenditInvoice)
	mb.ConsumeMessage(constants.CallbackCashInXenditFixedPaymentCode, h.cashinHandler.ListenerCallbackXenditFixedPaymentCode)
	mb.ConsumeMessage(constants.CallbackCashOutPaymentXfers, h.cashOutHandler.ListenCallbackXfers)
	mb.ConsumeMessage(constants.CallbackCashoutPaymentXendit, h.cashOutHandler.ListenCallbackXendit)
	mb.ConsumeMessage(constants.CallbackCashInOttocashEwallet, h.cashinHandler.ListenerCallbackOttocash)
	mb.ConsumeMessage(constants.CallbackCashInDokuVA, h.cashinHandler.ListenerCallbackDokuVa)
	mb.ConsumeMessage(constants.CallbackCashInCashlezOfflinePg, h.cashinHandler.ListenerCallbackCashlezOfflinePg)
	mb.ConsumeMessage(constants.CallbackCashInBankBcaSnap, h.cashinHandler.ListenerCallbackBankBcaSnap)

	mb.ConsumeMessage(constants.CallBackCashInAll, h.webhookHandler.WebhookCashInListener)
	mb.ConsumeMessage(constants.CallBackCashOutAll, h.webhookHandler.WebhookCashOutListener)
}
