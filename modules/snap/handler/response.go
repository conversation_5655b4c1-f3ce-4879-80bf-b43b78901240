package handler

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"repo.nusatek.id/nusatek/payment/domain"
)

// error resp with no data
func (h *Handler) sendErr(c *fiber.Ctx, err error) error {
	status := http.StatusInternalServerError

	if base, ok := err.(domain.ISnapBaseResp); ok {
		status = base.GetBaseResp().HttpCode
		return c.Status(status).JSON(base)
	}

	return c.Status(status).JSON(err)
}

// send either success or error with data
func (h *Handler) send(c *fiber.Ctx, br domain.ISnapBaseResp) error {
	status := br.GetBaseResp().HttpCode

	return c.Status(status).JSON(br)
}
