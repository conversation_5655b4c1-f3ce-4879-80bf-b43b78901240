package nicepay

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	rest "repo.nusatek.id/moaja/backend/libraries/rest-utils"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

type wrapper struct {
	endpoint    string
	isSandbox   bool
	callbackUrl string
	client      rest.RestClient
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Setup() RestNicePay {
	var (
		EndpointSandbox = config.GetString("nicepay.sandbox")
		Endpoint        = config.GetString("nicepay.host")
	)

	w.isSandbox = config.GetBool("nicepay.is_sandbox")
	if w.isSandbox {
		w.endpoint = EndpointSandbox
	} else {
		w.endpoint = Endpoint
	}
	w.callbackUrl = config.GetString("nicepay.callback_url")
	restOptions := rest.Options{
		Address:   w.endpoint,
		Timeout:   config.GetDuration("nicepay.timeout"),
		SkipTLS:   config.GetBool("nicepay.skiptls"),
		DebugMode: config.GetBool("nicepay.debug"),
	}

	if config.GetString("nicepay.proxy") != "" {
		restOptions.WithProxy = true
		restOptions.ProxyAddress = config.GetString("nicepay.proxy")
	}

	w.client = rest.New(restOptions)

	return w
}

func GetMerchantToken(timestamp string, credential Credential, referenceNo, amount string) string {
	var textToken = timestamp + credential.MerchantID + referenceNo + amount + credential.MerchantKey
	fmt.Println("Merchant token value ", timestamp, credential.MerchantID, referenceNo, amount, credential.MerchantKey)
	sha256Res := sha256.Sum256([]byte(textToken))
	return hex.EncodeToString(sha256Res[:])
}

func (w *wrapper) Registration(ctx context.Context, req *RegistrationRequest, credential Credential) (resp RegistrationResponse, err error) {
	path := "/direct/v2/registration"
	logger.Info(ctx, "request", logger.String("url", w.endpoint+path), logger.Any("req", req))
	req.DbProcessURL = w.callbackUrl
	body, status, err := w.client.Post(ctx, path, http.Header{}, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error post", logger.Err(err))
		return
	}

	if status != http.StatusOK {
		// var datas ErrorResponse
		// _ = json.Unmarshal(body, &datas)
		// if datas.Message != "" {
		// 	logger.Error(ctx, "[Response]", w.Endpoint+path, datas)
		// 	err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
		// 	return
		// }

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	if resp.ResultCd != ResultCdSuccess {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("request failed to provider payment: %s", resp.ResultMsg))
		logger.Error(ctx, "result not success", logger.Any("resp", resp), logger.Err(err))
		return
	}

	logger.Info(ctx, "success", logger.Any("resp", resp))
	return
}

func (w *wrapper) Inquiry(ctx context.Context, req *InquiryRequest, credential Credential) (resp InquiryResponse, err error) {
	path := "/direct/v2/inquiry"
	logger.Info(ctx, "request", logger.Any("url", w.endpoint+path), logger.Any("req", req))

	body, status, err := w.client.Post(ctx, path, http.Header{}, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error post", logger.Err(err))
		return
	}

	if status != http.StatusOK {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	if resp.ResultCd != ResultCdSuccess {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("request failed to provider payment: %s", resp.ResultMsg))
		logger.Error(ctx, "result not success", logger.Any("resp", resp), logger.Err(err))
		return
	}

	logger.Info(ctx, "success", logger.Any("resp", resp))
	return
}

func (w *wrapper) Cancel(ctx context.Context, req *CancelRequest, credential Credential) (resp CancelResponse, err error) {
	path := "/direct/v2/cancel"
	logger.Info(ctx, "request", logger.Any("url", w.endpoint+path), logger.Any("req", req))

	body, status, err := w.client.Post(ctx, path, http.Header{}, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error post", logger.Err(err))
		return
	}

	if status != http.StatusOK {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "unmarshal error", logger.String("resp", string(body)), logger.Err(err))
		return
	}

	if resp.ResultCd != ResultCdSuccess {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("request failed to provider payment: %s", resp.ResultMsg))
		logger.Error(ctx, "result not success", logger.Any("resp", resp), logger.Err(err))
		return
	}

	logger.Info(ctx, "success", logger.Any("resp", resp))
	return
}
