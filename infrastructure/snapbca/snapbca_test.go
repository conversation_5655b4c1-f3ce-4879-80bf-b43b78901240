package snapbca

import (
	"context"
	"encoding/base64"
	"log"
	"net/url"
	"testing"

	"github.com/go-resty/resty/v2"
	dbConfig "repo.nusatek.id/moaja/backend/libraries/database-handler/config"
	"repo.nusatek.id/moaja/backend/libraries/database-handler/redis"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/nusatek/payment/utils/config"
)

var (
	// clientID = `Nusantara_C1i3nt_ID_A56B52AF24435`
	clientID     = `acbbcdd8-d312-426b-8d5a-e196f41e9eeb`
	clientSecret = "7c8c272b-f23a-4bd7-a211-4495e732ad41"
	privateKey   = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	privateKeyBase64 = base64.StdEncoding.EncodeToString([]byte(privateKey))

	testCred = Credential{
		ClientID:         clientID,
		ClientSecret:     clientSecret,
		PrivateKeyBase64: privateKeyBase64,
	}
)

func TestUrlHttp(t *testing.T) {
	requrl, err := url.Parse("https://devapi.klikbca.com/openapi/v1.0/transfer-va/status?testing=test1,2,3")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(requrl.Path + requrl.Query().Encode())
	t.Log(requrl.Query().Encode())
}

func TestDoAuth(t *testing.T) {
	config.SetupWithPath("../../config.json")
	logger.Init("TestDoAuth")
	var err error
	ctx := context.TODO()
	c := NewWrapper()
	r := resty.New()
	r.Debug = true
	c.Endpoint = "https://devapi.klikbca.com"
	c.client = r
	resp, err := c.AccessTokenB2B(ctx, AccessTokenB2BReq{
		GrantType: "client_credentials",
	}, testCred)
	if err != nil {
		log.Fatal("err", err)
	}
	log.Println(resp)
}

func TestDoHttp(t *testing.T) {
	ctx := context.TODO()
	config.SetupWithPath("../../config.json")
	logger.Init("TestDoHttp")
	// var err error
	redisClient := redis.NewRedis(ctx, &dbConfig.DBConfig{
		Host: config.GetString("redis.host"),
		Port: config.GetInt("redis.port"),
		User: config.GetString("redis.user"),
		Pass: config.GetString("redis.password"),
	})
	c := NewWrapper()
	r := resty.New()
	r = r.SetTimeout(config.GetDuration("snap_bca.timeout")).SetDebug(config.GetBool("snap_bca.debug"))
	r.Debug = true
	c.Endpoint = "https://devapi.klikbca.com"
	c.client = r
	c = c.WithRedis(redisClient)

	custNo := "************"
	reqIdStr := "202402131100481068700045265257"
	partnerId := "10687"
	// va := partnerId + custNo
	req := TransferVaStatusReq{
		// PartnerServiceID: partnerId,
		// CustomerNo:       custNo,
		// VirtualAccountNo: va,
		// InquiryRequestID: reqIdStr,
		// PaymentRequestID: reqIdStr,
	}
	req.Build(partnerId, custNo, reqIdStr)
	resp, _ := c.TransferVaStatus(ctx, req, testCred)
	t.Log(resp.VirtualAccountData)
}

func TestGetCredential(t *testing.T) {
	ctx := context.TODO()
	config.SetupWithPath("../../config.json")
	logger.Init("TestGetCredential")

	secret := "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"
	cred, err := GetCredential(secret)
	if err != nil {
		t.Fatalf("error get credential %v", err)
	}
	t.Logf("cred %+v", cred)
	// t.Logf("cred %+v", cred.PrivateKey())

	// var err error
	redisClient := redis.NewRedis(ctx, &dbConfig.DBConfig{
		Host: config.GetString("redis.host"),
		Port: config.GetInt("redis.port"),
		User: config.GetString("redis.user"),
		Pass: config.GetString("redis.password"),
	})
	c := NewWrapper()
	r := resty.New()
	r = r.SetTimeout(config.GetDuration("snap_bca.timeout")).SetDebug(config.GetBool("snap_bca.debug"))
	r.Debug = true
	c.Endpoint = "https://devapi.klikbca.com"
	c.client = r
	c = c.WithRedis(redisClient)

	custNo := "************"
	reqIdStr := "202402151110491068700045269268"
	partnerId := "10687"
	// va := partnerId + custNo
	req := TransferVaStatusReq{}
	req.Build(partnerId, custNo, reqIdStr)
	resp, _ := c.TransferVaStatus(ctx, req, cred)
	t.Log(resp.VirtualAccountData)
}
