package domain

import (
	"time"

	"gorm.io/gorm"
)

type PaymentProviderChannelMappings struct {
	ID                  int64          `json:"id"`
	PaymentProviderID   int64          `json:"payment_provider_id" validate:"required"`
	PaymentChannelID    int64          `json:"payment_channel_id" validate:"required"`
	Code                string         `json:"code" validate:"min=3,alphanum"`
	MaxTransaction      float64        `json:"max_transaction" validate:"required"`
	Sla                 int            `json:"sla" validate:"required"`
	Capability          int            `json:"capability" validate:"eq=1|eq=2"`
	ProviderChannelCode string         `json:"provider_channel_code" validate:"required"`
	PaymentInstructions string         `json:"payment_instructions"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type PaymentProviderChannelMappingsComplete struct {
	PaymentProviderChannelMappings PaymentProviderChannelMappings
	PaymentChannelTypes            PaymentChannelTypes
}
