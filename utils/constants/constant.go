package constants

const (
	HEADER_TID = "tid"
)

// Tofic Message Broker
const (
	LogsPaymentService                   = "payment.service.log"
	LogsErrorPaymentService              = "payment.service.error.log"
	CallbackCashInXfersVa                = "payment.service.callback.xfers.virtual"
	CallbackCashInNicePay                = "payment.service.callback.nicepay.*"
	CallbackCashInSnapNicePay            = "payment.service.callback.snap.nicepay.*"
	CallbackCashOutPaymentXfers          = "payment.service.callback.xfers.cashout"
	CallbackCashoutPaymentXendit         = "payment.service.callback.xendit.cashout"
	CallbackCashInXenditVa               = "payment.service.callback.xendit.virtual"
	CallbackCashInXenditEwalet           = "payment.service.callback.xendit.ewallet"
	CallbackCashInXenditInvoice          = "payment.service.callback.xendit.invoice"
	CallbackCashInXenditFixedPaymentCode = "payment.service.callback.xendit.fixed_payment_code"
	CallbackCashInOttocashEwallet        = "payment.service.callback.ottocash.ewallet"
	CallbackCashInDokuVA                 = "payment.service.callback.doku.virtual_account"
	CallbackCashInCashlezOfflinePg       = "payment.service.callback.cashlez.offline_pg"
	CallbackCashInBankBcaSnap            = "payment.service.callback.bank_bca.snap"
	CallbackClient                       = "payment.service.callback.cashin.*"
	CallbackCashOutClient                = "payment.service.callback.cashout.*"
	CallBackCashInAll                    = "payment.service.callback.cashin.#"
	CallBackCashOutAll                   = "payment.service.callback.cashout.#"
)

// Response
const (
	ResponseSuccess                 = "Ok"
	ResponseGeneralError            = "General Error"
	ResponseBadRequest              = "Invalid Request"
	ResponseUnauthorized            = "Request requires authorization"
	ResponseInternalServerError     = "An error occurred on the server"
	ResponseInvalidTransaction      = "Transaction not recognized"
	ResponseInvalidAmount           = "Invalid amount"
	ResponseInvalidCardNumber       = "Card number not recognized"
	ResponseTimeout                 = "Timeout response from payment provider"
	ResponseTransactionNotPermitted = "Transaction is not permitted"
	ResponseInsufficientFund        = "Inusfficient funds"
	ResponseBankNotSupport          = "This bank cannot be used"
	ResponseDuplicateTransaction    = "Duplicate transaction"
	ResponseInvalidSecret           = "Invalid Secret Transction"
	ResponseUserNotFound            = "User not found"
	ResponseServiceDown             = "Service is down"
	ResponsePaymenProviderNotFound  = "Payment provider not found"
	ResponsePaymentChannelNotFound  = "Payment channel not found"
	ResponseBranchNotFound          = "Branch not found"
	ResponsePartnerNotFound         = "Partner not found"
	ResponseProductNotFound         = "Product not found"
)

// key redis
const (
	CacheCompany                       = "company"
	CacheCompanySecrets                = "company-secrets"
	CachePaymentProvider               = "payment-provider"
	CachePaymentChannel                = "payment-channel"
	CachePaymentChannelType            = "payment-channel-type"
	CacheCompanyProductCode            = "company-product-code"
	CacheCompanyProductId              = "company-product-id"
	CachePartner                       = "partners"
	CacheTags                          = "tags"
	CachePartnerTags                   = "partner-tags"
	CacheUserMenu                      = "user-menu"
	CacheUser                          = "user"
	CacheUserCompany                   = "user-company"
	CacheCashOutGenerateQR             = "cash-out-generate-qr"
	CacheCashInTransaction             = "cash-in-transaction"
	CacheCashOutTransaction            = "cash-out-transaction"
	CacheCompanyPaymentProviderMapping = "company-payment-provider-mapping"
	CacheFile                          = "file"
)

// cabability
const (
	CashInCapability  = 1
	CashOutCapability = 2
)

// TransactionType
const (
	TrxVirtualAccount = "virtual_bank_account"
	TrxCc             = "cc"
	TrxRetail         = "retail"
	TrxEwallet        = "ewallet"
	TrxOfflinePG      = "offline_pg"
	TrxSNAP           = "snap"
)
