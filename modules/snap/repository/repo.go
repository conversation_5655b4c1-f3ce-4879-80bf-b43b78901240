package repository

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/company_mapping/entity"
	"repo.nusatek.id/nusatek/payment/modules/snap/usecase"
)

type repo struct {
	db        *gorm.DB
	fnWrapper usecase.RepoFuncWrapper
}

func NewRepository(db *gorm.DB, fnWrapper usecase.RepoFuncWrapper) usecase.Repository {
	return &repo{db, fnWrapper}
}

func (r repo) FnWrapper() usecase.RepoFuncWrapper {
	return r.fnWrapper
}

func (r repo) GetOneClient(ctx context.Context, clientId string) (res domain.SnapClient, err error) {
	db := r.db.WithContext(ctx)
	err = db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", clientId).Take(&res).Error

	return
}

func (r repo) GetOneCompanyProductProviderChannelMappingBySnapCompCode(ctx context.Context, snapCompCode string) (res domain.CompanyPaymentProviderChannelMappings, err error) {
	db := r.db.WithContext(ctx)
	// TODO: save snap comp code on different column and give index for it
	// save when edit config on company provider
	err = db.WithContext(ctx).Where("channel_type_configs->>'comp_code' = ?", snapCompCode).Take(&res).Error

	return
}

func (r repo) GetOneCustomerVaByNumber(ctx context.Context, mappingId int64, vaNumber string) (res domain.FixedVACustomer, err error) {
	db := r.db.WithContext(ctx)
	gormData := entity.CustomerFVAGORM{}

	err = db.WithContext(ctx).Where("company_payment_provider_channel_mapping_id = ?", mappingId).
		Where("va_number = ?", vaNumber).Take(&gormData).Error
	res = gormData.ToDomain()
	return
}

// TODO: need to mapping customer fixed va , with cash in transaction
// func (s repo) GetOneCashInByVa(ctx context.Context, va string) (res *domain.CashInTransactions, err error) {
// 	hist, err := s.fnWrapper.GetLastCashinHistoryByVaAndStatus(ctx, va, "pending")
// 	if err != nil {
// 		log.Error(ctx, fmt.Sprintf("cash in history not found %v", err))
// 		return
// 	}

// 	res, err = s.fnWrapper.GetCashinTransactionById(ctx, fmt.Sprintf("%d", hist.CashInTransactionID))
// 	if err != nil {
// 		log.Error(ctx, fmt.Sprintf("cash in not found %v", err))
// 		return
// 	}

// 	return
// }

func (r repo) InsertVaRequestId(ctx context.Context, req *domain.SnapVaRequestId) (err error) {
	db := r.db.WithContext(ctx)
	return db.Clauses(clause.Returning{}).Create(req).Error
}

func (r repo) GetLastVaRequestId(ctx context.Context, req domain.SnapVaRequestId) (res domain.SnapVaRequestId, err error) {
	db := r.db.WithContext(ctx)
	if req.CashInTransactionId != 0 {
		db = r.db.Where("cash_in_transaction_id = ?", req.CashInTransactionId)
	}
	if len(req.ClientId) != 0 {
		db = r.db.Where("client_id = ?", req.ClientId)
	}
	if len(req.VaNumber) != 0 {
		db = r.db.Where("va_number = ?", req.VaNumber)
	}
	err = db.Order("id desc").Take(&res).Error
	return
}
