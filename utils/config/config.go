package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

var v *viper.Viper

func New() {
	v = viper.New()
}

func Setup() {
	fmt.Println("Checking Config ... ")

	v = viper.New()

	path := "./config.json"

	v.SetConfigType("json")
	v.SetConfigFile(path)

	if err := v.ReadInConfig(); err != nil {
		panic(err)
	}
}

func SetupWithPath(path string) {
	fmt.Println("Checking Config ... ")

	v = viper.New()

	v.SetConfigType("json")
	v.SetConfigFile(path)

	if err := v.ReadInConfig(); err != nil {
		panic(err)
	}
}

func GetString(key string) string { return v.GetString(key) }

func GetBool(key string) bool { return v.GetBool(key) }

func GetInt(key string) int { return v.GetInt(key) }

func GetDuration(key string) time.Duration { return v.GetDuration(key) }

func Set(key string, value interface{}) { v.Set(key, value) }

func GetStringSlice(key string) []string {
	return v.GetStringSlice(key)
}

func GetStringMapString(key string) map[string]string {
	return v.GetStringMapString(key)
}

func UnmarshalKey(key string, rawVal interface{}, opts ...viper.DecoderConfigOption) error {
	return v.UnmarshalKey(key, rawVal, opts...)
}
